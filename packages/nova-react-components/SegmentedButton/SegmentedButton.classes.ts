import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface SegmentedButtonClasses {
  /** Class name applied to the root element. */
  root: string;

  /** Class name applied to the SegmentedButton if `selected={true}`. */
  selected: string;
}

export type SegmentedButtonClassKey = keyof SegmentedButtonClasses;

export function getSegmentedButtonUtilityClass(slot: string): string {
  return generateUtilityClass('NovaSegmentedButton', slot, 'Nova');
}

const segmentedButtonClasses: SegmentedButtonClasses = generateUtilityClasses(
  'NovaSegmentedButton',
  ['root', 'selected'],
  'Nova',
);

export default segmentedButtonClasses;
