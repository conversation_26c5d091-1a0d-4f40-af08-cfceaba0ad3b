import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface BoxClasses {
  /** Class name applied to the root element. */
  root: string;
}

export type BoxClassKey = keyof BoxClasses;

export function getBoxUtilityClass(slot: string): string {
  return generateUtilityClass('NovaBox', slot, 'Nova');
}

const boxClasses: BoxClasses = generateUtilityClasses('NovaBox', ['root'], 'Nova');

export default boxClasses;
