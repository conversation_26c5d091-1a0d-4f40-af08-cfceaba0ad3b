import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotCommonProps } from '../types/slot';

export interface BoxTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & object;
  defaultComponent: D;
}

export type BoxProps<D extends React.ElementType = BoxTypeMap['defaultComponent'], P = SlotCommonProps> = OverrideProps<
  BoxTypeMap<P, D>,
  D
>;

export interface BoxOwnerState extends BoxProps {}
