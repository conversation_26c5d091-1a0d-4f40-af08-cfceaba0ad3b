import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface FabClasses {
  /** Class name applied to the root element. */
  root: string;
}

export type FabClassKey = keyof FabClasses;

export function getFabUtilityClass(slot: string): string {
  return generateUtilityClass('NovaFab', slot, 'Nova');
}

const fabClasses: FabClasses = generateUtilityClasses('NovaFab', ['root'], 'Nova');

export default fabClasses;
