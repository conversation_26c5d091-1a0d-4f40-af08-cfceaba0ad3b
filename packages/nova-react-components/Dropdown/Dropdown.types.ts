import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../types/slot';
import { SelectValue } from '../internal/hooks/useSelect';
import { SelectOption } from '../internal/hooks/useOption';
import { MenuProps } from '../Menu';

export interface DropdownSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the button.
   * @default 'button'
   */
  button?: React.ElementType;
  /**
   * The component that renders the start decorator.
   * @default 'span'
   */
  startDecorator?: React.ElementType;
  /**
   * The component that renders the end decorator.
   * @default 'span'
   */
  endDecorator?: React.ElementType;
  /**
   * The component that renders the indicator.
   * @default 'span'
   */
  indicator?: React.ElementType;
  /**
   * The component that renders the listbox.
   * @default 'ul'
   */
  listbox?: React.ElementType;
  /**
   * The component that renders the error state.
   * @default 'span'
   */
  errorStateIcon?: React.ElementType;
}

export type DropdownSlotsAndSlotProps<Multiple extends boolean> = CreateSlotsAndSlotProps<
  DropdownSlots,
  {
    root: SlotProps<'div', object, DropdownOwnerState<any, Multiple>>;
    button: SlotProps<'button', object, DropdownOwnerState<any, Multiple>>;
    startDecorator: SlotProps<'span', object, DropdownOwnerState<any, Multiple>>;
    endDecorator: SlotProps<'span', object, DropdownOwnerState<any, Multiple>>;
    indicator: SlotProps<'span', object, DropdownOwnerState<any, Multiple>>;
    listbox: SlotProps<
      'ul',
      Omit<MenuProps, 'slots' | 'slotProps' | 'open' | 'onClose' | 'onItemsChange'>,
      DropdownOwnerState<any, Multiple>
    >;
    errorStateIcon: SlotProps<'span', object, DropdownOwnerState<any, Multiple>>;
  }
>;

export interface DropdownTypeMap<
  Value,
  Multiple extends boolean = false,
  P = object,
  D extends React.ElementType = 'div',
> {
  props: P & {
    /**
     * A ref for imperative actions. It currently only supports `focusVisible()` action.
     */
    action?: React.Ref<{
      focusVisible(): void;
    }>;
    /**
     * Name of the dropdown.
     */
    name?: string;
    /**
     * Text to show when there is no selected value.
     */
    placeholder?: React.ReactNode;
    /**
     * If `true`, the select value cannot be empty when submitting form.
     * @default false
     */
    required?: boolean;
    /**
     * If `true`, the dropdown is disabled.
     * @default false
     */
    disabled?: boolean;
    /**
     * If `true`, the dropdown is under error state.
     * @default false
     */
    error?: boolean;
    /**
     * If `true`, the dropdown is under readOnly state.
     * @default false
     */
    readOnly?: boolean;
    /**
     * If `true`, the dropdown is focused during the first mount
     * @default false
     */
    autoFocus?: boolean;
    /**
     * If `true`, the dropdown will take up the full width of its container.
     * @default false
     */
    fullWidth?: boolean;
    /**
     * Leading adornment for the dropdown select.
     */
    startDecorator?: React.ReactNode;
    /**
     * Trailing adornment for the dropdown select.
     */
    endDecorator?: React.ReactNode;
    /**
     * The end indicator for the dropdown select.
     */
    indicator?: React.ReactNode | ((open: boolean) => React.ReactNode);
    /**
     * The size of the dropdown.
     * @default 'medium'
     */
    size?: 'small' | 'medium' | 'large';
    /**
     * If `true`, `value` must be an array and the menu will support multiple selections.
     * @default false
     */
    multiple?: Multiple;
    /**
     * A function to convert the currently selected value to a string.
     * Used to set a value of a hidden input associated with the select,
     * so that the selected value can be posted with a form.
     */
    getSerializedValue?: (
      option: SelectValue<SelectOption<Value>, Multiple>,
    ) => React.InputHTMLAttributes<HTMLInputElement>['value'];
    /**
     * The default selected value. Use when the component is not controlled.
     */
    defaultValue?: SelectValue<Value, Multiple>;
    /**
     * The selected value. Use when the component is controlled.
     */
    value?: SelectValue<Value, Multiple>;
    /**
     * Function that renders the selected value.
     */
    renderValue?: (option: SelectValue<SelectOption<Value>, Multiple>) => React.ReactNode;
    /**
     * Callback fired when an option is selected.
     */
    onChange?: (
      event: React.MouseEvent | React.KeyboardEvent | React.FocusEvent | null,
      value: SelectValue<Value, Multiple>,
    ) => void;
    /**
     * `id` attribute of the listbox element.
     * Also used to derive the `id` attributes of options.
     */
    listboxId?: string;
    /**
     * If `true`, the dropdown will be initially open.
     * @default false
     */
    defaultListboxOpen?: boolean;
    /**
     * Controls the open state of the select's listbox.
     * @default undefined
     */
    listboxOpen?: boolean;
    /**
     * Callback fired when the component requests to be opened.
     * Use in controlled mode (see listboxOpen).
     */
    onListboxOpenChange?: (isOpen: boolean) => void;
    /**
     * Triggered when focus leaves the menu and the menu should close.
     */
    onClose?: () => void;
    /**
     * Determines whether to display an error state icon at the end of `Dropdown` component.
     * This option only takes effect if the `error` property is set to `true`.
     * @default true
     */
    showErrorIcon?: boolean;
  } & DropdownSlotsAndSlotProps<Multiple>;
  defaultComponent: D;
}

export type DropdownProps<
  Value,
  Multiple extends boolean,
  D extends React.ElementType = DropdownTypeMap<Value, Multiple>['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<DropdownTypeMap<Value, Multiple, P, D>, D>;

export interface DropdownOwnerState<Value, Multiple extends boolean> extends DropdownProps<Value, Multiple> {
  /**
   * If `true`, the select button's focus is visible.
   */
  focusVisible?: boolean;
  /**
   * If `true`, the select dropdown is open.
   */
  open: boolean;
  /**
   * If `true`, the select button is active.
   */
  active: boolean;
  /**
   * If `true`, the select value is filled.
   */
  filled: boolean;
}
