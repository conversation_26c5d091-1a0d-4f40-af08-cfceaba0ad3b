import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import React, { useState } from 'react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Dropdown } from './Dropdown';
import { Option } from '../Option';
import { FormControl } from '../FormControl';
import { FormLabel } from '../FormLabel';
import { FormHelperText } from '../FormHelperText';
import { Checkbox } from '../Checkbox';
import { ListItemContent } from '../ListItemContent';
import { ListItemDecorator } from '../ListItemDecorator';
import { Button } from '../Button';
import { Divider } from '../Divider';

const generateOptions = (count: number) => {
  const result: any[] = [];
  for (let i = 0; i < count; i++) {
    result.push({
      label: `Option ${i + 1}`,
      value: `option${i + 1}`,
    });
  }
  return result;
};
const allOptions = generateOptions(20);
export default function Example() {
  const [open, setOpen] = useState<boolean>(false);
  const [options, setOptions] = useState<string[]>([]);
  const handleOptionChange = (event: React.SyntheticEvent | null, values: string[]) => {
    setOptions(values);
  };
  const onApply = () => {
    setOpen(false);
  };
  const onClear = () => {
    setOptions([]);
    setOpen(false);
  };
  return (
    <Dropdown
      multiple
      value={options}
      onChange={handleOptionChange}
      placeholder="Select"
      style={{ width: '400px' }}
      renderValue={(items) => (items.length > 1 ? `${items.length} options selected` : items[0]?.label)}
      listboxOpen={open}
      onListboxOpenChange={(isOpen) => {
        setOpen(isOpen);
      }}
      onClose={onApply}
    >
      <div style={{ maxHeight: '300px', overflow: 'auto' }}>
        {allOptions.map((i) => (
          <Option key={i.value} value={i.value}>
            <ListItemDecorator>
              <Checkbox checked={options.includes(i.value)} slotProps={{ input: { 'data-testid': 'checkbox' } }} />
            </ListItemDecorator>
            <ListItemContent primary={i.label} />
          </Option>
        ))}
      </div>
      <Divider />
      <div style={{ padding: '8px 16px', display: 'flex', justifyContent: 'end', gap: '8px' }}>
        <Button variant="text" onClick={onClear} data-testid="clear">
          Clear
        </Button>
        <Button onClick={onApply} data-testid="apply">
          Apply
        </Button>
      </div>
    </Dropdown>
  );
}

afterEach(() => {
  cleanup();
});

describe('Dropdown', () => {
  it('should render normal', () => {
    render(<Dropdown data-testid="NovaDropdown-root" />);
    expect(screen.getByTestId('NovaDropdown-root')).toHaveClass('NovaDropdown-root');
    expect(screen.getByTestId('NovaDropdown-root')).toHaveClass('NovaDropdown-sizeMedium');
  });

  it('should render normal', () => {
    render(
      <Dropdown placeholder="Select" style={{ width: '200px' }}>
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>,
    );
    expect(screen.getByText('Select')).toBeInTheDocument();
    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
    expect(screen.getByText('Option 3')).toBeInTheDocument();
  });

  it('should select default value', () => {
    render(
      <Dropdown placeholder="Select" style={{ width: '200px' }} defaultValue={'option2'}>
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>,
    );
    expect(screen.getByRole('combobox')).toHaveTextContent('Option 2');
  });

  it('should render disabled state', () => {
    render(
      <Dropdown placeholder="Select" style={{ width: '200px' }} disabled data-testid="NovaDropdown-root">
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>,
    );
    expect(screen.getByTestId('NovaDropdown-root')).toHaveClass('Nova-disabled');
  });

  it('should render error state', () => {
    render(
      <Dropdown placeholder="Select" style={{ width: '200px' }} error data-testid="NovaDropdown-root">
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>,
    );
    expect(screen.getByTestId('NovaDropdown-root')).toHaveClass('Nova-error');
  });

  it('should render form control', () => {
    render(
      <FormControl>
        <FormLabel>Label</FormLabel>
        <Dropdown placeholder="Select" style={{ width: '200px' }}>
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Dropdown>
        <FormHelperText>Supporting text</FormHelperText>
      </FormControl>,
    );
    expect(screen.getByText('Label')).toBeInTheDocument();
    expect(screen.getByText('Supporting text')).toBeInTheDocument();
  });

  it('should render large size', () => {
    render(
      <Dropdown placeholder="Select" style={{ width: '200px' }} size="large" data-testid="NovaDropdown-root">
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>,
    );
    expect(screen.getByTestId('NovaDropdown-root')).toHaveClass('NovaDropdown-sizeLarge');
  });

  it('should onChange fired', () => {
    const fn = vi.fn();
    render(
      <Dropdown
        placeholder="Select"
        style={{ width: '200px' }}
        onChange={(e, value) => {
          fn(value);
        }}
      >
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('Option 2'));
    expect(fn).toHaveBeenCalledWith('option2');
  });

  it('should multiple selections', () => {
    const fn = vi.fn();
    render(
      <Dropdown
        placeholder="Select"
        style={{ width: '200px' }}
        onChange={(e, value) => {
          fn(value);
        }}
        multiple
        value={['option1', 'option2']}
      >
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>,
    );
    expect(screen.getByRole('combobox')).toHaveTextContent('Option 1, Option 2');
  });

  it('should onChange fired when multiple option selected', () => {
    const fn = vi.fn();
    render(
      <Dropdown
        placeholder="Select"
        style={{ width: '200px' }}
        onChange={(e, value) => {
          fn(value);
        }}
        multiple
      >
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>,
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('Option 1'));
    fireEvent.click(screen.getByText('Option 2'));
    expect(fn).toHaveBeenCalledWith(['option1', 'option2']);
  });

  it('should render decorators', () => {
    const fn = vi.fn();
    render(
      <Dropdown
        placeholder="Select"
        style={{ width: '600px' }}
        onChange={(e, value) => {
          fn(value);
        }}
        startDecorator={<div>Start Decorator</div>}
        endDecorator={<div>End Decorator</div>}
      >
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>,
    );
    expect(screen.getByText('Start Decorator')).toBeInTheDocument();
    expect(screen.getByText('End Decorator')).toBeInTheDocument();
  });

  it('should render custom list box', async () => {
    render(<Example />);
    expect(screen.getByRole('combobox')).toHaveTextContent('Select');
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getAllByTestId('checkbox')).toHaveLength(20);
    fireEvent.click(screen.getAllByTestId('checkbox')[0]);
    fireEvent.click(screen.getAllByTestId('checkbox')[1]);
    fireEvent.click(screen.getByTestId('apply'));
    expect(screen.getByRole('combobox')).toHaveTextContent('2 options selected');
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByTestId('clear'));
    expect(screen.getByRole('combobox')).toHaveTextContent('Select');
  });
});
