import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface TooltipClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the trigger element. */
  trigger: string;
  /** Class name applied to the portal element. */
  portal: string;
  /** Class name applied to the positioner element. */
  positioner: string;
  /** Class name applied to the popup element. */
  popup: string;
  /** Class name applied to the arrow element. */
  arrow: string;
  /** Class name applied to the root element when the tooltip is open. */
  open: string;
  /** Class name applied to the root element when the tooltip is closed. */
  closed: string;
}

export type TooltipClassKey = keyof TooltipClasses;

export function getTooltipUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTooltip', slot, 'Nova');
}

const tooltipClasses: TooltipClasses = generateUtilityClasses(
  'NovaTooltip',
  ['root', 'trigger', 'portal', 'positioner', 'popup', 'arrow', 'open', 'closed'],
  'Nova',
);

export default tooltipClasses;
