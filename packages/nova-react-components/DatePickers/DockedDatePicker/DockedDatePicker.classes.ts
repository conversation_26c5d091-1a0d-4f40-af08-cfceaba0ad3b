import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

/**
 * Interface for DockedDatePicker CSS classes
 */
export interface DockedDatePickerClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the root element when disabled. */
  disabled: string;

  /** Styles applied to the popper element. */
  popper: string;

  /** Styles applied to the field element. */
  field: string;

  /** Styles applied to the calendar element. */
  calendar: string;

  /** Styles applied to the footer element. */
  footer: string;

  /** Styles applied to the selected day/month/year button. */
  selected: string;

  /** Styles applied to the month view container. */
  monthView: string;

  /** Styles applied to the month button elements. */
  monthButton: string;

  /** Styles applied to the selected month item. */
  selectedMonth: string;

  /** Styles applied to the year view container. */
  yearView: string;

  /** Styles applied to the year button elements. */
  yearButton: string;

  /** Styles applied to the selected year item. */
  selectedYear: string;
}

/**
 * Type for DockedDatePicker class keys
 */
export type DockedDatePickerClassKey = keyof DockedDatePickerClasses;

/**
 * Generates a utility class for the DockedDatePicker component
 * @param {string} slot - The class slot name
 * @returns {string} The generated class name
 */
export function getDockedDatePickerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDockedDatePicker', slot);
}

/**
 * Generated utility classes for the DockedDatePicker component
 */
export const dockedDatePickerClasses: DockedDatePickerClasses = generateUtilityClasses('NovaDockedDatePicker', [
  'root',
  'disabled',
  'popper',
  'field',
  'calendar',
  'footer',
  'selected',
  'monthView',
  'monthButton',
  'selectedMonth',
  'yearView',
  'yearButton',
  'selectedYear',
]);

export default dockedDatePickerClasses;
