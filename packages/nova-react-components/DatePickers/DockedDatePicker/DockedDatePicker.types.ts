import { ElementType } from 'react';
import { BaseDatePickerProps, BaseDatePickerSlots, BaseDatePickerSlotProps } from '../DatePicker/shared';
import { SlotComponentProps } from '@mui/utils/types';
import { OverrideProps } from '@mui/types';

/**
 * Props specific to docked (desktop) date picker behavior
 */
export interface DockedOnlyPickerProps {
  /**
   * Placement of the popup.
   * @default 'bottom-start'
   */
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end';
}

/**
 * Slots for the DockedDatePicker component
 */
export interface DockedDatePickerSlots extends BaseDatePickerSlots {
  /**
   * Component used for the popper.
   * @default Popper
   */
  popper?: React.ElementType;
}

/**
 * Slot props for the DockedDatePicker component
 */
export interface DockedDatePickerSlotProps extends BaseDatePickerSlotProps {
  /**
   * Props for the popper slot.
   */
  popper?: SlotComponentProps<React.ElementType, Record<string, unknown>, DockedDatePickerOwnerState>;
}

export interface DockedDatePickerComponentProps
  extends Omit<BaseDatePickerProps, 'slots' | 'slotProps'>,
    DockedOnlyPickerProps {}

export interface DockedDatePickerTypeMap<P = object, D extends ElementType = 'div'> {
  props: P &
    DockedDatePickerComponentProps & {
      /**
       * Overridable component slots.
       * @default {}
       */
      slots?: DockedDatePickerSlots;
      /**
       * The props used for each component slot.
       * @default {}
       */
      slotProps?: DockedDatePickerSlotProps;
    };
  defaultComponent: D;
}

/**
 * Props for the DockedDatePicker component
 */
export type DockedDatePickerProps<D extends ElementType = DockedDatePickerTypeMap['defaultComponent']> = OverrideProps<
  DockedDatePickerTypeMap<object, D>,
  D
> & {
  component?: D;
};

/**
 * Owner state for DockedDatePicker
 */
export type DockedDatePickerOwnerState = DockedDatePickerProps;
