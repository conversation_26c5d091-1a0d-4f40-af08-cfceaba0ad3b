'use client';
import React, { useCallback } from 'react';
import { OverridableComponent } from '@mui/types';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { ClickAwayListener } from '../../ClickAwayListener';
import { DockedDatePickerProps, DockedDatePickerOwnerState, DockedDatePickerTypeMap } from './DockedDatePicker.types';
import { DateCalendar } from '../DateCalendar';
import { PickerViewFooter } from '../PickerViewFooter';
import { Popper } from '../../Popper';
import { useDefaultDates } from '../hooks/useUtils';
import { DateField } from '../DateField';
import { CalendarIcon } from '../icons';
import { IconButton } from '../../IconButton';
import { getDockedDatePickerUtilityClass } from './DockedDatePicker.classes';
import { useDatePicker } from '../hooks/useDatePicker';

const useUtilityClasses = (ownerState: DockedDatePickerOwnerState) => {
  const { disabled } = ownerState;

  const slots = {
    root: ['root', disabled && 'disabled'],
    popper: ['popper'],
    field: ['field'],
    calendar: ['calendar'],
    footer: ['footer'],
  };

  return composeClasses(slots, getDockedDatePickerUtilityClass, {});
};

const DockedDatePickerRoot = styled('div')({
  display: 'inline-flex',
  flexDirection: 'column',
  position: 'relative',
});

const CalendarContainer = styled('div')(({ theme }) => ({
  backgroundColor: theme.vars.palette.surfaceContainerHigh,
  border: `1px solid ${theme.vars.palette.outlineVariant}`,
  borderRadius: '12px',
}));

export const DockedDatePicker = React.forwardRef(function DockedDatePicker(
  props: DockedDatePickerProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const defaultDate = useDefaultDates();

  const {
    value,
    defaultValue,
    onChange,
    onClose,
    label,
    placeholder,
    disabled = false,
    format = 'DD/MM/YYYY',
    fullWidth = false,
    required = false,
    readOnly = false,
    minDate = defaultDate.minDate,
    maxDate = defaultDate.maxDate,
    disableFuture = false,
    disablePast = false,
    shouldDisableDate,
    placement = 'bottom-start',
    clearText = 'Clear',
    className,
    closeOnSelect = false,
    onOpen,
    autoFocus = false,
    component,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const handleRef = useForkRef(ref, null);

  // Use the date picker hook
  const {
    dateState,
    open,
    anchorEl,
    handleOpen,
    currentView,
    handleViewDateChange,
    handleDateChange,
    handleInputChange,
    handleViewChange,
    handleAccept,
    handleCancel,
    handleClear,
  } = useDatePicker({
    value,
    defaultValue,
    onChange,
    onOpen,
    onClose,
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    format,
    disabled,
    readOnly,
    autoFocus,
    closeOnSelect,
  });

  const ownerState: DockedDatePickerOwnerState = {
    ...props,
    disabled,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? DockedDatePickerRoot;
  const SlotField = slots.field ?? DateField;
  const SlotPopper = slots.popper ?? Popper;
  const SlotCalendar = slots.calendar ?? DateCalendar;
  const SlotFooter = slots.footer ?? PickerViewFooter;

  const handlePickerIconClicked = useCallback(() => {
    handleOpen();
  }, [handleOpen]);

  const openPickerButton = (
    <IconButton
      className="DateField-calendarIcon"
      variant="standard"
      onClick={handlePickerIconClicked}
      disabled={disabled || readOnly}
    >
      {<CalendarIcon />}
    </IconButton>
  );

  const baseFieldProps = {
    value: dateState.tempDate,
    onChange: handleInputChange,
    label,
    placeholder,
    format,
    disabled,
    fullWidth,
    required,
    readOnly,
    disableFuture,
    disablePast,
    maxDate,
    minDate,
    clearable: false,
  };

  const rootProps = useSlotProps({
    elementType: DockedDatePickerRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  const fieldProps = useSlotProps({
    elementType: DateField,
    externalSlotProps: slotProps.field,
    additionalProps: {
      ...baseFieldProps,
      onClick: handleOpen,
      endDecorator: openPickerButton,
    },
    ownerState,
    className: classes.field,
  });

  const popperProps = useSlotProps({
    elementType: Popper,
    externalSlotProps: slotProps.popper,
    additionalProps: {
      open,
      anchorEl,
      placement,
      disablePortal: true,
    },
    ownerState,
    className: classes.popper,
  });

  const calendarProps = useSlotProps({
    elementType: DateCalendar,
    externalSlotProps: slotProps.calendar,
    additionalProps: {
      view: currentView,
      date: dateState.tempDate,
      viewDate: dateState.selectedDate,
      onViewDateChange: handleViewDateChange,
      onDateChange: handleDateChange,
      onViewChange: handleViewChange,
      disableFuture,
      disablePast,
      shouldDisableDate,
      minDate,
      maxDate,
      disabled,
      readOnly,
      variant: 'docked',
      viewStyle: 'list',
    },
    ownerState,
    className: classes.calendar,
  });

  const footerProps = useSlotProps({
    elementType: PickerViewFooter,
    externalSlotProps: slotProps.footer,
    additionalProps: {
      onAccept: handleAccept,
      onCancel: handleCancel,
      onClear: handleClear,
      clearText,
      disabled: disabled || readOnly,
    },
    ownerState,
    className: classes.footer,
  });

  return (
    <React.Fragment>
      <SlotRoot {...rootProps}>
        <SlotField {...fieldProps} />
      </SlotRoot>
      <SlotPopper {...popperProps}>
        <ClickAwayListener onClickAway={handleCancel}>
          <CalendarContainer>
            <SlotCalendar {...calendarProps} />
            {currentView === 'day' && <SlotFooter {...footerProps} />}
          </CalendarContainer>
        </ClickAwayListener>
      </SlotPopper>
    </React.Fragment>
  );
}) as OverridableComponent<DockedDatePickerTypeMap>;
