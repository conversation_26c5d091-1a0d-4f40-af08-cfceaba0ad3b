import {
  unstable_generateUtilityClass as generateUtilityClass,
  unstable_generateUtilityClasses as generateUtilityClasses,
} from '@mui/utils';

export interface DateRangePickerDayClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the day element. */
  day: string;
  /** Class name applied to the day element when it's selected. */
  daySelected: string;
  /** Class name applied to the day element when it's highlighted. */
  dayHighlight: string;
  /** Class name applied to the day element when it's in preview mode. */
  dayPreview: string;
  /** Class name applied to the day element when it's outside the current month. */
  dayOutsideMonth: string;
  /** Class name applied to the day element when it's not selected. */
  notSelectedDate: string;
  /** Class name applied to the day range interval element. */
  rangeIntervalDayHighlight: string;
  /** Class name applied to the day range interval element when it's the start of a range. */
  rangeIntervalDayHighlightStart: string;
  /** Class name applied to the day range interval element when it's the end of a range. */
  rangeIntervalDayHighlightEnd: string;
  /** Class name applied to the day range interval element in preview mode. */
  rangeIntervalDayPreview: string;
  /** Class name applied to the day range interval element when it's the start of a preview range. */
  rangeIntervalDayPreviewStart: string;
  /** Class name applied to the day range interval element when it's the end of a preview range. */
  rangeIntervalDayPreviewEnd: string;
  /** Class name applied to the day element outside the range interval. */
  dayOutsideRangeInterval: string;
  /** Class name applied to the day inside the range interval. */
  dayInsideRangeInterval: string;
  /** Class name applied to the range interval preview wrapper. */
  rangeIntervalPreview: string;
  /** Class name applied to the root element if day is outside current month. */
  outsideCurrentMonth: string;
  /** Class name applied to the root element if day is the first visible cell. */
  firstVisibleCell: string;
  /** Class name applied to the root element if day is the last visible cell. */
  lastVisibleCell: string;
}

export type DateRangePickerDayClassKey = keyof DateRangePickerDayClasses;

export function getDateRangePickerDayUtilityClass(slot: string) {
  return generateUtilityClass('NovaDateRangePickerDay', slot, 'Nova');
}

export const dateRangePickerDayClasses: DateRangePickerDayClasses = generateUtilityClasses(
  'NovaDateRangePickerDay',
  [
    'root',
    'day',
    'daySelected',
    'dayHighlight',
    'dayPreview',
    'dayOutsideMonth',
    'notSelectedDate',
    'rangeIntervalDayHighlight',
    'rangeIntervalDayHighlightStart',
    'rangeIntervalDayHighlightEnd',
    'rangeIntervalDayPreview',
    'rangeIntervalDayPreviewStart',
    'rangeIntervalDayPreviewEnd',
    'dayOutsideRangeInterval',
    'dayInsideRangeInterval',
    'rangeIntervalPreview',
    'outsideCurrentMonth',
    'firstVisibleCell',
    'lastVisibleCell',
  ],
  'Nova',
);
