import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import { describe, expect, it, afterEach, vi, beforeEach } from 'vitest';
import userEvent from '@testing-library/user-event';
import dayjs from 'dayjs/esm';
import { DateRangePickerDay } from './DateRangePickerDay';
import { PickerProvider } from '../PickerContext';

// Mock DatePickerDay component
vi.mock('../DatePickerDay', () => ({
  DatePickerDay: React.forwardRef<HTMLButtonElement, any>((props, ref) => (
    <button
      ref={ref}
      data-testid="date-picker-day"
      data-day={props.day?.format('YYYY-MM-DD')}
      data-selected={props.selected}
      data-disabled={props.disabled}
      data-outside-month={props.outsideCurrentMonth}
      data-draggable={props.draggable}
      data-timestamp={props['data-timestamp']}
      data-position={props['data-position']}
      className={props.className}
      onClick={() => props.onDaySelect?.(props.day)}
      onMouseEnter={props.onMouseEnter}
      onDragStart={props.onDragStart}
      onDragOver={props.onDragOver}
      onDragEnd={props.onDragEnd}
      onDrop={props.onDrop}
      {...props}
    >
      {props.day?.format('D')}
    </button>
  )),
}));

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<DateRangePickerDay />', () => {
  const defaultDay = dayjs('2023-05-15');

  const defaultProps = {
    day: defaultDay,
    onDaySelect: vi.fn(),
    onDayHover: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('basic rendering', () => {
    it('should render with default props', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toBeInTheDocument();
      expect(dayButton).toHaveAttribute('data-day', '2023-05-15');
      expect(dayButton).toHaveTextContent('15');
    });

    it('should render with three-layer structure', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toBeInTheDocument();

      const rootElement = dayButton.closest('div');
      expect(rootElement).toBeInTheDocument();
      expect(rootElement).toHaveClass('NovaDateRangePickerDay-root');
    });

    it('should forward custom className', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} className="custom-day-class" />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      const rootElement = dayButton.closest('div');
      expect(rootElement).toHaveClass('custom-day-class');
    });

    it('should render with data attributes', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} data-timestamp={1684108800000} data-position="middle" />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toHaveAttribute('data-timestamp', '1684108800000');
      expect(dayButton).toHaveAttribute('data-position', 'middle');
    });
  });

  describe('range highlighting', () => {
    it('should render with range highlighting', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting />
        </PickerProvider>,
      );

      const rootElement = screen.getByTestId('DateRangeHighlight');
      expect(rootElement).toBeInTheDocument();
      expect(rootElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayHighlight');
    });

    it('should render start of range highlighting', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting isStartOfHighlighting />
        </PickerProvider>,
      );

      const rootElement = screen.getByTestId('DateRangeHighlight');
      expect(rootElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayHighlight');
      expect(rootElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayHighlightStart');
    });

    it('should render end of range highlighting', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting isEndOfHighlighting />
        </PickerProvider>,
      );

      const rootElement = screen.getByTestId('DateRangeHighlight');
      expect(rootElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayHighlight');
      expect(rootElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayHighlightEnd');
    });

    it('should not render highlight testid when not highlighting', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting={false} />
        </PickerProvider>,
      );

      expect(screen.queryByTestId('DateRangeHighlight')).not.toBeInTheDocument();
      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toBeInTheDocument();
    });

    it('should not render highlight when outside current month', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting outsideCurrentMonth />
        </PickerProvider>,
      );

      expect(screen.queryByTestId('DateRangeHighlight')).not.toBeInTheDocument();
    });
  });

  describe('preview functionality', () => {
    it('should render with preview highlighting', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isPreviewing />
        </PickerProvider>,
      );

      const previewElement = screen.getByTestId('DateRangePreview');
      expect(previewElement).toBeInTheDocument();
      expect(previewElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayPreview');
    });

    it('should render start of preview highlighting', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isPreviewing isStartOfPreviewing />
        </PickerProvider>,
      );

      const previewElement = screen.getByTestId('DateRangePreview');
      expect(previewElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayPreview');
      expect(previewElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayPreviewStart');
    });

    it('should render end of preview highlighting', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isPreviewing isEndOfPreviewing />
        </PickerProvider>,
      );

      const previewElement = screen.getByTestId('DateRangePreview');
      expect(previewElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayPreview');
      expect(previewElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayPreviewEnd');
    });

    it('should not render preview testid when not previewing', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isPreviewing={false} />
        </PickerProvider>,
      );

      expect(screen.queryByTestId('DateRangePreview')).not.toBeInTheDocument();
    });

    it('should not render preview when outside current month', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isPreviewing outsideCurrentMonth />
        </PickerProvider>,
      );

      expect(screen.queryByTestId('DateRangePreview')).not.toBeInTheDocument();
    });
  });

  describe('user interactions', () => {
    it('should call onDaySelect when clicked', () => {
      const mockOnDaySelect = vi.fn();

      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} onDaySelect={mockOnDaySelect} />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      fireEvent.click(dayButton);

      expect(mockOnDaySelect).toHaveBeenCalledWith(defaultDay);
    });

    it('should call onDayHover when mouse enters', () => {
      const mockOnDayHover = vi.fn();

      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} onDayHover={mockOnDayHover} />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      fireEvent.mouseEnter(dayButton);

      expect(mockOnDayHover).toHaveBeenCalledWith(defaultDay);
    });

    it('should not call onDayHover when disabled', () => {
      const mockOnDayHover = vi.fn();

      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} onDayHover={mockOnDayHover} disabled />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      fireEvent.mouseEnter(dayButton);

      expect(mockOnDayHover).not.toHaveBeenCalled();
    });
  });

  describe('disabled state', () => {
    it('should render as disabled', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} disabled />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toHaveAttribute('data-disabled', 'true');
    });

    it('should apply disabled styling classes', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} disabled />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toBeInTheDocument();
      // Verify the component renders with disabled state
      expect(dayButton).toHaveAttribute('data-disabled', 'true');
    });
  });

  describe('outside current month', () => {
    it('should handle outside current month styling', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} outsideCurrentMonth />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toHaveAttribute('data-outside-month', 'true');

      const rootElement = dayButton.closest('div');
      expect(rootElement).toHaveClass('NovaDateRangePickerDay-outsideCurrentMonth');
    });
  });

  describe('selection state', () => {
    it('should render as selected', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} selected />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toHaveAttribute('data-selected', 'true');
    });

    it('should apply proper styling for selected state', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} selected />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toHaveAttribute('data-selected', 'true');
    });
  });

  describe('first and last visible cell', () => {
    it('should handle first visible cell styling', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isFirstVisibleCell />
        </PickerProvider>,
      );

      const rootElement = screen.getByTestId('date-picker-day').closest('div');
      expect(rootElement).toHaveClass('NovaDateRangePickerDay-firstVisibleCell');
    });

    it('should handle last visible cell styling', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isLastVisibleCell />
        </PickerProvider>,
      );

      const rootElement = screen.getByTestId('date-picker-day').closest('div');
      expect(rootElement).toHaveClass('NovaDateRangePickerDay-lastVisibleCell');
    });
  });

  describe('drag and drop', () => {
    it('should render as draggable', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} draggable />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toHaveAttribute('data-draggable', 'true');
    });

    it('should handle drag events', () => {
      const mockOnDragStart = vi.fn();
      const mockOnDragOver = vi.fn();
      const mockOnDragEnd = vi.fn();
      const mockOnDrop = vi.fn();

      render(
        <PickerProvider>
          <DateRangePickerDay
            {...defaultProps}
            draggable
            onDragStart={mockOnDragStart}
            onDragOver={mockOnDragOver}
            onDragEnd={mockOnDragEnd}
            onDrop={mockOnDrop}
          />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');

      fireEvent.dragStart(dayButton);
      expect(mockOnDragStart).toHaveBeenCalled();

      fireEvent.dragOver(dayButton);
      expect(mockOnDragOver).toHaveBeenCalled();

      fireEvent.dragEnd(dayButton);
      expect(mockOnDragEnd).toHaveBeenCalled();

      fireEvent.drop(dayButton);
      expect(mockOnDrop).toHaveBeenCalled();
    });
  });

  describe('complex combinations', () => {
    it('should handle multiple highlighting states', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting isStartOfHighlighting isPreviewing isEndOfPreviewing />
        </PickerProvider>,
      );

      const highlightElement = screen.getByTestId('DateRangeHighlight');
      expect(highlightElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayHighlight');
      expect(highlightElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayHighlightStart');

      const previewElement = screen.getByTestId('DateRangePreview');
      expect(previewElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayPreview');
      expect(previewElement).toHaveClass('NovaDateRangePickerDay-rangeIntervalDayPreviewEnd');
    });

    it('should handle disabled state with highlighting', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting disabled />
        </PickerProvider>,
      );

      const highlightElement = screen.getByTestId('DateRangeHighlight');
      expect(highlightElement).toBeInTheDocument();

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toHaveAttribute('data-disabled', 'true');
    });

    it('should handle selected state with range highlighting', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting selected />
        </PickerProvider>,
      );

      const highlightElement = screen.getByTestId('DateRangeHighlight');
      expect(highlightElement).toBeInTheDocument();

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toHaveAttribute('data-selected', 'true');
    });
  });

  describe('ref forwarding', () => {
    it('should forward ref correctly', () => {
      const ref = React.createRef<HTMLDivElement>();

      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} ref={ref} />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toBeInTheDocument();
      // Verify the component structure supports ref forwarding
      const rootElement = dayButton.closest('div');
      expect(rootElement).toBeInstanceOf(HTMLElement);
    });
  });

  describe('edge cases', () => {
    it('should handle undefined onDayHover gracefully', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} onDayHover={undefined} />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(() => {
        fireEvent.mouseEnter(dayButton);
      }).not.toThrow();
    });

    it('should handle undefined onDaySelect gracefully', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} onDaySelect={undefined} />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(() => {
        fireEvent.click(dayButton);
      }).not.toThrow();
    });

    it('should handle null day gracefully', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} day={null as any} />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toBeInTheDocument();
      expect(dayButton).not.toHaveAttribute('data-day');
    });
  });

  describe('accessibility', () => {
    it('should maintain accessibility with range highlighting', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting aria-label="Selected date in range" />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toHaveAttribute('aria-label', 'Selected date in range');

      const highlightElement = screen.getByTestId('DateRangeHighlight');
      expect(highlightElement).toBeInTheDocument();
    });

    it('should maintain accessibility when disabled', () => {
      render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} disabled aria-label="Disabled date" />
        </PickerProvider>,
      );

      const dayButton = screen.getByTestId('date-picker-day');
      expect(dayButton).toHaveAttribute('aria-label', 'Disabled date');
      expect(dayButton).toHaveAttribute('data-disabled', 'true');
    });
  });

  describe('performance', () => {
    it('should not re-render unnecessarily', () => {
      const { rerender } = render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      const initialElement = screen.getByTestId('date-picker-day');

      rerender(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('date-picker-day')).toBe(initialElement);
    });

    it('should handle rapid state changes efficiently', () => {
      const { rerender } = render(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting={false} />
        </PickerProvider>,
      );

      expect(screen.queryByTestId('DateRangeHighlight')).not.toBeInTheDocument();

      rerender(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting />
        </PickerProvider>,
      );

      expect(screen.getByTestId('DateRangeHighlight')).toBeInTheDocument();

      rerender(
        <PickerProvider>
          <DateRangePickerDay {...defaultProps} isHighlighting={false} />
        </PickerProvider>,
      );

      expect(screen.queryByTestId('DateRangeHighlight')).not.toBeInTheDocument();
    });
  });
});
