import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import dayjs from 'dayjs/esm';
import { DayCalendar } from './DayCalendar';
import { PickerProvider } from '../PickerContext';

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<DayCalendar />', () => {
  const defaultProps = {
    date: dayjs('2023-05-15'),
    viewDate: dayjs('2023-05-15'),
    handleDateChange: vi.fn(),
    disableFuture: false,
    disablePast: false,
    minDate: dayjs('1900-01-01'),
    maxDate: dayjs('2099-12-31'),
    shouldDisableDate: undefined,
    disabled: false,
    readOnly: false,
    displayWeekNumber: true,
    component: 'div',
  };

  describe('rendering', () => {
    it('should render calendar grid with weekday header', () => {
      const { container } = render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      // Should render weekday header
      expect(container.querySelector('div[role="row"]')).toBeInTheDocument();
      // Should render calendar grid
      expect(container.querySelector('div[role="grid"]')).toBeInTheDocument();
    });

    it('should render days of the month', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      // Should show the selected date (15)
      expect(screen.getByText('15')).toBeInTheDocument();

      // Should show other days of May 2023
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('31')).toBeInTheDocument();
    });

    it('should not display weekday header when displayWeekNumber is false', () => {
      const { container } = render(
        <PickerProvider>
          <DayCalendar {...defaultProps} displayWeekNumber={false} />
        </PickerProvider>,
      );

      expect(container.querySelector('div[role="row"]')).not.toBeInTheDocument();
    });

    it('should render selected day with proper attributes', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      expect(selectedDay).toHaveAttribute('aria-selected', 'true');
      expect(selectedDay).toHaveAttribute('tabIndex', '0');
    });

    it('should render today with proper attributes', () => {
      const today = dayjs();
      const props = {
        ...defaultProps,
        date: null,
        viewDate: today,
      };

      render(
        <PickerProvider>
          <DayCalendar {...props} />
        </PickerProvider>,
      );

      const todayButton = screen.getByText(today.format('D')).closest('button');
      expect(todayButton).toHaveAttribute('aria-current', 'date');
    });
  });

  describe('day selection', () => {
    it('should call handleDateChange when day is clicked', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const day20 = screen.getByText('20').closest('button');
      fireEvent.click(day20 as HTMLElement);

      expect(defaultProps.handleDateChange).toHaveBeenCalledWith(
        expect.objectContaining({
          $d: expect.any(Date),
        }),
      );
    });

    it('should not call handleDateChange when disabled day is clicked', () => {
      const shouldDisableDate = (date: any) => date.date() === 20;

      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} shouldDisableDate={shouldDisableDate} />
        </PickerProvider>,
      );

      const day20 = screen.getByText('20').closest('button');
      expect(day20).toBeDisabled();

      fireEvent.click(day20 as HTMLElement);
      expect(defaultProps.handleDateChange).not.toHaveBeenCalled();
    });
  });

  describe('keyboard navigation', () => {
    it('should handle arrow left navigation', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'ArrowLeft' });

      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });

    it('should handle arrow right navigation', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'ArrowRight' });

      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });

    it('should handle arrow up navigation', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'ArrowUp' });

      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });

    it('should handle arrow down navigation', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'ArrowDown' });

      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });

    it('should handle Home key navigation', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'Home' });

      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });

    it('should handle End key navigation', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'End' });

      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });

    it('should handle PageUp key navigation', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'PageUp' });

      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });

    it('should handle PageDown key navigation', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'PageDown' });

      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });

    it('should not handle other keys', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'Enter' });

      expect(defaultProps.handleDateChange).not.toHaveBeenCalled();
    });
  });

  describe('disabled states', () => {
    it('should disable all days when calendar is disabled', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} disabled />
        </PickerProvider>,
      );

      const dayButtons = screen.getAllByRole('button');
      dayButtons.forEach((button) => {
        expect(button).toBeDisabled();
      });
    });

    it('should disable all days when calendar is readOnly', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} readOnly />
        </PickerProvider>,
      );

      const dayButtons = screen.getAllByRole('button');
      dayButtons.forEach((button) => {
        expect(button).toBeDisabled();
      });
    });

    it('should disable future dates when disableFuture is true', () => {
      const today = dayjs();
      const props = {
        ...defaultProps,
        date: today,
        viewDate: today,
        disableFuture: true,
      };

      render(
        <PickerProvider>
          <DayCalendar {...props} />
        </PickerProvider>,
      );

      // Future dates should be disabled
      const futureDate = today.add(1, 'day');
      const futureDateButton = screen.getByText(futureDate.format('D')).closest('button');
      expect(futureDateButton).toBeDisabled();
    });

    it('should disable past dates when disablePast is true', () => {
      const today = dayjs();
      const props = {
        ...defaultProps,
        date: today,
        viewDate: today,
        disablePast: true,
      };

      render(
        <PickerProvider>
          <DayCalendar {...props} />
        </PickerProvider>,
      );

      // Past dates should be disabled
      const pastDate = today.subtract(1, 'day');
      const pastDateButton = screen.getByText(pastDate.format('D')).closest('button');
      expect(pastDateButton).toBeDisabled();
    });
  });

  describe('date constraints', () => {
    it('should disable dates before minDate', () => {
      const minDate = dayjs('2023-05-10');
      const props = {
        ...defaultProps,
        minDate,
      };

      render(
        <PickerProvider>
          <DayCalendar {...props} />
        </PickerProvider>,
      );

      const beforeMinDate = screen.getByText('5').closest('button');
      expect(beforeMinDate).toBeDisabled();
    });

    it('should disable dates after maxDate', () => {
      const maxDate = dayjs('2023-05-20');
      const props = {
        ...defaultProps,
        maxDate,
      };

      render(
        <PickerProvider>
          <DayCalendar {...props} />
        </PickerProvider>,
      );

      const afterMaxDate = screen.getByText('25').closest('button');
      expect(afterMaxDate).toBeDisabled();
    });
  });

  describe('selectedDays prop', () => {
    it('should handle multiple selected days', () => {
      const selectedDays = [dayjs('2023-05-10'), dayjs('2023-05-15'), dayjs('2023-05-20')];

      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} selectedDays={selectedDays} />
        </PickerProvider>,
      );

      // All selected days should have aria-selected="true"
      const day10 = screen.getByText('10').closest('button');
      const day15 = screen.getByText('15').closest('button');
      const day20 = screen.getByText('20').closest('button');

      expect(day10).toHaveAttribute('aria-selected', 'true');
      expect(day15).toHaveAttribute('aria-selected', 'true');
      expect(day20).toHaveAttribute('aria-selected', 'true');
    });
  });

  describe('slots and slotProps', () => {
    it('should render with custom root slot', () => {
      const CustomRoot = React.forwardRef<HTMLDivElement>((props, ref) => (
        <div ref={ref} data-testid="custom-root" {...props} />
      ));

      const slots = {
        root: CustomRoot,
      };

      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} slots={slots} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('custom-root')).toBeInTheDocument();
    });

    it('should apply custom root slot props', () => {
      const slotProps = {
        root: {
          'data-testid': 'root-with-props',
          className: 'custom-class',
        },
      };

      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} slotProps={slotProps} />
        </PickerProvider>,
      );

      const rootElement = screen.getByTestId('root-with-props');
      expect(rootElement).toBeInTheDocument();
      expect(rootElement).toHaveClass('custom-class');
    });

    it('should render with custom day slot', () => {
      const CustomDay = React.forwardRef<HTMLButtonElement>((props, ref) => (
        <button ref={ref} data-testid="custom-day" {...props} />
      ));

      const slots = {
        day: CustomDay,
      };

      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} slots={slots} />
        </PickerProvider>,
      );

      expect(screen.getAllByTestId('custom-day')[0]).toBeInTheDocument();
    });
  });

  describe('focus management', () => {
    it('should focus selected day when changed via keyboard', () => {
      const { rerender } = render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'ArrowRight' });

      // After rerender with new date, focus should be managed
      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });

    it('should handle focusedDay prop', () => {
      const focusedDay = dayjs('2023-05-20');
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} focusedDay={focusedDay} />
        </PickerProvider>,
      );

      expect(screen.getByText('20')).toBeInTheDocument();
    });
  });

  describe('month boundaries in navigation', () => {
    it('should handle arrow navigation across month boundaries', () => {
      const props = {
        ...defaultProps,
        date: dayjs('2023-05-01'), // First day of month
        viewDate: dayjs('2023-05-01'),
      };

      render(
        <PickerProvider>
          <DayCalendar {...props} />
        </PickerProvider>,
      );

      const firstDay = screen.getByText('1').closest('button');
      fireEvent.keyDown(firstDay as HTMLElement, { key: 'ArrowLeft' });

      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });

    it('should handle PageUp navigation to previous month', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'PageUp' });

      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });

    it('should handle PageDown navigation to next month', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      const selectedDay = screen.getByText('15').closest('button');
      fireEvent.keyDown(selectedDay as HTMLElement, { key: 'PageDown' });

      expect(defaultProps.handleDateChange).toHaveBeenCalled();
    });
  });

  describe('outside month days', () => {
    it('should render empty cells for days outside current month', () => {
      const { container } = render(
        <PickerProvider>
          <DayCalendar {...defaultProps} />
        </PickerProvider>,
      );

      // Should have empty div cells for outside month days
      const emptyCells = container.querySelectorAll('div[style*="aspect-ratio: 1/1"]');
      expect(emptyCells.length).toBeGreaterThan(0);
    });
  });

  describe('edge cases', () => {
    it('should handle null date', () => {
      const props = {
        ...defaultProps,
        date: null,
      };

      render(
        <PickerProvider>
          <DayCalendar {...props} />
        </PickerProvider>,
      );

      expect(screen.getByText('15')).toBeInTheDocument();
    });

    it('should handle month with different number of days', () => {
      const februaryDate = dayjs('2023-02-15');
      const props = {
        ...defaultProps,
        date: februaryDate,
        viewDate: februaryDate,
      };

      render(
        <PickerProvider>
          <DayCalendar {...props} />
        </PickerProvider>,
      );

      expect(screen.getByText('15')).toBeInTheDocument();
      expect(screen.getByText('28')).toBeInTheDocument();
      expect(screen.queryByText('31')).not.toBeInTheDocument();
    });

    it('should handle leap year February', () => {
      const leapYearFeb = dayjs('2024-02-15');
      const props = {
        ...defaultProps,
        date: leapYearFeb,
        viewDate: leapYearFeb,
      };

      render(
        <PickerProvider>
          <DayCalendar {...props} />
        </PickerProvider>,
      );

      expect(screen.getByText('29')).toBeInTheDocument();
    });
  });

  describe('component prop and forwarding', () => {
    it('should forward additional props to root element', () => {
      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} data-testid="day-calendar" aria-label="Date selection" />
        </PickerProvider>,
      );

      const calendar = screen.getByTestId('day-calendar');
      expect(calendar).toBeInTheDocument();
      expect(calendar).toHaveAttribute('aria-label', 'Date selection');
    });

    it('should render with custom component', () => {
      const CustomComponent = React.forwardRef<HTMLDivElement>((props, ref) => (
        <section ref={ref} data-testid="custom-component" {...props} />
      ));

      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} component={CustomComponent} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('custom-component')).toBeInTheDocument();
    });
  });

  describe('ref handling', () => {
    it('should forward ref correctly', () => {
      const ref = React.createRef<HTMLDivElement>();

      render(
        <PickerProvider>
          <DayCalendar {...defaultProps} ref={ref} />
        </PickerProvider>,
      );

      expect(ref.current).toBeInstanceOf(HTMLDivElement);
    });
  });
});
