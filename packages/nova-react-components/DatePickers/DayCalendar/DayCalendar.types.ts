import * as React from 'react';
import { SlotComponentProps } from '@mui/utils';
import { PickerDateType } from '../models/pickers';
import {
  BaseDateValidationProps,
  DayValidationProps,
  MonthValidationProps,
  YearValidationProps,
} from '../models/validation';
import { FormProps } from '../models/formProps';
import { SxProps } from '../../types/theme';
import { OverrideProps } from '@mui/types';
import { ExportedDatePickerDayProps } from '../DatePickerDay/DatePickerDay.types';

/**
 * Slots for DayCalendar component
 */
export interface DayCalendarSlots {
  /**
   * Custom component for day.
   * @default DatePickerDay
   */
  day?: React.ElementType;
  /**
   * Custom component for the root element.
   * @default div
   */
  root?: React.ElementType;
}

/**
 * Slot props for DayCalendar component
 */
export interface DayCalendarSlotProps {
  day?: SlotComponentProps<React.ElementType, object, DayCalendarOwnerState>;
  /**
   * Props passed to the root element.
   */
  root?: SlotComponentProps<React.ElementType, object, DayCalendarOwnerState>;
}

/**
 * Props exported by DayCalendar for use by parent components
 */
export interface ExportedDayCalendarProps extends ExportedDatePickerDayProps {
  /**
   * First day of the week. 0 = Sunday, 1 = Monday, etc.
   * @default 0
   */
  firstDayOfWeek?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
  /**
   * If `true`, the week number will be display in the calendar.
   * @default true
   */
  displayWeekNumber?: boolean;
}

export interface DayCalendarComponentProps
  extends ExportedDayCalendarProps,
    DayValidationProps,
    MonthValidationProps,
    YearValidationProps,
    Required<BaseDateValidationProps>,
    FormProps {
  /**
   * The currently selected date.
   */
  date?: PickerDateType | null;

  /**
   * The month to display in the calendar.
   */
  viewDate: PickerDateType;

  /**
   * Callback for when a date is selected.
   */
  handleDateChange: (date: PickerDateType) => void;

  /**
   * The selected days (for range selection)
   */
  selectedDays?: Array<PickerDateType | null>;

  /**
   * The focused day (for keyboard navigation)
   */
  focusedDay?: PickerDateType | null;

  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
}

export interface DayCalendarTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    DayCalendarComponentProps & {
      /**
       * The slots for customizing the component appearance.
       */
      slots?: DayCalendarSlots;

      /**
       * The props used for each slot.
       */
      slotProps?: DayCalendarSlotProps;
    };
  defaultComponent: D;
}

export type DayCalendarProps<D extends React.ElementType = DayCalendarTypeMap['defaultComponent']> = OverrideProps<
  DayCalendarTypeMap<object, D>,
  D
> & {
  component?: D;
};

export type DayCalendarOwnerState = DayCalendarProps;
