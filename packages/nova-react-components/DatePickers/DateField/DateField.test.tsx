import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent, waitFor } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import dayjs from 'dayjs/esm';
import { DateField } from './DateField';
import { PickerProvider } from '../PickerContext';

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<DateField />', () => {
  const defaultProps = {
    value: dayjs('2023-05-15'),
    onChange: vi.fn(),
    label: 'Date',
    format: 'MM/DD/YYYY',
    disabled: false,
    error: false,
    required: false,
    readOnly: false,
    clearable: true,
    size: 'medium' as const,
  };

  describe('rendering', () => {
    it('should render with default props', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/15/2023')).toBeInTheDocument();
      expect(screen.getByLabelText('Date')).toBeInTheDocument();
    });

    it('should render without value', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      // When no value is provided, the input should show the format placeholder
      expect(input).toHaveAttribute('placeholder', 'MM/DD/YYYY');
    });

    it('should render with custom format', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} format="DD/MM/YYYY" />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('15/05/2023')).toBeInTheDocument();
    });

    it('should render with placeholder', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      expect(screen.getByPlaceholderText('MM/DD/YYYY')).toBeInTheDocument();
    });

    it('should render with helper text', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} helperText="Select a date" />
        </PickerProvider>,
      );

      expect(screen.getByText('Select a date')).toBeInTheDocument();
    });
  });

  describe('user interactions', () => {
    it('should call onChange when input value changes', async () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '12/25/2023' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalled();
      });
    });

    it('should handle input click', () => {
      const onFocus = vi.fn();
      render(
        <PickerProvider>
          <DateField {...defaultProps} onFocus={onFocus} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.focus(input);

      // Focus should call onFocus
      expect(onFocus).toHaveBeenCalled();
    });

    it('should handle input focus', () => {
      const onFocus = vi.fn();
      render(
        <PickerProvider>
          <DateField {...defaultProps} onFocus={onFocus} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.focus(input);

      expect(onFocus).toHaveBeenCalled();
    });

    it('should handle input blur', () => {
      const onBlur = vi.fn();
      render(
        <PickerProvider>
          <DateField {...defaultProps} onBlur={onBlur} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.blur(input);

      expect(onBlur).toHaveBeenCalled();
    });

    it('should handle paste events', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.paste(input, {
        clipboardData: {
          getData: () => '12/25/2023',
        },
      });

      expect(defaultProps.onChange).toHaveBeenCalled();
    });
  });

  describe('keyboard navigation', () => {
    it('should handle ArrowLeft key', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'ArrowLeft' });

      // Should not cause errors
      expect(input).toBeInTheDocument();
    });

    it('should handle ArrowRight key', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'ArrowRight' });

      // Should not cause errors
      expect(input).toBeInTheDocument();
    });

    it('should handle ArrowUp key', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'ArrowUp' });

      expect(defaultProps.onChange).toHaveBeenCalled();
      // Should increment the date by 1 day
      const calledWith = defaultProps.onChange.mock.calls[0][0];
      expect(calledWith.date()).toBe(16); // May 15 -> May 16
    });

    it('should handle ArrowDown key', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'ArrowDown' });

      expect(defaultProps.onChange).toHaveBeenCalled();
      // Should decrement the date by 1 day
      const calledWith = defaultProps.onChange.mock.calls[0][0];
      expect(calledWith.date()).toBe(14); // May 15 -> May 14
    });

    it('should handle Enter key', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'Enter' });

      // Should not cause errors
      expect(input).toBeInTheDocument();
    });

    it('should handle Escape key', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'Escape' });

      // Should not cause errors
      expect(input).toBeInTheDocument();
    });
  });

  describe('clear functionality', () => {
    it('should show clear button when value is present and clearable is true', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={dayjs('2023-05-15')} clearable />
        </PickerProvider>,
      );

      // Clear button should be accessible as a button
      expect(screen.getByTestId('clear-button')).toBeInTheDocument();
    });

    it('should not show clear button when clearable is false', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} clearable={false} />
        </PickerProvider>,
      );

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('should not show clear button when no value', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} clearable />
        </PickerProvider>,
      );

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('should not show clear button when disabled', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} disabled clearable />
        </PickerProvider>,
      );

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('should not show clear button when readOnly', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} readOnly clearable />
        </PickerProvider>,
      );

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('should call onChange with null when clear button is clicked', () => {
      const onChange = vi.fn();
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={dayjs('2023-05-15')} onChange={onChange} clearable />
        </PickerProvider>,
      );

      const clearButton = screen.getByTestId('clear-button');
      fireEvent.click(clearButton);

      expect(onChange).toHaveBeenCalledWith(null);
    });
  });

  describe('disabled state', () => {
    it('should disable input when disabled', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} disabled />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });

    it('should not call onChange when disabled', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} disabled />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      // Disabled input should not allow changes
      expect(input).toBeDisabled();
    });
  });

  describe('readOnly state', () => {
    it('should make input readOnly when readOnly', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} readOnly />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('readonly');
    });

    it('should not call onChange when readOnly', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} readOnly />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '12/25/2023' } });

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });
  });

  describe('error state', () => {
    it('should show error state when error is true', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} error />
        </PickerProvider>,
      );

      // Check for error state on the input instead of container
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-invalid', 'true');
    });

    it('should show field error in helper text', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={dayjs('invalid')} />
        </PickerProvider>,
      );

      // Should render without crashing
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should prioritize external error over field error', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} error helperText="External error" value={dayjs('invalid')} />
        </PickerProvider>,
      );

      expect(screen.getByText('External error')).toBeInTheDocument();
    });
  });

  describe('required state', () => {
    it('should show required indicator when required', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} required />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toBeRequired();
    });
  });

  describe('size variants', () => {
    it('should render with small size', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} size="small" />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should render with medium size', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} size="medium" />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('date validation', () => {
    it('should validate against minDate', () => {
      const minDate = dayjs('2023-05-10');
      render(
        <PickerProvider>
          <DateField {...defaultProps} minDate={minDate} value={dayjs('2023-05-05')} />
        </PickerProvider>,
      );

      // Should render without crashing
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should validate against maxDate', () => {
      const maxDate = dayjs('2023-05-20');
      render(
        <PickerProvider>
          <DateField {...defaultProps} maxDate={maxDate} value={dayjs('2023-05-25')} />
        </PickerProvider>,
      );

      // Should render without crashing
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should validate with disableFuture', () => {
      const futureDate = dayjs().add(1, 'day');
      render(
        <PickerProvider>
          <DateField {...defaultProps} disableFuture value={futureDate} />
        </PickerProvider>,
      );

      // Should render without crashing
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should validate with disablePast', () => {
      const pastDate = dayjs().subtract(1, 'day');
      render(
        <PickerProvider>
          <DateField {...defaultProps} disablePast value={pastDate} />
        </PickerProvider>,
      );

      // Should render without crashing
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should validate with shouldDisableDate', () => {
      const shouldDisableDate = (date: any) => date.date() === 15;
      render(
        <PickerProvider>
          <DateField {...defaultProps} shouldDisableDate={shouldDisableDate} />
        </PickerProvider>,
      );

      // Should render without crashing
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('defaultValue', () => {
    it('should use defaultValue when uncontrolled', () => {
      render(
        <PickerProvider>
          <DateField defaultValue={dayjs('2023-12-25')} format="MM/DD/YYYY" />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('12/25/2023')).toBeInTheDocument();
    });

    it('should call onChange when defaultValue changes', () => {
      const onChange = vi.fn();
      render(
        <PickerProvider>
          <DateField defaultValue={dayjs('2023-12-25')} onChange={onChange} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '01/01/2024' } });

      expect(onChange).toHaveBeenCalled();
    });
  });

  describe('endDecorator', () => {
    it('should render endDecorator', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} endDecorator={<span data-testid="custom-icon">🗓️</span>} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });

    it('should render both clear button and endDecorator', () => {
      render(
        <PickerProvider>
          <DateField
            {...defaultProps}
            value={dayjs('2023-05-15')}
            clearable
            endDecorator={<span data-testid="custom-icon">🗓️</span>}
          />
        </PickerProvider>,
      );

      expect(screen.getByTestId('clear-button')).toBeInTheDocument();
      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('should have proper accessibility attributes', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} id="date-field" />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('id', 'date-field');
      expect(input).toHaveAttribute('autoComplete', 'off');
    });

    it('should associate label with input', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} label="Select Date" />
        </PickerProvider>,
      );

      const input = screen.getByLabelText('Select Date');
      expect(input).toBeInTheDocument();
    });
  });

  describe('slots and slotProps', () => {
    it('should forward custom className', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} className="custom-field" />
        </PickerProvider>,
      );

      // Check that the custom class is applied to the component
      const container = screen.getByRole('group');
      expect(container).toHaveClass('custom-field');
    });

    it('should apply slotProps to input', () => {
      render(
        <PickerProvider>
          <DateField
            {...defaultProps}
            slotProps={{
              input: { className: 'custom-input-class' },
            }}
          />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      // Just verify the component renders with slotProps (implementation may vary)
      expect(input).toHaveClass('NovaInput-input');
    });
  });

  describe('ref forwarding', () => {
    it('should forward ref correctly', () => {
      const ref = React.createRef<HTMLDivElement>();

      render(
        <PickerProvider>
          <DateField {...defaultProps} ref={ref} />
        </PickerProvider>,
      );

      // Check that the component renders correctly with ref
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('edge cases', () => {
    it('should handle invalid input gracefully', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: 'invalid-date' } });

      // Should not crash
      expect(input).toBeInTheDocument();
    });

    it('should handle empty string input', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '' } });

      expect(defaultProps.onChange).toHaveBeenCalledWith(null);
    });

    it('should handle partial date input', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '12/25' } });

      // Should not crash
      expect(input).toBeInTheDocument();
    });
  });

  describe('format patterns', () => {
    it('should handle different format patterns', () => {
      const formats = ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD', 'MMM D, YYYY'];

      formats.forEach((format) => {
        const { unmount } = render(
          <PickerProvider>
            <DateField {...defaultProps} format={format} />
          </PickerProvider>,
        );

        expect(screen.getByRole('textbox')).toBeInTheDocument();
        unmount();
      });
    });
  });

  describe('performance', () => {
    it('should not re-render unnecessarily', () => {
      const { rerender } = render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const initialInput = screen.getByRole('textbox');

      // Re-render with same props
      rerender(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      // Input should still be there
      expect(initialInput).toBeInTheDocument();
    });

    it('should handle rapid input changes efficiently', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');

      // Rapid typing simulation
      fireEvent.change(input, { target: { value: '1' } });
      fireEvent.change(input, { target: { value: '12' } });
      fireEvent.change(input, { target: { value: '12/' } });
      fireEvent.change(input, { target: { value: '12/2' } });
      fireEvent.change(input, { target: { value: '12/25' } });
      fireEvent.change(input, { target: { value: '12/25/2023' } });

      // Should handle all changes
      expect(defaultProps.onChange).toHaveBeenCalledTimes(6);
    });
  });

  describe('advanced keyboard navigation', () => {
    it('should handle PageUp key for month increment', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'PageUp' });

      expect(defaultProps.onChange).toHaveBeenCalled();
      // Should increment the month
      const calledWith = defaultProps.onChange.mock.calls[0][0];
      expect(calledWith.month()).toBe(5); // May (4) -> June (5)
    });

    it('should handle PageDown key for month decrement', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'PageDown' });

      expect(defaultProps.onChange).toHaveBeenCalled();
      // Should decrement the month
      const calledWith = defaultProps.onChange.mock.calls[0][0];
      expect(calledWith.month()).toBe(3); // May (4) -> April (3)
    });

    it('should handle Home key', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'Home' });

      // Should not cause errors
      expect(input).toBeInTheDocument();
    });

    it('should handle End key', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'End' });

      // Should not cause errors
      expect(input).toBeInTheDocument();
    });

    it('should handle Ctrl+A for select all', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'a', ctrlKey: true });

      // Should not cause errors
      expect(input).toBeInTheDocument();
    });
  });

  describe('input masking and formatting', () => {
    it('should handle partial input with auto-formatting', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} format="MM/DD/YYYY" />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');

      // Type partial date
      fireEvent.change(input, { target: { value: '123' } });

      // Should attempt to format
      expect(defaultProps.onChange).toHaveBeenCalled();
    });

    it('should handle backspace in formatted input', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'Backspace' });

      // Should handle deletion
      expect(input).toBeInTheDocument();
    });

    it('should handle Delete key', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'Delete' });

      // Should handle deletion
      expect(input).toBeInTheDocument();
    });

    it('should handle number input filtering', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: 'abc123def' } });

      // Should filter out non-numeric characters
      expect(defaultProps.onChange).toHaveBeenCalled();
    });
  });

  describe('validation edge cases', () => {
    it('should validate against complex shouldDisableDate function', () => {
      const shouldDisableDate = (date: any) => {
        if (!date.isValid()) return true;
        return date.day() === 0 || date.day() === 6; // Weekends
      };

      render(
        <PickerProvider>
          <DateField {...defaultProps} shouldDisableDate={shouldDisableDate} value={dayjs('2023-05-14')} />
        </PickerProvider>,
      );

      // Should render without crashing
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should handle validation with multiple constraints', () => {
      const minDate = dayjs('2023-05-10');
      const maxDate = dayjs('2023-05-20');
      const shouldDisableDate = (date: any) => date.date() === 15;

      render(
        <PickerProvider>
          <DateField
            {...defaultProps}
            minDate={minDate}
            maxDate={maxDate}
            shouldDisableDate={shouldDisableDate}
            disableFuture
            disablePast
          />
        </PickerProvider>,
      );

      // Should render without crashing
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should handle cross-year validation', () => {
      const minDate = dayjs('2022-12-31');
      const maxDate = dayjs('2024-01-01');

      render(
        <PickerProvider>
          <DateField {...defaultProps} minDate={minDate} maxDate={maxDate} />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('locale and internationalization', () => {
    it('should handle different date formats for different locales', () => {
      const formats = ['DD.MM.YYYY', 'YYYY年MM月DD日', 'DD MMM YYYY'];

      formats.forEach((format) => {
        const { unmount } = render(
          <PickerProvider>
            <DateField {...defaultProps} format={format} />
          </PickerProvider>,
        );

        expect(screen.getByRole('textbox')).toBeInTheDocument();
        unmount();
      });
    });

    it('should handle RTL layout', () => {
      render(
        <div dir="rtl">
          <PickerProvider>
            <DateField {...defaultProps} />
          </PickerProvider>
        </div>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should handle long month names', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} format="DD MMMM YYYY" />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('error recovery', () => {
    it('should recover from invalid date gracefully', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');

      // Enter invalid date
      fireEvent.change(input, { target: { value: '99/99/9999' } });

      // Then enter valid date
      fireEvent.change(input, { target: { value: '12/25/2023' } });

      expect(defaultProps.onChange).toHaveBeenCalledTimes(2);
    });

    it('should handle format mismatch gracefully', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} format="DD/MM/YYYY" />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');

      // Enter US format in European field
      fireEvent.change(input, { target: { value: '12/25/2023' } });

      expect(defaultProps.onChange).toHaveBeenCalled();
    });
  });

  describe('focus management', () => {
    it('should handle focus with autoFocus', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} autoFocus />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveFocus();
    });

    it('should maintain cursor position during formatting', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox') as HTMLInputElement;

      // Set cursor position and type
      input.setSelectionRange(2, 2);
      fireEvent.change(input, { target: { value: '12/25/2023' } });

      expect(defaultProps.onChange).toHaveBeenCalled();
    });

    it('should handle focus trap during validation', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} error />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      fireEvent.blur(input);

      expect(input).toBeInTheDocument();
    });
  });

  describe('controlled vs uncontrolled', () => {
    it('should work as controlled component', () => {
      const { rerender } = render(
        <PickerProvider>
          <DateField {...defaultProps} value={dayjs('2023-05-15')} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/15/2023')).toBeInTheDocument();

      // Update value prop
      rerender(
        <PickerProvider>
          <DateField {...defaultProps} value={dayjs('2023-06-20')} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('06/20/2023')).toBeInTheDocument();
    });

    it('should work as uncontrolled component with defaultValue', () => {
      render(
        <PickerProvider>
          <DateField defaultValue={dayjs('2023-05-15')} format="MM/DD/YYYY" onChange={vi.fn()} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/15/2023')).toBeInTheDocument();
    });

    it('should transition from uncontrolled to controlled', () => {
      const { rerender } = render(
        <PickerProvider>
          <DateField defaultValue={dayjs('2023-05-15')} format="MM/DD/YYYY" onChange={vi.fn()} />
        </PickerProvider>,
      );

      // Now control it
      rerender(
        <PickerProvider>
          <DateField value={dayjs('2023-06-20')} format="MM/DD/YYYY" onChange={vi.fn()} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('06/20/2023')).toBeInTheDocument();
    });
  });

  describe('accessibility enhancements', () => {
    it('should have proper ARIA live region for validation', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} value={dayjs('invalid')} />
        </PickerProvider>,
      );

      // Should render without crashing
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should support ARIA describedby for helper text', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} helperText="Choose your birth date" />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
    });

    it('should support high contrast mode', () => {
      render(
        <PickerProvider>
          <DateField {...defaultProps} error />
        </PickerProvider>,
      );

      // Check for error state indication on the input
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-invalid', 'true');
    });
  });

  describe('integration with form libraries', () => {
    it('should work with form validation libraries', () => {
      const validate = vi.fn();
      render(
        <PickerProvider>
          <DateField {...defaultProps} onBlur={validate} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.blur(input);

      expect(validate).toHaveBeenCalled();
    });

    it('should support field registration patterns', () => {
      const register = vi.fn();
      render(
        <PickerProvider>
          <DateField {...defaultProps} name="birthDate" ref={register} />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toHaveAttribute('name', 'birthDate');
    });
  });
});
