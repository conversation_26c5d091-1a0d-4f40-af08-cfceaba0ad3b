import React, { ElementType } from 'react';
import { OverrideProps } from '@mui/types';
import { SlotComponentProps } from '@mui/utils/types';
import { TextFieldProps } from '../../TextField';
import { PickerDateType } from '../models/pickers';
import { BaseDateValidationProps, DayValidationProps } from '../models/validation';
import { BasePickerInputProps } from '../DatePicker/shared';

export interface DateFieldPropsColorOverrides {}

/**
 * Slots for the DateField component
 */
export interface DateFieldSlots {
  /**
   * The component that renders the root.
   * @default TextField
   */
  root?: React.ElementType;
}

/**
 * Slot props for the DateField component
 */
export interface DateFieldSlotProps {
  /**
   * Props for the root slot.
   */
  root?: SlotComponentProps<React.ElementType, Record<string, unknown>, DateFieldOwnerState>;

  /**
   * Props for the input slot.
   */
  input?: SlotComponentProps<React.ElementType, Record<string, unknown>, DateFieldOwnerState>;
}

export interface DateFieldOwnerState extends DateFieldComponentProps {
  disabled?: boolean;
  error?: boolean;
}

export interface DateFieldComponentProps
  extends BaseDateValidationProps,
    DayValidationProps,
    BasePickerInputProps<PickerDateType | null> {
  /**
   * Callback fired when the component receives focus.
   */
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;

  /**
   * The format the date is displayed in.
   * @default 'MM/DD/YYYY'
   */
  format?: string;

  /**
   * If true, a clear button will be displayed when a date is selected.
   * @default true
   */
  clearable?: boolean;
}

export interface DateFieldTypeMap<P = object, D extends ElementType = 'div'> {
  props: P &
    DateFieldComponentProps &
    Omit<TextFieldProps, 'value' | 'defaultValue' | 'onChange' | 'onFocus' | 'slots' | 'slotProps'> & {
      /**
       * Overridable component slots.
       * @default {}
       */
      slots?: DateFieldSlots;
      /**
       * The props used for each component slot.
       * @default {}
       */
      slotProps?: DateFieldSlotProps;
    };
  defaultComponent: D;
}

export type DateFieldProps<D extends ElementType = DateFieldTypeMap['defaultComponent']> = OverrideProps<
  DateFieldTypeMap<object, D>,
  D
> & {
  component?: D;
};
