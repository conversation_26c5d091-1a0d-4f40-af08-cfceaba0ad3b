'use client';
import React from 'react';
import { OverridableComponent } from '@mui/types';
import {
  unstable_useId as useId,
  unstable_composeClasses as composeClasses,
  unstable_useForkRef as useForkRef,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import clsx from 'clsx';
import { TextField } from '../../TextField';
import { DateFieldOwnerState, DateFieldProps, DateFieldTypeMap } from './DateField.types';
import { ClearIcon } from '../icons';
import { getDateFieldUtilityClass } from './DateField.classes';
import { useFieldIntegration } from '../hooks/useFieldHelpers/useFieldIntegration';

// Utility function to create CSS classes for the component
const useUtilityClasses = (ownerState: DateFieldOwnerState) => {
  const { error, disabled } = ownerState;

  const slots = {
    root: ['root', error && 'error', disabled && 'disabled'],
    input: ['input'],
  };

  return composeClasses(slots, getDateFieldUtilityClass, {});
};

export const DateField = React.forwardRef(function DateField(
  props: DateFieldProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const {
    className,
    component,
    slots = {},
    slotProps = {},
    label,
    helperText,
    id: idOverride,
    name,
    value: valueProp,
    defaultValue,
    onChange,
    onFocus,
    disabled = false,
    error: errorProp = false,
    required = false,
    readOnly = false,
    format = 'MM/DD/YYYY',
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    clearable = true,
    autoComplete = 'off',
    size = 'medium',
    endDecorator,
    ...other
  } = props;

  const handleRef = useForkRef(ref, null);

  // Use our integrated hook
  const {
    rootRef,
    inputRef,
    inputValue,
    error: fieldError,
    handleInputClick,
    handleInputFocus,
    handleInputBlur,
    handleInputChange,
    handleKeyDown,
    handlePaste,
    handleClear,
    rootAriaAttributes,
    dateValue,
  } = useFieldIntegration({
    value: valueProp,
    defaultValue,
    onChange,
    onFocus,
    format,
    minDate,
    maxDate,
    shouldDisableDate,
    disableFuture,
    disablePast,
    disabled,
    readOnly,
    required,
  });

  const ownerState: DateFieldOwnerState = {
    ...props,
    disabled,
    error: errorProp || !!fieldError,
  };

  const id = useId(idOverride);
  const classes = useUtilityClasses(ownerState);

  // Define slot components
  const SlotRoot = slots.root ?? TextField;

  // Create slot props using useSlotProps
  const rootProps = useSlotProps({
    elementType: TextField,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      component,
      id,
      name,
      label,
      value: inputValue || '',
      placeholder: format,
      helperText: fieldError || helperText,
      disabled,
      error: errorProp || !!fieldError,
      required,
      readOnly,
      endDecorator: (
        <>
          {valueProp && !readOnly && !disabled && clearable && (
            <div
              onClick={handleClear}
              className="DateField-clearButton"
              aria-label="Clear date"
              data-testid="clear-button"
            >
              <ClearIcon />
            </div>
          )}
          {endDecorator}
        </>
      ),
      className: clsx(classes.root, className),
      onClick: handleInputClick,
      onFocus: handleInputFocus,
      onBlur: handleInputBlur,
      onKeyDown: handleKeyDown,
      onChange: handleInputChange,
      onPaste: handlePaste,
      autoComplete,
      slotProps: {
        input: {
          ref: inputRef,
          className: clsx(classes.input),
          ...slotProps.input,
        },
      },
      ref: handleRef,
      ...rootAriaAttributes,
    },
    ownerState,
  });

  return <SlotRoot {...rootProps} />;
}) as OverridableComponent<DateFieldTypeMap>;
