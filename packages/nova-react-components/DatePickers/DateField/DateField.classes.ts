import {
  unstable_generateUtilityClass as generateUtilityClass,
  unstable_generateUtilityClasses as generateUtilityClasses,
} from '@mui/utils';

export interface DateFieldClasses {
  /** Styles applied to the root element */
  root: string;
  /** Styles applied to the input element */
  input: string;
  /** Styles applied to the clear button element */
  clearButton: string;
  /** Styles applied to the error state */
  error: string;
  /** Styles applied to the disabled state */
  disabled: string;
  /** Styles applied to the clearable state */
  clearable: string;
  /** Styles applied to the small size variant */
  sizeSmall: string;
  /** Styles applied to the medium size variant */
  sizeMedium: string;
  /** Styles applied to the large size variant */
  sizeLarge: string;
}

export type DateFieldClassKey = keyof DateFieldClasses;

export function getDateFieldUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDateField', slot);
}

const dateFieldClasses: DateFieldClasses = generateUtilityClasses('NovaDateField', [
  'root',
  'input',
  'clearButton',
  'error',
  'disabled',
  'clearable',
  'sizeSmall',
  'sizeMedium',
  'sizeLarge',
]);

export default dateFieldClasses;
