'use client';
import React, { useCallback, useState } from 'react';
import { OverridableComponent } from '@mui/types';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { DialogRoot } from '../../Dialog';
import { DateRangeField } from '../DateRangeField/DateRangeField';
import { DateRangeCalendar } from '../DateRangeCalendar/DateRangeCalendar';
import {
  ModalDateRangePickerProps,
  ModalDateRangePickerOwnerState,
  ModalDateRangePickerTypeMap,
} from './ModalDateRangePicker.types';
import { getModalDateRangePickerUtilityClass } from './ModalDateRangePicker.classes';
import { Divider } from '../../Divider';
import { PickerViewHeader } from '../PickerViewHeader';
import { PickerViewFooter } from '../PickerViewFooter';
import { useDateRangePicker } from '../hooks/useDateRangePicker';
import { useDefaultDates, useNow, useUtils } from '../hooks';
import { unstable_createUseMediaQuery as createUseMediaQuery } from '@mui/system/useMediaQuery';
import { CalendarWeekDay } from '../DayCalendar';
import { DateField } from '../DateField';
import { PickerDateType } from '../models/pickers';
import { Box } from '../../Box';
import { IconButton } from '../../IconButton';
import { CalendarIcon } from '../icons';

const useMediaQuery = createUseMediaQuery();

const useUtilityClasses = (ownerState: ModalDateRangePickerOwnerState) => {
  const { disabled } = ownerState;

  const slots = {
    root: ['root', disabled && 'disabled'],
    dialog: ['dialog'],
    content: ['content'],
    header: ['header'],
    footer: ['footer'],
  };

  return composeClasses(slots, getModalDateRangePickerUtilityClass, {});
};

const ModalDateRangePickerRoot = styled('div')({
  display: 'inline-flex',
  flexDirection: 'column',
});

/**
 * Modal version of DateRangePicker component
 */
export const ModalDateRangePicker = React.forwardRef(function ModalDateRangePicker(
  props: ModalDateRangePickerProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const defaultDate = useDefaultDates();
  const {
    value,
    defaultValue,
    onChange,
    disabled = false,
    readOnly = false,
    onOpen,
    onClose,
    format = 'MM/DD/YYYY',
    label,
    calendars = 2,
    minDate = defaultDate.minDate,
    maxDate = defaultDate.maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    closeOnSelect = false,
    clearText = 'Clear',
    okText = 'OK',
    cancelText = 'Cancel',
    autoFocus = false,
    component,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const [isEditing, setIsEditing] = useState(false);
  const isSmall = useMediaQuery('(max-width:768px)', { noSsr: true });
  const utils = useUtils();
  const now = useNow();
  const handleRef = useForkRef(ref, null);

  // Use the date range picker hook
  const {
    selectedRange,
    updateRange,
    rangePosition,
    setRangePosition,
    open,
    handleOpen,
    currentView,
    availableViews,
    handleViewChange,
    handleAccept,
    handleCancel,
  } = useDateRangePicker({
    value,
    defaultValue,
    onChange,
    onOpen,
    onClose,
    disabled,
    readOnly,
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    format,
    closeOnSelect,
    autoFocus,
  });

  const ownerState: ModalDateRangePickerOwnerState = {
    ...props,
    disabled,
    readOnly,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? ModalDateRangePickerRoot;
  const SlotField = slots.field ?? DateRangeField;
  const SlotDialog = slots.dialog ?? DialogRoot;
  const SlotCalendar = slots.calendar ?? DateRangeCalendar;
  const SlotFooter = slots.footer ?? PickerViewFooter;
  const SlotHeader = slots.header ?? PickerViewHeader;

  const rootProps = useSlotProps({
    elementType: ModalDateRangePickerRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  const openPickerButton = (
    <IconButton variant="standard" onClick={handleOpen} disabled={disabled || readOnly}>
      {<CalendarIcon />}
    </IconButton>
  );

  const handleSwitchDateField = () => {
    setIsEditing((editing) => !editing);
  };

  const baseFieldProps = {
    format,
    disabled,
    readOnly,
    disableFuture,
    disablePast,
    maxDate,
    minDate,
  };

  const fieldProps = useSlotProps({
    elementType: DateRangeField,
    externalSlotProps: slotProps.field,
    additionalProps: {
      ...baseFieldProps,
      value: selectedRange,
      onChange: updateRange,
      label,
      onClick: handleOpen,
      endDecorator: openPickerButton,
    },
    ownerState,
  });

  const dialogProps = useSlotProps({
    elementType: DialogRoot,
    externalSlotProps: slotProps.dialog,
    additionalProps: {
      open,
      onClose: handleCancel,
      fullScreen: isSmall && !isEditing,
    },
    ownerState,
    className: classes.dialog,
  });

  const headerProps = useSlotProps({
    elementType: PickerViewHeader,
    externalSlotProps: slotProps.header,
    additionalProps: {
      days: selectedRange,
      label: label || isEditing ? 'Select dates' : 'Depart - return dates',
      view: currentView,
      views: availableViews,
      onViewChange: handleViewChange,
      disabled,
      readOnly,
      isEditing,
      onSwitchDateField: handleSwitchDateField,
    },
    ownerState,
    className: classes.header,
  });

  const calendarProps = useSlotProps({
    elementType: DateRangeCalendar,
    externalSlotProps: slotProps.calendar,
    additionalProps: {
      value: selectedRange,
      onChange: updateRange,
      rangePosition,
      onRangePositionChange: setRangePosition,
      calendars,
      disabled,
      readOnly,
      disableFuture,
      disablePast,
      minDate,
      maxDate,
      shouldDisableDate,
      variant: 'modal',
    },
    ownerState,
  });

  const footerProps = useSlotProps({
    elementType: PickerViewFooter,
    externalSlotProps: slotProps.footer,
    additionalProps: {
      onCancel: handleCancel,
      onAccept: handleAccept,
      disabled: disabled || readOnly,
      fullScreen: isSmall && !isEditing,
      clearText,
      cancelText,
      okText,
    },
    ownerState,
    className: classes.footer,
  });

  const handleChangeStartDate = useCallback(
    (newValue: PickerDateType) => {
      updateRange([newValue, value[1]]);
    },
    [updateRange, value],
  );

  const handleChangeEndDate = useCallback(
    (newValue: PickerDateType) => {
      updateRange([value[0], newValue]);
    },
    [updateRange, value],
  );

  return (
    <SlotRoot {...rootProps}>
      <SlotField style={{ minWidth: '260px' }} {...fieldProps} />
      <SlotDialog {...dialogProps}>
        <SlotHeader {...headerProps} />
        {!isEditing && <CalendarWeekDay utils={utils} now={now} />}
        <Divider />
        {!isEditing && <SlotCalendar {...calendarProps} />}
        {isEditing && (
          <Box sx={{ display: 'flex', gap: '8px', paddingInline: '24px', paddingBlock: '16px' }}>
            <DateField
              value={value[0]}
              onChange={handleChangeStartDate}
              label="Start"
              sx={{ width: '136px' }}
              {...baseFieldProps}
            />
            <DateField
              value={value[1]}
              onChange={handleChangeEndDate}
              label="End"
              sx={{ width: '136px' }}
              {...baseFieldProps}
            />
          </Box>
        )}
        {currentView === 'year' && <Divider />}
        {(currentView === 'day' || currentView === 'year') && <SlotFooter {...footerProps} />}
      </SlotDialog>
    </SlotRoot>
  );
}) as OverridableComponent<ModalDateRangePickerTypeMap>;
