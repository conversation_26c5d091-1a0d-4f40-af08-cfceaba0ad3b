import { ElementType } from 'react';
import { OverrideProps } from '@mui/types';
import {
  BaseDateRangePickerProps,
  BaseDateRangePickerSlots,
  BaseDateRangePickerSlotProps,
} from '../DateRangePicker/shared';
import { SlotComponentProps } from '@mui/utils/types';

/**
 * Slots for the ModalDateRangePicker component
 */
export interface ModalDateRangePickerSlots extends BaseDateRangePickerSlots {
  /**
   * Component used for the dialog.
   * @default Dialog
   */
  dialog?: React.ElementType;

  /**
   * Component used for the header.
   * @default PickerViewHeader
   */
  header?: React.ElementType;
}

/**
 * Slot props for the ModalDateRangePicker component
 */
export interface ModalDateRangePickerSlotProps extends BaseDateRangePickerSlotProps {
  /**
   * Props for the dialog slot.
   */
  dialog?: SlotComponentProps<React.ElementType, Record<string, unknown>, ModalDateRangePickerOwnerState>;

  /**
   * Props for the header slot.
   */
  header?: SlotComponentProps<React.ElementType, Record<string, unknown>, ModalDateRangePickerOwnerState>;
}

/**
 * Component props for the ModalDateRangePicker
 */
export interface ModalDateRangePickerComponentProps extends Omit<BaseDateRangePickerProps, 'slots' | 'slotProps'> {}

/**
 * Type map for ModalDateRangePicker
 */
export interface ModalDateRangePickerTypeMap<P = object, D extends ElementType = 'div'> {
  props: P &
    ModalDateRangePickerComponentProps & {
      /**
       * Overridable component slots.
       * @default {}
       */
      slots?: ModalDateRangePickerSlots;
      /**
       * The props used for each component slot.
       * @default {}
       */
      slotProps?: ModalDateRangePickerSlotProps;
    };
  defaultComponent: D;
}

/**
 * Props for the ModalDateRangePicker component
 */
export type ModalDateRangePickerProps<D extends ElementType = ModalDateRangePickerTypeMap['defaultComponent']> =
  OverrideProps<ModalDateRangePickerTypeMap<object, D>, D> & {
    component?: D;
  };

/**
 * Owner state for ModalDateRangePicker
 */
export type ModalDateRangePickerOwnerState = ModalDateRangePickerProps;
