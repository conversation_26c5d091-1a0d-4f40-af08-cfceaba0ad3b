import * as React from 'react';
import { PickerDateType } from '../models/pickers';

/**
 * Props exported by DatePickerDay for use by parent components
 */
export interface ExportedDatePickerDayProps {
  /**
   * If `true`, today's date is rendering without highlighting with circle.
   * @default false
   */
  disableHighlightToday?: boolean;
  /**
   * If `true`, days outside the current month are rendered:
   *
   * - if `fixedWeekNumber` is defined, renders days to have the weeks requested.
   *
   * - if `fixedWeekNumber` is not defined, renders day to fill the first and last week of the current month.
   *
   * - ignored if `calendars` equals more than `1` on range pickers.
   * @default false
   */
  showDaysOutsideCurrentMonth?: boolean;
  /**
   * If `true`, removes the margin from the first and last days of the month.
   * @default false
   */
  disableMargin?: boolean;
}

export interface DatePickerDayOwnerState {
  /** The date object for the day */
  day: PickerDateType;
  /** Whether the day is selected */
  isSelected: boolean;
  /** Whether the day is disabled */
  isDisabled: boolean;
  /** Whether the day is today */
  isToday: boolean;
  /** Whether the day is outside the current month */
  isOutsideCurrentMonth: boolean;
  /** Whether to remove the day margin */
  disableMargin: boolean;
  /** Whether to highlight today */
  disableHighlightToday: boolean;
  /** Whether to show days outside the current month */
  showDaysOutsideCurrentMonth: boolean;
  /** Whether the day is the first visible cell of the month */
  isFirstVisibleCell: boolean;
  /** Whether the day is the last visible cell of the month */
  isLastVisibleCell: boolean;
}

export interface DatePickerDayProps extends ExportedDatePickerDayProps, React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * The date to display.
   */
  day: PickerDateType;

  /**
   * Override or extend the styles applied to the component.
   */
  className?: string;

  /**
   * If `true`, day is selected.
   */
  selected?: boolean;

  /**
   * If `true`, day is disabled.
   */
  disabled?: boolean;

  /**
   * If `true`, day is outside current month.
   */
  outsideCurrentMonth?: boolean;

  /**
   * If `true`, day is the first visible cell of the month.
   */
  isFirstVisibleCell?: boolean;

  /**
   * If `true`, day is the last visible cell of the month.
   */
  isLastVisibleCell?: boolean;

  /**
   * Callback fired when a day is selected.
   */
  onDaySelect?: (day: PickerDateType) => void;

  /**
   * Additional components or nodes to render inside the day button.
   */
  children?: React.ReactNode;
}
