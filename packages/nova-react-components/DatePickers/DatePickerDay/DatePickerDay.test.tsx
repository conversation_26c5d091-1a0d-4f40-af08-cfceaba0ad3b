import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import dayjs from 'dayjs/esm';
import { DatePickerDay } from './DatePickerDay';
import { PickerProvider } from '../PickerContext';

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<DatePickerDay />', () => {
  const defaultProps = {
    day: dayjs('2023-05-15'),
    onDaySelect: vi.fn(),
    disabled: false,
    selected: false,
    outsideCurrentMonth: false,
    showDaysOutsideCurrentMonth: true,
    disableHighlightToday: false,
    disableMargin: false,
  };

  describe('rendering', () => {
    it('should render day button with correct text', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByText('15')).toBeInTheDocument();
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('should render custom children instead of default day text', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps}>Custom Content</DatePickerDay>
        </PickerProvider>,
      );

      expect(screen.getByText('Custom Content')).toBeInTheDocument();
      expect(screen.queryByText('15')).not.toBeInTheDocument();
    });

    it('should not render when outside current month and showDaysOutsideCurrentMonth is false', () => {
      const { container } = render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} outsideCurrentMonth showDaysOutsideCurrentMonth={false} />
        </PickerProvider>,
      );

      expect(container.firstChild).toBe(null);
    });

    it('should render when outside current month and showDaysOutsideCurrentMonth is true', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} outsideCurrentMonth showDaysOutsideCurrentMonth />
        </PickerProvider>,
      );

      expect(screen.getByText('15')).toBeInTheDocument();
    });
  });

  describe('selected state', () => {
    it('should render selected day with correct attributes', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} selected />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-selected', 'true');
      expect(button).toHaveAttribute('tabIndex', '0');
    });

    it('should render unselected day with correct attributes', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} selected={false} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-selected', 'false');
      expect(button).toHaveAttribute('tabIndex', '-1');
    });
  });

  describe('today highlighting', () => {
    it('should highlight today by default', () => {
      const today = dayjs();
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} day={today} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('NovaDatePickerDay-today');
    });

    it('should not highlight today when disableHighlightToday is true', () => {
      const today = dayjs();
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} day={today} disableHighlightToday />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).not.toHaveClass('NovaDatePickerDay-today');
    });

    it('should not highlight non-today dates', () => {
      const notToday = dayjs().add(1, 'day');
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} day={notToday} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).not.toHaveClass('NovaDatePickerDay-today');
    });
  });

  describe('disabled state', () => {
    it('should render disabled day', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} disabled />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveClass('Nova-disabled');
    });

    it('should not call onDaySelect when disabled day is clicked', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} disabled />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(defaultProps.onDaySelect).not.toHaveBeenCalled();
    });
  });

  describe('day selection', () => {
    it('should call onDaySelect when day is clicked', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(defaultProps.onDaySelect).toHaveBeenCalledTimes(1);
      expect(defaultProps.onDaySelect).toHaveBeenCalledWith(defaultProps.day);
    });

    it('should not call onDaySelect when onDaySelect is not provided', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} onDaySelect={undefined} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      // Should not throw error
      expect(button).toBeInTheDocument();
    });

    it('should prevent default on click', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      const event = new MouseEvent('click', { bubbles: true });
      const preventDefaultSpy = vi.spyOn(event, 'preventDefault');

      fireEvent(button, event);

      expect(preventDefaultSpy).toHaveBeenCalled();
    });
  });

  describe('outside current month styling', () => {
    it('should apply outside month class when outsideCurrentMonth is true', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} outsideCurrentMonth showDaysOutsideCurrentMonth />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('NovaDatePickerDay-outsideCurrentMonth');
    });

    it('should not apply outside month class when outsideCurrentMonth is false', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} outsideCurrentMonth={false} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).not.toHaveClass('NovaDatePickerDay-outsideCurrentMonth');
    });
  });

  describe('margin handling', () => {
    it('should apply no margin class when disableMargin is true', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} disableMargin />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('NovaDatePickerDay-noMargin');
    });

    it('should not apply no margin class when disableMargin is false', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} disableMargin={false} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).not.toHaveClass('NovaDatePickerDay-noMargin');
    });
  });

  describe('accessibility', () => {
    it('should have proper button type', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('type', 'button');
    });

    it('should handle focus correctly for selected day', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} selected />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('tabIndex', '0');
    });

    it('should handle focus correctly for unselected day', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} selected={false} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('tabIndex', '-1');
    });
  });

  describe('className handling', () => {
    it('should apply custom className', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} className="custom-class" />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class');
      expect(button).toHaveClass('NovaDatePickerDay-root');
    });

    it('should merge multiple classes correctly', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} selected className="custom-class" />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('NovaDatePickerDay-root');
      expect(button).toHaveClass('Nova-selected');
      expect(button).toHaveClass('custom-class');
    });
  });

  describe('ref forwarding', () => {
    it('should forward ref correctly', () => {
      const ref = React.createRef<HTMLButtonElement>();

      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} ref={ref} />
        </PickerProvider>,
      );

      expect(ref.current).toBeInstanceOf(HTMLButtonElement);
    });
  });

  describe('keyboard interaction', () => {
    it('should handle keydown events when provided', () => {
      const onKeyDown = vi.fn();
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} onKeyDown={onKeyDown} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      fireEvent.keyDown(button, { key: 'Enter' });

      expect(onKeyDown).toHaveBeenCalledTimes(1);
    });
  });

  describe('edge cases', () => {
    it('should handle leap year dates', () => {
      const leapDay = dayjs('2024-02-29');
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} day={leapDay} />
        </PickerProvider>,
      );

      expect(screen.getByText('29')).toBeInTheDocument();
    });

    it('should handle first day of month', () => {
      const firstDay = dayjs('2023-05-01');
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} day={firstDay} />
        </PickerProvider>,
      );

      expect(screen.getByText('1')).toBeInTheDocument();
    });

    it('should handle last day of month', () => {
      const lastDay = dayjs('2023-05-31');
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} day={lastDay} />
        </PickerProvider>,
      );

      expect(screen.getByText('31')).toBeInTheDocument();
    });

    it('should handle year boundaries', () => {
      const newYear = dayjs('2024-01-01');
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} day={newYear} />
        </PickerProvider>,
      );

      expect(screen.getByText('1')).toBeInTheDocument();
    });
  });

  describe('props handling', () => {
    it('should handle isFirstVisibleCell prop', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} isFirstVisibleCell />
        </PickerProvider>,
      );

      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('should handle isLastVisibleCell prop', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} isLastVisibleCell />
        </PickerProvider>,
      );

      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('should spread additional props to button element', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} data-testid="custom-day" title="Day 15" />
        </PickerProvider>,
      );

      const button = screen.getByTestId('custom-day');
      expect(button).toHaveAttribute('title', 'Day 15');
    });
  });

  describe('complex interactions', () => {
    it('should handle multiple prop combinations correctly', () => {
      render(
        <PickerProvider>
          <DatePickerDay
            {...defaultProps}
            selected
            disabled
            outsideCurrentMonth
            showDaysOutsideCurrentMonth
            disableMargin
            className="complex-day"
          />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveClass('NovaDatePickerDay-root');
      expect(button).toHaveClass('Nova-selected');
      expect(button).toHaveClass('Nova-disabled');
      expect(button).toHaveClass('NovaDatePickerDay-outsideCurrentMonth');
      expect(button).toHaveClass('NovaDatePickerDay-noMargin');
      expect(button).toHaveClass('complex-day');
    });

    it('should handle click events with keyboard modifiers', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      fireEvent.click(button, { shiftKey: true });

      expect(defaultProps.onDaySelect).toHaveBeenCalledWith(defaultProps.day);
    });

    it('should handle onKeyDown with different key combinations', () => {
      const onKeyDown = vi.fn();
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} onKeyDown={onKeyDown} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');

      fireEvent.keyDown(button, { key: 'Space' });
      expect(onKeyDown).toHaveBeenCalledTimes(1);

      fireEvent.keyDown(button, { key: 'Enter' });
      expect(onKeyDown).toHaveBeenCalledTimes(2);

      fireEvent.keyDown(button, { key: 'Tab' });
      expect(onKeyDown).toHaveBeenCalledTimes(3);
    });
  });

  describe('error handling and edge cases', () => {
    it('should handle extreme dates', () => {
      const extremeDate = dayjs('2099-12-31');
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} day={extremeDate} />
        </PickerProvider>,
      );

      expect(screen.getByText('31')).toBeInTheDocument();
    });

    it('should handle very old dates', () => {
      const oldDate = dayjs('1900-01-01');
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} day={oldDate} />
        </PickerProvider>,
      );

      expect(screen.getByText('1')).toBeInTheDocument();
    });

    it('should handle onDaySelect being undefined gracefully', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} onDaySelect={undefined} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');

      // Should not crash when clicking without onDaySelect
      expect(() => {
        fireEvent.click(button);
      }).not.toThrow();
    });
  });

  describe('performance and optimization', () => {
    it('should not re-render unnecessarily with same props', () => {
      const { rerender } = render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      const initialButton = screen.getByRole('button');

      // Re-render with same props
      rerender(
        <PickerProvider>
          <DatePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      const rerenderedButton = screen.getByRole('button');
      expect(rerenderedButton).toBeInTheDocument();
    });

    it('should handle rapid successive clicks', () => {
      render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');

      // Rapid clicks
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);

      expect(defaultProps.onDaySelect).toHaveBeenCalledTimes(3);
    });
  });

  describe('component state management', () => {
    it('should update when selected state changes', () => {
      const { rerender } = render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} selected={false} />
        </PickerProvider>,
      );

      let button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-selected', 'false');
      expect(button).toHaveAttribute('tabIndex', '-1');

      rerender(
        <PickerProvider>
          <DatePickerDay {...defaultProps} selected={true} />
        </PickerProvider>,
      );

      button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-selected', 'true');
      expect(button).toHaveAttribute('tabIndex', '0');
    });

    it('should update when disabled state changes', () => {
      const { rerender } = render(
        <PickerProvider>
          <DatePickerDay {...defaultProps} disabled={false} />
        </PickerProvider>,
      );

      let button = screen.getByRole('button');
      expect(button).not.toBeDisabled();

      rerender(
        <PickerProvider>
          <DatePickerDay {...defaultProps} disabled={true} />
        </PickerProvider>,
      );

      button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });
  });
});
