import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent, waitFor } from '@testing-library/react';
import { describe, expect, it, afterEach, vi, beforeEach } from 'vitest';
import dayjs from 'dayjs/esm';
import { YearListView } from './YearListView';
import { PickerProvider } from '../../PickerContext';

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<YearListView />', () => {
  const defaultProps = {
    date: dayjs('2023-05-15'),
    viewDate: dayjs('2023-05-15'),
    onChange: vi.fn(),
    onYearRangeChange: vi.fn(),
    disabled: false,
    readOnly: false,
    minDate: dayjs('1900-01-01'),
    maxDate: dayjs('2099-12-31'),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render year list correctly', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} data-testid="year-list-view" />
        </PickerProvider>,
      );

      const yearListView = screen.getByRole('listbox');
      expect(yearListView).toBeInTheDocument();
      expect(yearListView).toHaveAttribute('aria-label', 'Year selection');
    });

    it('should render years within minDate and maxDate range', () => {
      const minDate = dayjs('2020-01-01');
      const maxDate = dayjs('2025-12-31');

      render(
        <PickerProvider>
          <YearListView {...defaultProps} minDate={minDate} maxDate={maxDate} />
        </PickerProvider>,
      );

      expect(screen.getByText('2020')).toBeInTheDocument();
      expect(screen.getByText('2025')).toBeInTheDocument();
      expect(screen.queryByText('2019')).not.toBeInTheDocument();
      expect(screen.queryByText('2026')).not.toBeInTheDocument();
    });

    it('should highlight selected year', () => {
      const selectedDate = dayjs('2023-05-15');

      render(
        <PickerProvider>
          <YearListView {...defaultProps} date={selectedDate} />
        </PickerProvider>,
      );

      const selectedYear = screen.getByText('2023');
      expect(selectedYear).toHaveClass('selected');
      expect(selectedYear).toHaveAttribute('aria-selected', 'true');
    });

    it('should disable years based on shouldDisableYear function', () => {
      const shouldDisableYear = (year: any) => year.year() === 2022;

      render(
        <PickerProvider>
          <YearListView {...defaultProps} shouldDisableYear={shouldDisableYear} />
        </PickerProvider>,
      );

      const disabledYear = screen.getByText('2022');
      expect(disabledYear).toBeDisabled();
      expect(disabledYear).toHaveClass('disabled');
    });
  });

  describe('interaction', () => {
    it('should call onChange when year is clicked', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} />
        </PickerProvider>,
      );

      const year2024 = screen.getByText('2024');
      fireEvent.click(year2024);

      expect(defaultProps.onChange).toHaveBeenCalledWith(2024);
    });

    it('should not call onChange when disabled year is clicked', () => {
      const shouldDisableYear = (year: any) => year.year() === 2022;

      render(
        <PickerProvider>
          <YearListView {...defaultProps} shouldDisableYear={shouldDisableYear} />
        </PickerProvider>,
      );

      const disabledYear = screen.getByText('2022');
      fireEvent.click(disabledYear);

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });

    it('should not call onChange when component is disabled', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} disabled />
        </PickerProvider>,
      );

      const year2024 = screen.getByText('2024');
      fireEvent.click(year2024);

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });

    it('should not call onChange when component is readOnly', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} readOnly />
        </PickerProvider>,
      );

      const year2024 = screen.getByText('2024');
      fireEvent.click(year2024);

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });
  });

  describe('keyboard navigation', () => {
    it('should handle Enter key to select year', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} />
        </PickerProvider>,
      );

      const year2024 = screen.getByText('2024');
      year2024.focus();
      fireEvent.keyDown(year2024, { key: 'Enter' });

      expect(defaultProps.onChange).toHaveBeenCalledWith(2024);
    });

    it('should handle Space key to select year', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} />
        </PickerProvider>,
      );

      const year2024 = screen.getByText('2024');
      year2024.focus();
      fireEvent.keyDown(year2024, { key: ' ' });

      expect(defaultProps.onChange).toHaveBeenCalledWith(2024);
    });

    it('should handle ArrowDown to move to next year', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} />
        </PickerProvider>,
      );

      const year2023 = screen.getByText('2023');
      fireEvent.keyDown(year2023, { key: 'ArrowDown' });

      // Should focus next year (implementation sets focused year state)
      expect(screen.getByText('2024')).toBeInTheDocument();
    });

    it('should handle ArrowUp to move to previous year', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} />
        </PickerProvider>,
      );

      const year2023 = screen.getByText('2023');
      fireEvent.keyDown(year2023, { key: 'ArrowUp' });

      // Should focus previous year
      expect(screen.getByText('2022')).toBeInTheDocument();
    });

    it('should handle Home key to move to first year', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} />
        </PickerProvider>,
      );

      const year2023 = screen.getByText('2023');
      fireEvent.keyDown(year2023, { key: 'Home' });

      // Should focus first year (1900)
      expect(screen.getByText('1900')).toBeInTheDocument();
    });

    it('should handle End key to move to last year', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} />
        </PickerProvider>,
      );

      const year2023 = screen.getByText('2023');
      fireEvent.keyDown(year2023, { key: 'End' });

      // Should focus last year (2099)
      expect(screen.getByText('2099')).toBeInTheDocument();
    });
  });

  describe('scrolling behavior', () => {
    it('should handle scroll events without crashing', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} />
        </PickerProvider>,
      );

      const container = screen.getByRole('listbox');

      // Just test that scroll events don't crash the component
      expect(() => {
        fireEvent.scroll(container);
      }).not.toThrow();

      // Component should still be functional after scroll
      expect(container).toBeInTheDocument();
    });
  });

  describe('date constraints', () => {
    it('should disable past years when disablePast is true', () => {
      const currentYear = dayjs().year();

      render(
        <PickerProvider>
          <YearListView {...defaultProps} disablePast />
        </PickerProvider>,
      );

      const pastYear = screen.getByText((currentYear - 1).toString());
      expect(pastYear).toBeDisabled();
    });

    it('should disable future years when disableFuture is true', () => {
      const currentYear = dayjs().year();

      render(
        <PickerProvider>
          <YearListView {...defaultProps} disableFuture />
        </PickerProvider>,
      );

      const futureYear = screen.getByText((currentYear + 1).toString());
      expect(futureYear).toBeDisabled();
    });

    it('should disable years before minDate', () => {
      const minDate = dayjs('2020-01-01');

      render(
        <PickerProvider>
          <YearListView {...defaultProps} minDate={minDate} />
        </PickerProvider>,
      );

      expect(screen.queryByText('2019')).not.toBeInTheDocument();
    });

    it('should disable years after maxDate', () => {
      const maxDate = dayjs('2025-12-31');

      render(
        <PickerProvider>
          <YearListView {...defaultProps} maxDate={maxDate} />
        </PickerProvider>,
      );

      expect(screen.queryByText('2026')).not.toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} />
        </PickerProvider>,
      );

      const listbox = screen.getByRole('listbox');
      expect(listbox).toHaveAttribute('aria-label', 'Year selection');

      const yearOptions = screen.getAllByRole('option');
      expect(yearOptions.length).toBeGreaterThan(0);

      yearOptions.forEach((option) => {
        expect(option).toHaveAttribute('aria-selected');
      });
    });

    it('should manage tabIndex correctly for keyboard navigation', () => {
      const selectedDate = dayjs('2023-05-15');

      render(
        <PickerProvider>
          <YearListView {...defaultProps} date={selectedDate} />
        </PickerProvider>,
      );

      const selectedYear = screen.getByText('2023');
      expect(selectedYear).toHaveAttribute('tabIndex', '0');

      const otherYear = screen.getByText('2024');
      expect(otherYear).toHaveAttribute('tabIndex', '-1');
    });

    it('should have proper disabled states for accessibility', () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} disabled />
        </PickerProvider>,
      );

      const yearButtons = screen.getAllByRole('option');
      yearButtons.forEach((button) => {
        expect(button).toBeDisabled();
      });
    });
  });

  describe('focus management', () => {
    it('should maintain focus when year changes', async () => {
      render(
        <PickerProvider>
          <YearListView {...defaultProps} />
        </PickerProvider>,
      );

      const year2023 = screen.getByText('2023');
      year2023.focus();

      // Simulate focus change
      fireEvent.focus(year2023);

      expect(document.activeElement).toBe(year2023);
    });
  });
});
