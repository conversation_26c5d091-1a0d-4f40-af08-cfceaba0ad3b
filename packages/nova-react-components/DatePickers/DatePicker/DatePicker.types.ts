import {
  DockedDatePickerProps,
  DockedDatePickerSlots,
  DockedDatePickerSlotProps,
} from '../DockedDatePicker/DockedDatePicker.types';
import {
  ModalDatePickerProps,
  ModalDatePickerSlots,
  ModalDatePickerSlotProps,
} from '../ModalDatePicker/ModalDatePicker.types';
import { OverrideProps } from '@mui/types';

/**
 * Component slots for the DatePicker - inherits from both variants
 */
export interface DatePickerSlots extends DockedDatePickerSlots, ModalDatePickerSlots {}

/**
 * Props for the DatePicker slots - inherits from both variants
 */
export interface DatePickerSlotProps extends DockedDatePickerSlotProps, ModalDatePickerSlotProps {}

/**
 * Props for the main DatePicker component
 * Follows MUI's pattern of extending both desktop and mobile variants
 */
export interface DatePickerComponentProps extends DockedDatePickerProps, ModalDatePickerProps {
  /**
   * CSS media query when `Mobile` mode will be changed to `Desktop`.
   * @default '@media (pointer: fine)'
   * @example '@media (min-width: 720px)' or theme.breakpoints.up("sm")
   */
  desktopModeMediaQuery?: string;

  /**
   * Overridable component slots.
   * @default {}
   */
  slots?: DatePickerSlots;

  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps?: DatePickerSlotProps;
}

export interface DatePickerTypeMap {
  props: DatePickerComponentProps;
  defaultComponent: React.ElementType;
}

/**
 * DatePicker component props
 */
export type DatePickerProps<D extends React.ElementType = DatePickerTypeMap['defaultComponent']> = OverrideProps<
  DatePickerTypeMap,
  D
>;
