import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach, vi, beforeEach } from 'vitest';
import dayjs from 'dayjs/esm';
import { DatePicker } from './DatePicker';
import { PickerProvider } from '../PickerContext';

// Mock the media query hook
const mockUseMediaQuery = vi.fn();
vi.mock('@mui/system/useMediaQuery', () => ({
  unstable_createUseMediaQuery: () => () => mockUseMediaQuery(),
}));

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<DatePicker />', () => {
  const defaultProps = {
    value: dayjs('2023-05-15'),
    onChange: vi.fn(),
    label: 'Select Date',
    format: 'MM/DD/YYYY',
  };

  beforeEach(() => {
    mockUseMediaQuery.mockReturnValue(true); // Default to desktop (pointer: fine)
  });

  describe('media query switching', () => {
    it('should render ModalDatePicker on desktop (pointer: fine)', () => {
      mockUseMediaQuery.mockReturnValue(true);

      render(
        <PickerProvider>
          <DatePicker {...defaultProps} />
        </PickerProvider>,
      );

      // Look for the wrapper class that indicates ModalDatePicker
      const wrapper = document.querySelector('.NovaDatePicker-root');
      expect(wrapper).toBeInTheDocument();

      // Should have an input field with the formatted date
      const input = screen.getByRole('textbox');
      expect(input).toHaveDisplayValue('05/15/2023');
    });

    it('should render DockedDatePicker on mobile (pointer: coarse)', () => {
      mockUseMediaQuery.mockReturnValue(false);

      render(
        <PickerProvider>
          <DatePicker {...defaultProps} />
        </PickerProvider>,
      );

      // Look for the wrapper class that indicates DockedDatePicker
      const wrapper = document.querySelector('.NovaDockedDatePicker-root');
      expect(wrapper).toBeInTheDocument();

      // Should have an input field with the formatted date
      const input = screen.getByRole('textbox');
      expect(input).toHaveDisplayValue('05/15/2023');
    });

    it('should switch components when media query changes', () => {
      mockUseMediaQuery.mockReturnValue(true);

      const { rerender } = render(
        <PickerProvider>
          <DatePicker {...defaultProps} />
        </PickerProvider>,
      );

      // Should start with modal variant
      expect(document.querySelector('.NovaDatePicker-root')).toBeInTheDocument();

      mockUseMediaQuery.mockReturnValue(false);
      rerender(
        <PickerProvider>
          <DatePicker {...defaultProps} />
        </PickerProvider>,
      );

      // Should switch to docked variant
      expect(document.querySelector('.NovaDockedDatePicker-root')).toBeInTheDocument();
    });

    it('should call useMediaQuery hook', () => {
      render(
        <PickerProvider>
          <DatePicker {...defaultProps} />
        </PickerProvider>,
      );

      expect(mockUseMediaQuery).toHaveBeenCalledWith();
    });
  });

  describe('prop forwarding', () => {
    it('should forward value and format props', () => {
      const testDate = dayjs('2024-12-25');

      render(
        <PickerProvider>
          <DatePicker {...defaultProps} value={testDate} format="DD/MM/YYYY" />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveDisplayValue('25/12/2024');
    });

    it('should forward disabled prop', () => {
      render(
        <PickerProvider>
          <DatePicker {...defaultProps} disabled />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });

    it('should forward readOnly prop', () => {
      render(
        <PickerProvider>
          <DatePicker {...defaultProps} readOnly />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('readonly');
    });

    it('should forward placeholder prop', () => {
      render(
        <PickerProvider>
          <DatePicker {...defaultProps} value={null} placeholder="Custom placeholder" />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('placeholder', 'Custom placeholder');
    });

    it('should forward label prop', () => {
      render(
        <PickerProvider>
          <DatePicker {...defaultProps} label="Custom Label" />
        </PickerProvider>,
      );

      expect(screen.getByText('Custom Label')).toBeInTheDocument();
    });
  });

  describe('ref forwarding', () => {
    it('should forward ref to the component', () => {
      const ref = React.createRef<HTMLDivElement>();

      render(
        <PickerProvider>
          <DatePicker {...defaultProps} ref={ref} />
        </PickerProvider>,
      );

      expect(ref.current).toBeInstanceOf(HTMLElement);
      expect(ref.current).toHaveClass('NovaDatePicker-root');
    });

    it('should forward ref correctly on media query change', () => {
      mockUseMediaQuery.mockReturnValue(true);
      const ref = React.createRef<HTMLDivElement>();

      const { rerender } = render(
        <PickerProvider>
          <DatePicker {...defaultProps} ref={ref} />
        </PickerProvider>,
      );

      expect(ref.current).toHaveClass('NovaDatePicker-root');

      mockUseMediaQuery.mockReturnValue(false);
      rerender(
        <PickerProvider>
          <DatePicker {...defaultProps} ref={ref} />
        </PickerProvider>,
      );

      expect(ref.current).toHaveClass('NovaDockedDatePicker-root');
    });
  });

  describe('date handling', () => {
    it('should handle null values', () => {
      render(
        <PickerProvider>
          <DatePicker {...defaultProps} value={null} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      // When value is null, it should show the placeholder instead of empty string
      expect(input).toHaveDisplayValue(['MM/DD/YYYY']);
    });

    it('should format dates correctly', () => {
      const testDate = dayjs('2024-01-15');

      render(
        <PickerProvider>
          <DatePicker {...defaultProps} value={testDate} format="YYYY-MM-DD" />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveDisplayValue('2024-01-15');
    });
  });

  describe('integration', () => {
    it('should render calendar icon button', () => {
      render(
        <PickerProvider>
          <DatePicker {...defaultProps} />
        </PickerProvider>,
      );

      const calendarButton = screen.getByRole('button');
      expect(calendarButton).toBeInTheDocument();

      const calendarIcon = screen.getByTestId('CalendarIcon');
      expect(calendarIcon).toBeInTheDocument();
    });

    it('should handle rapid media query changes', () => {
      const { rerender } = render(
        <PickerProvider>
          <DatePicker {...defaultProps} />
        </PickerProvider>,
      );

      // Rapidly switch between variants
      for (let i = 0; i < 5; i++) {
        mockUseMediaQuery.mockReturnValue(i % 2 === 0);
        rerender(
          <PickerProvider>
            <DatePicker {...defaultProps} />
          </PickerProvider>,
        );
      }

      // Should still render correctly
      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
      expect(input).toHaveDisplayValue('05/15/2023');
    });

    it('should maintain date consistency across media query changes', () => {
      const testDate = dayjs('2024-06-20');
      mockUseMediaQuery.mockReturnValue(true);

      const { rerender } = render(
        <PickerProvider>
          <DatePicker {...defaultProps} value={testDate} />
        </PickerProvider>,
      );

      let input = screen.getByRole('textbox');
      expect(input).toHaveDisplayValue('06/20/2024');

      mockUseMediaQuery.mockReturnValue(false);
      rerender(
        <PickerProvider>
          <DatePicker {...defaultProps} value={testDate} />
        </PickerProvider>,
      );

      input = screen.getByRole('textbox');
      expect(input).toHaveDisplayValue('06/20/2024');
    });
  });
});
