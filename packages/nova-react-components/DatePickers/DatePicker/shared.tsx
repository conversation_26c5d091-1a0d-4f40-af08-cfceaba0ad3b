import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotProps } from '../../types/slot';
import { SxProps } from '../../types/theme';
import { PickerDateType } from '../models/pickers';
import { BasePickerProps, BasePickerTextProps } from '../types';
import { DateCalendarSlotProps, ExportedDateCalendarProps } from '../DateCalendar/DateCalendar.types';

/**
 * Base input props for all picker components
 */
export interface BasePickerInputProps<TValue> {
  /**
   * The selected value.
   * Used when the component is controlled.
   */
  value?: TValue;
  /**
   * The default selected value.
   * Used when the component is not controlled.
   */
  defaultValue?: TValue;
  /**
   * Callback fired when the value changes.
   * @param {TValue} value The new value.
   */
  onChange?: (value: TValue) => void;

  /**
   * Format string for displaying the date (uses dayjs format strings).
   * @default 'DD/MM/YYYY'
   */
  format?: string;
}

/**
 * Slots available for the base date picker
 */
export interface BaseDatePickerSlots {
  /**
   * Component used for the root element.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * Component used for the field.
   * @default DateField
   */
  field?: React.ElementType;
  /**
   * Custom component for the calendar.
   * @default DateCalendar
   */
  calendar?: React.ElementType;

  /**
   * Component used for the footer.
   * @default PickerViewFooter
   */
  footer?: React.ElementType;
}

/**
 * Slot props for the base date picker
 */
export interface BaseDatePickerSlotProps {
  root?: SlotProps<React.ElementType, object, BaseDatePickerOwnerState>;
  field?: SlotProps<React.ElementType, object, BaseDatePickerOwnerState>;
  calendar?: SlotProps<React.ElementType, object, BaseDatePickerOwnerState>;
  footer?: SlotProps<React.ElementType, object, BaseDatePickerOwnerState>;
}

/**
 * Base props for all date picker variants
 */
export interface BaseDatePickerProps
  extends BasePickerTextProps,
    BasePickerInputProps<PickerDateType | null>,
    ExportedDateCalendarProps,
    BasePickerProps {
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
}

/**
 * Owner state for base date picker
 */
export interface BaseDatePickerOwnerState extends BaseDatePickerProps {}
