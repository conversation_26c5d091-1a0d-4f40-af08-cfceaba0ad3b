'use client';
import React, { useCallback, useState } from 'react';
import { OverridableComponent } from '@mui/types';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { Dialog } from '../../Dialog';
import { CalendarIcon } from '../icons';
import { ModalDatePickerProps, ModalDatePickerOwnerState, ModalDatePickerTypeMap } from './ModalDatePicker.types';
import { Divider } from '../../Divider';
import { DateCalendar } from '../DateCalendar';
import { PickerViewHeader } from '../PickerViewHeader';
import { PickerViewFooter } from '../PickerViewFooter';
import { useDefaultDates } from '../hooks/useUtils';
import { DateField } from '../DateField';
import { IconButton } from '../../IconButton';
import { getModalDatePickerUtilityClass } from './ModalDatePicker.classes';
import { unstable_createUseMediaQuery as createUseMediaQuery } from '@mui/system/useMediaQuery';
import { Box } from '../../Box';
import { useDatePicker } from '../hooks/useDatePicker';

const useMediaQuery = createUseMediaQuery();

const useUtilityClasses = (ownerState: ModalDatePickerOwnerState) => {
  const { disabled } = ownerState;

  const slots = {
    root: ['root', disabled && 'disabled'],
    dialog: ['dialog'],
    field: ['field'],
    calendar: ['calendar'],
    footer: ['footer'],
    header: ['header'],
  };

  return composeClasses(slots, getModalDatePickerUtilityClass, {});
};

const ModalDatePickerRoot = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
}));

export const ModalDatePicker = React.forwardRef(function ModalDatePicker(
  props: ModalDatePickerProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  // Spread props with defaults
  const defaultDate = useDefaultDates();
  const {
    value,
    defaultValue,
    onChange,
    onClose,
    label = 'Date',
    placeholder = 'DD/MM/YYYY',
    disabled = false,
    format = 'DD/MM/YYYY',
    fullWidth = false,
    required = false,
    readOnly = false,
    minDate = defaultDate.minDate,
    maxDate = defaultDate.maxDate,
    disableFuture = false,
    disablePast = false,
    shouldDisableDate,
    firstDayOfWeek = 0, // 0 = Sunday, 1 = Monday, etc.
    clearText = 'Clear',
    cancelText = 'Cancel',
    okText = 'OK',
    className,
    autoFocus = false,
    onOpen,
    component,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const [isEditing, setIsEditing] = useState(false);
  const handleRef = useForkRef(ref, null);

  // Use the date picker hook
  const {
    dateState,
    open,
    handleOpen,
    currentView,
    availableViews,
    handleViewDateChange,
    handleDateChange,
    handleInputChange,
    handleViewChange,
    handleAccept,
    handleCancel,
  } = useDatePicker({
    value,
    defaultValue,
    onChange,
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    format,
    disabled,
    readOnly,
    autoFocus,
    closeOnSelect: false,
  });

  const ownerState: ModalDatePickerOwnerState = {
    ...props,
    disabled,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? ModalDatePickerRoot;
  const SlotField = slots.field ?? DateField;
  const SlotDialog = slots.dialog ?? Dialog.Root;
  const SlotCalendar = slots.calendar ?? DateCalendar;
  const SlotFooter = slots.footer ?? PickerViewFooter;
  const SlotHeader = slots.header ?? PickerViewHeader;

  const handlePickerIconClicked = useCallback(() => {
    handleOpen();
  }, [handleOpen]);

  const handleSwitchDateField = () => {
    setIsEditing((editing) => !editing);
  };

  const isSmall = useMediaQuery('(max-width:768px)', { noSsr: true });

  const openPickerButton = (
    <IconButton
      className="DateField-calendarIcon"
      variant="standard"
      onClick={handlePickerIconClicked}
      disabled={disabled || readOnly}
    >
      {<CalendarIcon />}
    </IconButton>
  );

  const baseFieldProps = {
    value: dateState.tempDate,
    onChange: handleInputChange,
    label,
    placeholder,
    format,
    disabled,
    fullWidth,
    required,
    readOnly,
    disableFuture,
    disablePast,
    maxDate,
    minDate,
    clearable: false,
  };

  const rootProps = useSlotProps({
    elementType: ModalDatePickerRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  const fieldProps = useSlotProps({
    elementType: DateField,
    externalSlotProps: slotProps.field,
    additionalProps: {
      ...baseFieldProps,
      onClick: handleOpen,
      endDecorator: openPickerButton,
    },
    ownerState,
    className: classes.field,
  });

  const dialogProps = useSlotProps({
    elementType: Dialog.Root,
    externalSlotProps: slotProps.dialog,
    additionalProps: {
      open,
      onClose: handleCancel,
      fullScreen: isSmall,
      'aria-labelledby': 'date-picker-dialog-title',
    },
    ownerState,
    className: classes.dialog,
  });

  const headerProps = useSlotProps({
    elementType: PickerViewHeader,
    externalSlotProps: slotProps.header,
    additionalProps: {
      days: [dateState.tempDate],
      label,
      view: currentView,
      views: availableViews,
      onViewChange: handleViewChange,
      disabled,
      readOnly,
      isEditing,
      onSwitchDateField: handleSwitchDateField,
    },
    ownerState,
    className: classes.header,
  });

  const calendarProps = useSlotProps({
    elementType: DateCalendar,
    externalSlotProps: slotProps.calendar,
    additionalProps: {
      view: currentView,
      date: dateState.tempDate,
      viewDate: dateState.selectedDate,
      onViewDateChange: handleViewDateChange,
      onDateChange: handleDateChange,
      onViewChange: handleViewChange,
      disableFuture,
      disablePast,
      shouldDisableDate,
      minDate,
      maxDate,
      disabled,
      readOnly,
      variant: 'modal',
    },
    ownerState,
    className: classes.calendar,
  });

  const footerProps = useSlotProps({
    elementType: PickerViewFooter,
    externalSlotProps: slotProps.footer,
    additionalProps: {
      onAccept: handleAccept,
      onCancel: handleCancel,
      fullScreen: isSmall && !isEditing,
      disabled: disabled || readOnly,
    },
    ownerState,
    className: classes.footer,
  });

  return (
    <SlotRoot {...rootProps}>
      <SlotField {...fieldProps} />

      <SlotDialog {...dialogProps}>
        <SlotHeader {...headerProps} />
        <Divider />

        {isEditing && (
          <Box sx={{ display: 'flex', gap: '8px', paddingInline: '24px', paddingBlock: '16px' }}>
            <DateField {...baseFieldProps} label="Date" sx={{ width: '260px' }} />
          </Box>
        )}
        {!isEditing && <SlotCalendar {...calendarProps} />}
        {currentView === 'year' && <Divider />}

        {(currentView === 'day' || currentView === 'year') && <SlotFooter {...footerProps} />}
      </SlotDialog>
    </SlotRoot>
  );
}) as OverridableComponent<ModalDatePickerTypeMap>;
