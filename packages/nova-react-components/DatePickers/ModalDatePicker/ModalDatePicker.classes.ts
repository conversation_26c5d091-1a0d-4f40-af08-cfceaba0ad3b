import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

/**
 * Interface for DatePicker CSS classes
 */
export interface ModalDatePickerClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the root element when disabled. */
  disabled: string;

  /** Styles applied to the dialog element. */
  dialog: string;

  /** Styles applied to the field element. */
  field: string;

  /** Styles applied to the calendar element. */
  calendar: string;

  /** Styles applied to the footer element. */
  footer: string;

  /** Styles applied to the header element. */
  header: string;

  /** Styles applied to the selected day/month/year button. */
  selected: string;

  /** Styles applied to the month view container. */
  monthView: string;

  /** Styles applied to the selected month item. */
  selectedMonth: string;

  /** Styles applied to the month button elements. */
  monthButton: string;

  /** Styles applied to the year view container. */
  yearView: string;

  /** Styles applied to the year button elements. */
  yearButton: string;

  /** Styles applied to the selected year item. */
  selectedYear: string;
}

/**
 * Type for DatePicker class keys
 */
export type ModalDatePickerClassKey = keyof ModalDatePickerClasses;

/**
 * Generates a utility class for the DatePicker component
 * @param {string} slot - The class slot name
 * @returns {string} The generated class name
 */
export function getModalDatePickerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDatePicker', slot);
}

/**
 * Generated utility classes for the DatePicker component
 */
const modalDatePickerClasses: ModalDatePickerClasses = generateUtilityClasses('NovaModalDatePicker', [
  'root',
  'disabled',
  'dialog',
  'field',
  'calendar',
  'footer',
  'header',
  'selected',
  'monthView',
  'selectedMonth',
  'monthButton',
  'yearView',
  'selectedYear',
  'yearButton',
]);

export default modalDatePickerClasses;
