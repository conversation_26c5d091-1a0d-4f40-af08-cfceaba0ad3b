import { ElementType, ReactNode } from 'react';
import { TextFieldProps } from '../../TextField';
import { BaseDatePickerProps, BaseDatePickerSlots, BaseDatePickerSlotProps } from '../DatePicker/shared';
import { SlotComponentProps } from '@mui/utils/types';
import { OverrideProps } from '@mui/types';

/**
 * Props specific to modal (mobile) date picker behavior
 */
export interface ModalOnlyPickerProps {
  /**
   * Placement of the popup.
   * @default 'bottom-start'
   */
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end';

  /**
   * Callback fired when the popup is opened.
   */
  onOpen?: () => void;

  /**
   * Call<PERSON> fired when the popup is closed.
   */
  onClose?: () => void;

  /**
   * Controls whether the popup is open.
   * @default false
   */
  open?: boolean;
}

/**
 * Slots for the ModalDatePicker component
 */
export interface ModalDatePickerSlots extends BaseDatePickerSlots {
  /**
   * Component used for the dialog.
   * @default Dialog
   */
  dialog?: React.ElementType;

  /**
   * Component used for the header.
   * @default PickerViewHeader
   */
  header?: React.ElementType;
}

/**
 * Slot props for the ModalDatePicker component
 */
export interface ModalDatePickerSlotProps extends BaseDatePickerSlotProps {
  /**
   * Props for the dialog slot.
   */
  dialog?: SlotComponentProps<React.ElementType, Record<string, unknown>, ModalDatePickerOwnerState>;

  /**
   * Props for the header slot.
   */
  header?: SlotComponentProps<React.ElementType, Record<string, unknown>, ModalDatePickerOwnerState>;
}

export interface ModalDatePickerComponentProps
  extends Omit<BaseDatePickerProps, 'slots' | 'slotProps'>,
    ModalOnlyPickerProps {}

export interface ModalDatePickerTypeMap<P = object, D extends ElementType = 'div'> {
  props: P &
    ModalDatePickerComponentProps & {
      /**
       * Overridable component slots.
       * @default {}
       */
      slots?: ModalDatePickerSlots;
      /**
       * The props used for each component slot.
       * @default {}
       */
      slotProps?: ModalDatePickerSlotProps;
    };
  defaultComponent: D;
}

/**
 * Props for the ModalDatePicker component
 */
export type ModalDatePickerProps<D extends ElementType = ModalDatePickerTypeMap['defaultComponent']> = OverrideProps<
  ModalDatePickerTypeMap<object, D>,
  D
> & {
  component?: D;
};

/**
 * Owner state for ModalDatePicker
 */
export type ModalDatePickerOwnerState = ModalDatePickerProps;
