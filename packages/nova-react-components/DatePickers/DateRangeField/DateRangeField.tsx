'use client';
import * as React from 'react';
import { OverridableComponent } from '@mui/types';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { DateRangeFieldOwnerState, DateRangeFieldProps, DateRangeFieldTypeMap } from './DateRangeField.types';
import { getDateRangeFieldUtilityClass } from './DateRangeField.classes';
import { useUtils } from '../hooks/useUtils';
import { PickerRangeValue } from '../utils/dateRangeUtils';
import { PickerDateType } from '../models/pickers';
import { TextField } from '../../TextField';

const useUtilityClasses = (ownerState: DateRangeFieldOwnerState) => {
  const { disabled, error, fullWidth } = ownerState;

  const slots = {
    root: ['root', disabled && 'disabled', error && 'error', fullWidth && 'fullWidth'],
    input: ['input'],
  };

  return composeClasses(slots, getDateRangeFieldUtilityClass, {});
};

export const DateRangeField = React.forwardRef(function DateRangeField(
  props: DateRangeFieldProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const {
    value,
    defaultValue,
    onChange,
    format = 'MM/DD/YYYY',
    separator = ' – ',
    startLabel = 'Start date',
    endLabel = 'End date',
    clearable = true,
    disabled = false,
    readOnly = false,
    fullWidth = false,
    required = false,
    error = false,
    helperText,
    placeholder,
    label,
    onClick,
    onFocus,
    onBlur,
    endDecorator,
    component = 'div',
    className,
    autoComplete = 'off',
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const utils = useUtils();
  const handleRef = useForkRef(ref, null);

  // Ensure displayValue is a valid range
  const ensureValidRange = (rangeValue: any): PickerRangeValue => {
    if (!rangeValue) return [null, null];
    if (!Array.isArray(rangeValue)) return [null, null];
    if (rangeValue.length !== 2) return [rangeValue[0] || null, null];
    return rangeValue as PickerRangeValue;
  };

  const displayValue = ensureValidRange(value || defaultValue);

  // Format the range for display
  const formatRangeValue = React.useCallback(
    (range: PickerRangeValue): string => {
      const [start, end] = range;

      if (!start && !end) {
        return '';
      }

      const startStr = start ? utils.formatByString(start, format) : '';
      const endStr = end ? utils.formatByString(end, format) : '';

      if (start && end) {
        return `${startStr}${separator}${endStr}`;
      } else if (start) {
        return startStr;
      } else if (end) {
        return endStr;
      }

      return '';
    },
    [utils, format, separator],
  );

  // Current formatted display value
  const [fieldValue, setFieldValue] = React.useState(() => formatRangeValue(displayValue));

  // Update field value when prop value changes
  React.useEffect(() => {
    setFieldValue(formatRangeValue(displayValue));
  }, [displayValue, formatRangeValue]);

  // Handle field input change
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setFieldValue(newValue);

    // Try to parse the range from the input
    const parseRangeValue = (str: string): PickerRangeValue => {
      if (!str.trim()) {
        return [null, null];
      }

      // Ensure separator is a string for splitting
      const separatorStr = typeof separator === 'string' ? separator : ' – ';
      const parts = str.split(separatorStr).map((part) => part.trim());

      if (parts.length === 2) {
        const [startStr, endStr] = parts;

        const parseDate = (dateStr: string): PickerDateType | null => {
          if (!dateStr) return null;
          try {
            const parsed = utils.parse(dateStr, format);
            return utils.isValid(parsed) ? parsed : null;
          } catch {
            return null;
          }
        };

        return [parseDate(startStr), parseDate(endStr)];
      } else if (parts.length === 1) {
        // Single date input - treat as start date
        const parseDate = (dateStr: string): PickerDateType | null => {
          if (!dateStr) return null;
          try {
            const parsed = utils.parse(dateStr, format);
            return utils.isValid(parsed) ? parsed : null;
          } catch {
            return null;
          }
        };

        return [parseDate(parts[0]), null];
      }

      return [null, null];
    };

    if (onChange) {
      const parsedRange = parseRangeValue(newValue);
      onChange(parsedRange);
    }
  };

  const ownerState: DateRangeFieldOwnerState = {
    ...props,
    disabled,
    readOnly,
    error,
    fullWidth,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? TextField;

  const rootProps = useSlotProps({
    elementType: TextField,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
      'data-testid': 'date-range-field',
      'data-label': label,
      'data-separator': separator,
      label: label || `${startLabel} ${separator} ${endLabel}`,
      value: fieldValue,
      onChange: handleChange,
      onFocus,
      onBlur,
      onClick,
      placeholder: placeholder || `${format}${typeof separator === 'string' ? separator : ' – '}${format}`,
      disabled,
      readOnly,
      required,
      error,
      helperText,
      fullWidth,
      endDecorator,
      autoComplete,
      slotProps: {
        ...slotProps,
        input: {
          'data-testid': 'date-input',
          ...slotProps.input,
        },
      },
    },
    ownerState,
    className: classes.root,
  });

  return <SlotRoot {...rootProps} />;
}) as OverridableComponent<DateRangeFieldTypeMap>;
