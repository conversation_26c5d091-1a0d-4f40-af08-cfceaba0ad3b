import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent, waitFor } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import dayjs from 'dayjs/esm';
import { DateRangeField } from './DateRangeField';
import { PickerProvider } from '../PickerContext';
import type { PickerRangeValue } from '../utils/dateRangeUtils';

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<DateRangeField />', () => {
  const defaultProps = {
    value: [dayjs('2023-05-15'), dayjs('2023-05-20')] as PickerRangeValue,
    onChange: vi.fn(),
    label: 'Date Range',
    format: 'MM/DD/YYYY',
    separator: ' – ',
    disabled: false,
    error: false,
    required: false,
    readOnly: false,
    clearable: true,
  };

  describe('rendering', () => {
    it('should render with default props', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/15/2023 – 05/20/2023')).toBeInTheDocument();
      expect(screen.getByLabelText('Date Range')).toBeInTheDocument();
    });

    it('should render without value', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('placeholder', 'MM/DD/YYYY – MM/DD/YYYY');
    });

    it('should render with single date (start only)', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[dayjs('2023-05-15'), null]} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/15/2023')).toBeInTheDocument();
    });

    it('should render with single date (end only)', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[null, dayjs('2023-05-20')]} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/20/2023')).toBeInTheDocument();
    });

    it('should render with custom format', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} format="DD/MM/YYYY" />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('15/05/2023 – 20/05/2023')).toBeInTheDocument();
    });

    it('should render with custom separator', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} separator=" to " />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/15/2023 to 05/20/2023')).toBeInTheDocument();
    });

    it('should render with helper text', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} helperText="Select date range" />
        </PickerProvider>,
      );

      expect(screen.getByText('Select date range')).toBeInTheDocument();
    });

    it('should render with placeholder when no value', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      expect(screen.getByPlaceholderText('MM/DD/YYYY – MM/DD/YYYY')).toBeInTheDocument();
    });
  });

  describe('user interactions', () => {
    it('should call onChange when input value changes', async () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '05/15/2023 – 05/20/2023' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalled();
      });
    });

    it('should handle single date input', async () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '05/15/2023' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalledWith([expect.any(Object), null]);
      });
    });

    it('should handle input click', () => {
      const onClick = vi.fn();
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} onClick={onClick} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      expect(onClick).toHaveBeenCalled();
    });

    it('should handle input focus', () => {
      const onFocus = vi.fn();
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} onFocus={onFocus} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.focus(input);

      expect(onFocus).toHaveBeenCalled();
    });

    it('should handle input blur', () => {
      const onBlur = vi.fn();
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} onBlur={onBlur} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.blur(input);

      expect(onBlur).toHaveBeenCalled();
    });

    it('should parse partial range input', async () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '05/15/2023 – ' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalled();
      });
    });
  });

  describe('range parsing', () => {
    it('should parse complete date range', async () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '05/15/2023 – 05/20/2023' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalledWith([expect.any(Object), expect.any(Object)]);
      });
    });

    it('should handle empty input', async () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalledWith([null, null]);
      });
    });

    it('should handle invalid date input', async () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: 'invalid – dates' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalledWith([null, null]);
      });
    });

    it('should handle different separators', async () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} separator=" to " value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '05/15/2023 to 05/20/2023' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalledWith([expect.any(Object), expect.any(Object)]);
      });
    });
  });

  describe('disabled state', () => {
    it('should disable input when disabled', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} disabled />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });

    it('should not call onChange when disabled', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} disabled />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '05/15/2023 – 05/20/2023' } });

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });
  });

  describe('readOnly state', () => {
    it('should make input readOnly when readOnly', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} readOnly />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('readonly');
    });

    it('should not call onChange when readOnly', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} readOnly />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '05/15/2023 – 05/20/2023' } });

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });
  });

  describe('error state', () => {
    it('should show error state when error is true', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} error />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-invalid', 'true');
    });

    it('should show error with helper text', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} error helperText="Invalid range" />
        </PickerProvider>,
      );

      expect(screen.getByText('Invalid range')).toBeInTheDocument();
    });
  });

  describe('required state', () => {
    it('should show required indicator when required', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} required />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toBeRequired();
    });
  });

  describe('fullWidth', () => {
    it('should apply fullWidth when specified', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} fullWidth />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('defaultValue', () => {
    it('should use defaultValue when uncontrolled', () => {
      render(
        <PickerProvider>
          <DateRangeField
            defaultValue={[dayjs('2023-12-25'), dayjs('2023-12-31')]}
            format="MM/DD/YYYY"
            onChange={vi.fn()}
          />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('12/25/2023 – 12/31/2023')).toBeInTheDocument();
    });

    it('should call onChange when defaultValue changes', () => {
      const onChange = vi.fn();
      render(
        <PickerProvider>
          <DateRangeField defaultValue={[dayjs('2023-12-25'), dayjs('2023-12-31')]} onChange={onChange} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '01/01/2024 – 01/07/2024' } });

      expect(onChange).toHaveBeenCalled();
    });
  });

  describe('endDecorator', () => {
    it('should render endDecorator', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} endDecorator={<span data-testid="custom-icon">📅</span>} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('should have proper accessibility attributes', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
    });

    it('should associate label with input', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} label="Select Date Range" />
        </PickerProvider>,
      );

      const input = screen.getByLabelText('Select Date Range');
      expect(input).toBeInTheDocument();
    });

    it('should support ARIA describedby for helper text', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} helperText="Choose your travel dates" />
        </PickerProvider>,
      );

      expect(screen.getByText('Choose your travel dates')).toBeInTheDocument();
    });
  });

  describe('slots and slotProps', () => {
    it('should forward custom className', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} className="custom-range-field" />
        </PickerProvider>,
      );

      // The className gets applied to the root container, check using a data-testid approach
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should apply slotProps to input', () => {
      render(
        <PickerProvider>
          <DateRangeField
            {...defaultProps}
            slotProps={{
              root: { className: 'custom-root-class' },
            }}
          />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('ref forwarding', () => {
    it('should forward ref correctly', () => {
      const ref = React.createRef<HTMLDivElement>();

      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} ref={ref} />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('edge cases', () => {
    it('should handle non-array value gracefully', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={'invalid' as any} />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should handle array with wrong length', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[dayjs('2023-05-15')] as any} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/15/2023')).toBeInTheDocument();
    });

    it('should handle invalid separator input', async () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '05/15/2023 05/20/2023' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalled();
      });
    });

    it('should handle empty string parts', async () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: ' – 05/20/2023' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalledWith([null, expect.any(Object)]);
      });
    });

    it('should handle complex separator', async () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} separator={<span> to </span>} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '05/15/2023 – 05/20/2023' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalled();
      });
    });
  });

  describe('format patterns', () => {
    it('should handle different format patterns', () => {
      const formats = ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD', 'MMM D, YYYY'];

      formats.forEach((format) => {
        const { unmount } = render(
          <PickerProvider>
            <DateRangeField {...defaultProps} format={format} />
          </PickerProvider>,
        );

        expect(screen.getByRole('textbox')).toBeInTheDocument();
        unmount();
      });
    });

    it('should handle ISO format', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} format="YYYY-MM-DD" />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('2023-05-15 – 2023-05-20')).toBeInTheDocument();
    });

    it('should handle verbose format', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} format="MMM D, YYYY" />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('May 15, 2023 – May 20, 2023')).toBeInTheDocument();
    });
  });

  describe('performance', () => {
    it('should not re-render unnecessarily', () => {
      const { rerender } = render(
        <PickerProvider>
          <DateRangeField {...defaultProps} />
        </PickerProvider>,
      );

      const initialInput = screen.getByRole('textbox');

      rerender(
        <PickerProvider>
          <DateRangeField {...defaultProps} />
        </PickerProvider>,
      );

      expect(initialInput).toBeInTheDocument();
    });

    it('should handle rapid input changes efficiently', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');

      fireEvent.change(input, { target: { value: '0' } });
      fireEvent.change(input, { target: { value: '05' } });
      fireEvent.change(input, { target: { value: '05/1' } });
      fireEvent.change(input, { target: { value: '05/15' } });
      fireEvent.change(input, { target: { value: '05/15/2023' } });

      expect(defaultProps.onChange).toHaveBeenCalled();
    });
  });

  describe('controlled vs uncontrolled', () => {
    it('should work as controlled component', () => {
      const { rerender } = render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[dayjs('2023-05-15'), dayjs('2023-05-20')]} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/15/2023 – 05/20/2023')).toBeInTheDocument();

      rerender(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[dayjs('2023-06-15'), dayjs('2023-06-20')]} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('06/15/2023 – 06/20/2023')).toBeInTheDocument();
    });

    it('should work as uncontrolled component with defaultValue', () => {
      render(
        <PickerProvider>
          <DateRangeField
            defaultValue={[dayjs('2023-05-15'), dayjs('2023-05-20')]}
            format="MM/DD/YYYY"
            onChange={vi.fn()}
          />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/15/2023 – 05/20/2023')).toBeInTheDocument();
    });
  });

  describe('validation scenarios', () => {
    it('should handle date validation', () => {
      render(
        <PickerProvider>
          <DateRangeField
            {...defaultProps}
            minDate={dayjs('2023-01-01')}
            maxDate={dayjs('2023-12-31')}
            value={[dayjs('2023-05-15'), dayjs('2023-05-20')]}
          />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should handle range validation', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} value={[dayjs('2023-05-20'), dayjs('2023-05-15')]} />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('locale and internationalization', () => {
    it('should handle different separators for different locales', () => {
      const separators = [' – ', ' to ', ' ~ ', ' | '];

      separators.forEach((separator) => {
        const { unmount } = render(
          <PickerProvider>
            <DateRangeField {...defaultProps} separator={separator} />
          </PickerProvider>,
        );

        expect(screen.getByRole('textbox')).toBeInTheDocument();
        unmount();
      });
    });

    it('should handle RTL layout', () => {
      render(
        <div dir="rtl">
          <PickerProvider>
            <DateRangeField {...defaultProps} />
          </PickerProvider>
        </div>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('integration scenarios', () => {
    it('should work with form validation libraries', () => {
      const validate = vi.fn();
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} onBlur={validate} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.blur(input);

      expect(validate).toHaveBeenCalled();
    });

    it('should support field registration patterns', () => {
      render(
        <PickerProvider>
          <DateRangeField {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });
});
