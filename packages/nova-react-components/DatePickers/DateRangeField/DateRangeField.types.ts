import React, { ElementType } from 'react';
import { OverrideProps } from '@mui/types';
import { SlotComponentProps } from '@mui/utils';
import { TextFieldProps } from '../../TextField';
import { SxProps } from '../../types/theme';
import { PickerDateType } from '../models/pickers';
import { PickerRangeValue } from '../utils/dateRangeUtils';
import { BaseDateValidationProps, DayValidationProps } from '../models/validation';
import { BasePickerInputProps } from '../DatePicker/shared';

/**
 * Props exported by DateRangeField for use by parent components
 */
export interface ExportedDateRangeFieldProps {
  /**
   * The separator between start and end dates in the field.
   * @default ' – '
   */
  separator?: React.ReactNode;
  /**
   * If `true`, the field allows clearing the range.
   * @default true
   */
  clearable?: boolean;
}

/**
 * Slots for DateRangeField component
 */
export interface DateRangeFieldSlots {
  /**
   * The component that renders the root field.
   * @default TextField
   */
  root?: React.ElementType;
}

/**
 * Slot props for DateRangeField component
 */
export interface DateRangeFieldSlotProps {
  root?: Omit<TextFieldProps, 'value' | 'onChange' | 'defaultValue'>;
  input?: React.ComponentProps<'input'>;
}

/**
 * Component props for DateRangeField
 */
export interface DateRangeFieldComponentProps
  extends ExportedDateRangeFieldProps,
    BaseDateValidationProps,
    DayValidationProps,
    BasePickerInputProps<PickerRangeValue | null> {
  /**
   * Label for the start date field.
   * @default 'Start date'
   */
  startLabel?: string;

  /**
   * Label for the end date field.
   * @default 'End date'
   */
  endLabel?: string;
}

/**
 * Type map for DateRangeField
 */
export interface DateRangeFieldTypeMap<P = object, D extends ElementType = 'div'> {
  props: P &
    DateRangeFieldComponentProps &
    Omit<TextFieldProps, 'value' | 'defaultValue' | 'onChange' | 'onFocus' | 'slots' | 'slotProps'> & {
      /**
       * The slots for customizing the component appearance.
       */
      slots?: DateRangeFieldSlots;
      /**
       * The props used for each slot.
       */
      slotProps?: DateRangeFieldSlotProps;
    };
  defaultComponent: D;
}

/**
 * Props for DateRangeField
 */
export type DateRangeFieldProps<D extends ElementType = DateRangeFieldTypeMap['defaultComponent']> = OverrideProps<
  DateRangeFieldTypeMap<object, D>,
  D
> & {
  component?: D;
};

/**
 * Owner state for DateRangeField
 */
export type DateRangeFieldOwnerState = DateRangeFieldProps;
