import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface DateCalendarClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the component when disabled. */
  disabled: string;
}

export type DateCalendarClassKey = keyof DateCalendarClasses;

export function getDateCalendarUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDateCalendar', slot, 'Nova');
}

const dateCalendarClasses: DateCalendarClasses = generateUtilityClasses(
  'NovaDateCalendar',
  ['root', 'disabled'],
  'Nova',
);

export default dateCalendarClasses;
