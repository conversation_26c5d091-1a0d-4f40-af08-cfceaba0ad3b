'use client';
import React from 'react';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { MonthCalendar } from '../MonthCalendar';
import { YearCalendar } from '../YearCalendar';
import { useNow, useUtils } from '../hooks/useUtils';
import { DateCalendarOwnerState, DateCalendarProps } from './DateCalendar.types';
import { getDateCalendarUtilityClass } from './DateCalendar.classes';
import { CalendarHeader } from '../CalendarHeader';
import { DayCalendar } from '../DayCalendar';
import { Divider } from '../../Divider';

const useUtilityClasses = (ownerState: DateCalendarOwnerState) => {
  const { disabled } = ownerState;

  const slots = {
    root: ['root'],
    disabled: disabled ? ['disabled'] : [],
  };

  return composeClasses(slots, getDateCalendarUtilityClass, {});
};

const Root = styled('div')<{ variant?: 'docked' | 'modal' }>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  minWidth: '328px',
  variants: [
    {
      props: { variant: 'modal' },
      style: {
        padding: '8px 0',
        '@media (max-width: 600px)': {
          paddingBottom: '64px',
          minWidth: '100%',
        },
      },
    },
    {
      props: { variant: 'docked' },
      style: {
        padding: '0',
      },
    },
  ],
}));

export const DateCalendar = React.forwardRef(function DateCalendar(
  props: DateCalendarProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const {
    date,
    viewDate,
    onDateChange,
    onViewDateChange,
    view,
    onViewChange,
    views = ['day', 'month', 'year'],
    disableFuture,
    disablePast,
    shouldDisableDate,
    minDate,
    maxDate,
    disabled = false,
    readOnly = false,
    variant = 'docked',
    viewStyle = 'grid',
    component,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const utils = useUtils();
  const now = useNow();
  const handleRef = useForkRef(ref, null);
  const ownerState = {
    ...props,
  };

  const classes = useUtilityClasses(ownerState);
  const SlotRoot = slots.root ?? Root;

  const rootProps = useSlotProps({
    elementType: Root,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
      variant,
    },
    ownerState,
    className: classes.root,
  });

  // Handlers for view-specific actions
  const handleMonthSelect = (month: number) => {
    // Create a date that combines the current day with the selected month
    const updatedDate = utils.setMonth(date ? date : now, month);
    // Update the actual date
    onDateChange(updatedDate);
    // Switch to day view if available after a small delay to ensure updates process
    if (view === 'month' && views.includes('day')) {
      setTimeout(() => {
        onViewChange('day');
      }, 10);
    }
  };

  const handleYearSelect = (year: number) => {
    // Create a date that preserves month/day with the new year
    const updatedDate = utils.setYear(date ? date : now, year);
    // Update the actual date
    onDateChange(updatedDate);

    if (view === 'year') {
      setTimeout(() => {
        onViewChange('day');
      }, 10);
    }
  };

  return (
    <SlotRoot {...rootProps}>
      <CalendarHeader
        date={date}
        viewDate={viewDate || now}
        view={view}
        variant={variant}
        onPreviousMonth={onViewDateChange}
        onNextMonth={onViewDateChange}
        onPreviousYear={onViewDateChange}
        onNextYear={onViewDateChange}
        onViewChange={onViewChange}
        disableFuture={disableFuture}
        disablePast={disablePast}
        disabled={disabled}
        readOnly={readOnly}
      />
      {view !== 'day' && viewStyle === 'list' && <Divider />}
      {view === 'day' && (
        <DayCalendar
          date={date}
          viewDate={viewDate || now}
          handleDateChange={onDateChange}
          disableFuture={disableFuture}
          disablePast={disablePast}
          shouldDisableDate={shouldDisableDate}
          disabled={disabled}
          readOnly={readOnly}
          minDate={minDate}
          maxDate={maxDate}
        />
      )}

      {view === 'month' && (
        <MonthCalendar
          date={date}
          viewDate={viewDate || now}
          onChange={handleMonthSelect}
          disabled={disabled}
          readOnly={readOnly}
          viewStyle={viewStyle}
          disableFuture={disableFuture}
          disablePast={disablePast}
          minDate={minDate}
          maxDate={maxDate}
        />
      )}

      {view === 'year' && (
        <YearCalendar
          date={date}
          viewDate={viewDate || now}
          onChange={handleYearSelect}
          onYearRangeChange={onDateChange}
          disabled={disabled}
          readOnly={readOnly}
          viewStyle={viewStyle}
          disableFuture={disableFuture}
          disablePast={disablePast}
          minDate={minDate}
          maxDate={maxDate}
        />
      )}
    </SlotRoot>
  );
});
