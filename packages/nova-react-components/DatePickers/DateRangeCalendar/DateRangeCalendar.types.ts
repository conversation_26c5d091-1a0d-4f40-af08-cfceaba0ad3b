import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotComponentProps } from '@mui/utils';
import { ExportedDateCalendarProps } from '../DateCalendar/DateCalendar.types';
import { PickerRangeValue, RangePosition } from '../utils/dateRangeUtils';
import { SxProps } from '../../types/theme';
import { DatePickerVariant } from '../types';

/**
 * Props exported by DateRangeCalendar for use by parent components
 * Follows MUI's pattern of inheriting from child component exports
 */
export interface ExportedDateRangeCalendarProps extends ExportedDateCalendarProps {
  /**
   * Number of calendars to display side by side.
   * @default 2
   */
  calendars?: 1 | 2 | 3;
  /**
   * Position of the current month in the calendar grid.
   * @default 1
   */
  currentMonthCalendarPosition?: 1 | 2 | 3;
}

/**
 * Slots for DateRangeCalendar component
 */
export interface DateRangeCalendarSlots {
  /**
   * The component used for the root element.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * Custom component for range calendar header.
   * @default RangeCalendarHeader
   */
  rangeCalendarHeader?: React.ElementType;
  /**
   * Custom component for day range picker.
   * @default DateRangePickerDay
   */
  day?: React.ElementType;
  /**
   * Custom component for month view.
   * @default MonthCalendar
   */
  monthCalendar?: React.ElementType;
  /**
   * Custom component for year view.
   * @default YearCalendar
   */
  yearCalendar?: React.ElementType;
}

/**
 * Slot props for DateRangeCalendar component
 */
export interface DateRangeCalendarSlotProps {
  root?: SlotComponentProps<'div', object, DateRangeCalendarOwnerState>;
  rangeCalendarHeader?: SlotComponentProps<React.ElementType, object, DateRangeCalendarOwnerState>;
  day?: SlotComponentProps<React.ElementType, object, DateRangeCalendarOwnerState>;
  monthCalendar?: SlotComponentProps<React.ElementType, object, DateRangeCalendarOwnerState>;
  yearCalendar?: SlotComponentProps<React.ElementType, object, DateRangeCalendarOwnerState>;
}

export interface DateRangeCalendarComponentProps extends ExportedDateRangeCalendarProps {
  /**
   * The selected range.
   */
  value?: PickerRangeValue;

  /**
   * The default selected range.
   */
  defaultValue?: PickerRangeValue;

  /**
   * Callback fired when the range changes.
   * @param {PickerRangeValue} range The new range value.
   */
  onChange?: (range: PickerRangeValue) => void;

  /**
   * The position in the range that is being edited (start or end).
   */
  rangePosition?: RangePosition;

  /**
   * The default range position for uncontrolled component.
   * @default 'start'
   */
  defaultRangePosition?: RangePosition;

  /**
   * Callback fired when the range position changes.
   * @param {RangePosition} position The new range position.
   */
  onRangePositionChange?: (position: RangePosition) => void;

  /**
   * Available range positions for selection.
   * @default ['start', 'end']
   */
  availableRangePositions?: RangePosition[];

  /**
   * The variant of the date picker.
   * @default 'docked'
   */
  variant?: DatePickerVariant;

  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
}

/**
 * Type map for DateRangeCalendar
 */
export interface DateRangeCalendarTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    DateRangeCalendarComponentProps & {
      /**
       * The slots for customizing the component appearance.
       */
      slots?: DateRangeCalendarSlots;
      /**
       * The props used for each slot.
       */
      slotProps?: DateRangeCalendarSlotProps;
    };
  defaultComponent: D;
}

/**
 * Props for DateRangeCalendar
 */
export type DateRangeCalendarProps<D extends React.ElementType = DateRangeCalendarTypeMap['defaultComponent']> =
  OverrideProps<DateRangeCalendarTypeMap<object, D>, D> & {
    component?: D;
  };

/**
 * Owner state for DateRangeCalendar
 */
export type DateRangeCalendarOwnerState = DateRangeCalendarProps;
