import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface CalendarHeaderClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the container element if `variant="docked"`. */
  variantDocked: string;
  /** Class name applied to the container element if `variant="modal"`. */
  variantModal: string;
  /** Class name applied to the month year section element. */
  monthYearSection: string;
  /** Class name applied to the selector button element. */
  selectorButton: string;
  /** Class name applied to the selector button when expanded. */
  expanded: string;
  /** Class name applied to the selector button when disabled. */
  disabled: string;
}

export type CalendarHeaderClassKey = keyof CalendarHeaderClasses;

export function getCalendarHeaderUtilityClass(slot: string): string {
  return generateUtilityClass('NovaCalendarHeader', slot, 'Nova');
}

const calendarHeaderClasses: CalendarHeaderClasses = generateUtilityClasses(
  'NovaCalendarHeader',
  ['root', 'variantDocked', 'variantModal', 'monthYearSection', 'selectorButton', 'expanded', 'disabled'],
  'Nova',
);

export default calendarHeaderClasses;
