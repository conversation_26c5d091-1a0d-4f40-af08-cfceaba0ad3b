import {
  unstable_generateUtilityClass as generateUtilityClass,
  unstable_generateUtilityClasses as generateUtilityClasses,
} from '@mui/utils';

export interface DockedDateRangePickerClasses {
  /** Styles applied to the root element. */
  root: string;

  /** Styles applied to the root element when disabled. */
  disabled: string;

  /** Styles applied to the popper element. */
  popper: string;

  /** Styles applied to the field element. */
  field: string;

  /** Styles applied to the calendar element. */
  calendar: string;

  /** Styles applied to the footer element. */
  footer: string;
}

export type DockedDateRangePickerClassKey = keyof DockedDateRangePickerClasses;

export function getDockedDateRangePickerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDockedDateRangePicker', slot);
}

export const dockedDateRangePickerClasses: DockedDateRangePickerClasses = generateUtilityClasses(
  'NovaDockedDateRangePicker',
  ['root', 'disabled', 'popper', 'content', 'field', 'calendar', 'footer'],
);
