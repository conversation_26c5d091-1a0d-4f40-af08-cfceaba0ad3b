'use client';
import * as React from 'react';
import { useState, useCallback } from 'react';
import { OverridableComponent } from '@mui/types';
import { styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses, unstable_useForkRef as useForkRef } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { DateRangeField } from '../DateRangeField/DateRangeField';
import { DateRangeCalendar } from '../DateRangeCalendar/DateRangeCalendar';
import {
  DockedDateRangePickerProps,
  DockedDateRangePickerOwnerState,
  DockedDateRangePickerTypeMap,
} from './DockedDateRangePicker.types';
import { ClickAwayListener } from '../../ClickAwayListener';
import { Popper } from '../../Popper';
import { PickerViewFooter } from '../PickerViewFooter';
import { getDockedDateRangePickerUtilityClass } from './DockedDateRangePicker.classes';
import { useDefaultDates } from '../hooks';
import { IconButton } from '../../IconButton';
import { CalendarIcon } from '../icons';
import { useDateRangePicker } from '../hooks/useDateRangePicker';

const useUtilityClasses = (ownerState: DockedDateRangePickerOwnerState) => {
  const { disabled } = ownerState;

  const slots = {
    root: ['root', disabled && 'disabled'],
    popper: ['popper'],
    field: ['field'],
    calendar: ['calendar'],
    footer: ['footer'],
  };

  return composeClasses(slots, getDockedDateRangePickerUtilityClass, {});
};

const DockedDateRangePickerRoot = styled('div')({
  display: 'inline-flex',
  flexDirection: 'column',
  position: 'relative',
});

const DockedDateRangePickerPopper = styled(Popper)({
  zIndex: 1400,
});

const DockedDateRangePickerPaper = styled('div')(({ theme }) => ({
  backgroundColor: theme.vars.palette.surfaceContainerHigh,
  boxShadow: theme.vars.shadows[4],
  borderRadius: '12px',
  overflow: 'hidden',
}));

/**
 * Docked version of DateRangePicker component
 */
export const DockedDateRangePicker = React.forwardRef(function DockedDateRangePicker(
  props: DockedDateRangePickerProps,
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const defaultDate = useDefaultDates();
  const {
    value,
    defaultValue,
    onChange,
    disabled = false,
    readOnly = false,
    calendars = 2,
    onOpen,
    onClose,
    format = 'MM/DD/YYYY',
    label,
    minDate = defaultDate.minDate,
    maxDate = defaultDate.maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    closeOnSelect = false,
    clearText = 'Clear',
    okText = 'OK',
    cancelText = 'Cancel',
    placement = 'bottom-start',
    autoFocus = false,
    component,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const handleRef = useForkRef(ref, null);
  // Use the date range picker hook
  const {
    selectedRange,
    updateRange,
    rangePosition,
    setRangePosition,
    open,
    handleOpen,
    handleClose,
    anchorEl,
    currentView,
    handleAccept,
    handleCancel,
    handleClear,
  } = useDateRangePicker({
    value,
    defaultValue,
    onChange,
    onClose,
    disabled,
    readOnly,
    minDate,
    maxDate,
    disableFuture,
    disablePast,
    shouldDisableDate,
    format,
    closeOnSelect,
    autoFocus,
  });

  const ownerState: DockedDateRangePickerOwnerState = {
    ...props,
    disabled,
    readOnly,
  };
  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? DockedDateRangePickerRoot;
  const SlotField = slots.field ?? DateRangeField;
  const SlotPopper = slots.popper ?? DockedDateRangePickerPopper;
  const SlotCalendar = slots.calendar ?? DateRangeCalendar;
  const SlotFooter = slots.footer ?? PickerViewFooter;

  const rootProps = useSlotProps({
    elementType: DockedDateRangePickerRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref: handleRef,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  const openPickerButton = (
    <IconButton variant="standard" onClick={handleOpen} disabled={disabled || readOnly}>
      {<CalendarIcon />}
    </IconButton>
  );

  const fieldProps = useSlotProps({
    elementType: DateRangeField,
    externalSlotProps: slotProps.field,
    additionalProps: {
      value: selectedRange,
      onChange: updateRange,
      format,
      disabled,
      readOnly,
      label,
      onClick: handleOpen,
      endDecorator: openPickerButton,
      style: { minWidth: '260px' },
    },
    ownerState,
    className: classes.field,
  });

  const popperProps = useSlotProps({
    elementType: DockedDateRangePickerPopper,
    externalSlotProps: slotProps.popper,
    additionalProps: {
      role: 'dialog',
      open,
      anchorEl,
      placement,
      disablePortal: false,
    },
    ownerState,
    className: classes.popper,
  });

  const calendarProps = useSlotProps({
    elementType: DateRangeCalendar,
    externalSlotProps: slotProps.calendar,
    additionalProps: {
      value: selectedRange,
      onChange: updateRange,
      rangePosition,
      onRangePositionChange: setRangePosition,
      calendars,
      disabled,
      readOnly,
      disableFuture,
      disablePast,
      minDate,
      maxDate,
      shouldDisableDate,
      variant: 'docked',
    },
    ownerState,
    className: classes.calendar,
  });

  const footerProps = useSlotProps({
    elementType: PickerViewFooter,
    externalSlotProps: slotProps.footer,
    additionalProps: {
      onCancel: handleCancel,
      onClear: handleClear,
      onAccept: handleAccept,
      clearText,
      cancelText,
      okText,
    },
    ownerState,
    className: classes.footer,
  });

  return (
    <SlotRoot {...rootProps}>
      <SlotField {...fieldProps} />
      {open && (
        <ClickAwayListener onClickAway={handleClose} data-testid="click-away-listener">
          <SlotPopper {...popperProps}>
            <DockedDateRangePickerPaper>
              <SlotCalendar {...calendarProps} />
              {currentView === 'day' && <SlotFooter {...footerProps} />}
            </DockedDateRangePickerPaper>
          </SlotPopper>
        </ClickAwayListener>
      )}
    </SlotRoot>
  );
}) as OverridableComponent<DockedDateRangePickerTypeMap>;
