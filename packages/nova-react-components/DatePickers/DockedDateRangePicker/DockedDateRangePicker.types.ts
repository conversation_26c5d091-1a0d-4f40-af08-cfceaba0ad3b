import { ElementType } from 'react';
import { OverrideProps } from '@mui/types';
import {
  BaseDateRangePickerProps,
  BaseDateRangePickerSlots,
  BaseDateRangePickerSlotProps,
} from '../DateRangePicker/shared';
import { SlotComponentProps } from '@mui/utils/types';

/**
 * Props specific to docked (desktop) date range picker behavior
 */
export interface DockedOnlyRangePickerProps {
  /**
   * Placement of the popup.
   * @default 'bottom-start'
   */
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end';
}

/**
 * Slots for the DockedDateRangePicker component
 */
export interface DockedDateRangePickerSlots extends BaseDateRangePickerSlots {
  /**
   * Component used for the popper.
   * @default Popper
   */
  popper?: React.ElementType;
}

/**
 * Slot props for the DockedDateRangePicker component
 */
export interface DockedDateRangePickerSlotProps extends BaseDateRangePickerSlotProps {
  /**
   * Props for the popper slot.
   */
  popper?: SlotComponentProps<React.ElementType, Record<string, unknown>, DockedDateRangePickerOwnerState>;
}

/**
 * Component props for the DockedDateRangePicker
 */
export interface DockedDateRangePickerComponentProps
  extends Omit<BaseDateRangePickerProps, 'slots' | 'slotProps'>,
    DockedOnlyRangePickerProps {}

/**
 * Type map for DockedDateRangePicker
 */
export interface DockedDateRangePickerTypeMap<P = object, D extends ElementType = 'div'> {
  props: P &
    DockedDateRangePickerComponentProps & {
      /**
       * Overridable component slots.
       * @default {}
       */
      slots?: DockedDateRangePickerSlots;
      /**
       * The props used for each component slot.
       * @default {}
       */
      slotProps?: DockedDateRangePickerSlotProps;
    };
  defaultComponent: D;
}

/**
 * Props for the DockedDateRangePicker component
 * Follows MUI's DesktopDateRangePicker pattern
 */
export type DockedDateRangePickerProps<D extends ElementType = DockedDateRangePickerTypeMap['defaultComponent']> =
  OverrideProps<DockedDateRangePickerTypeMap<object, D>, D> & {
    component?: D;
  };

/**
 * Owner state for DockedDateRangePicker
 */
export type DockedDateRangePickerOwnerState = DockedDateRangePickerProps;
