import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent, waitFor } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import dayjs from 'dayjs/esm';
import { DockedDateRangePicker } from './DockedDateRangePicker';
import { PickerProvider } from '../PickerContext';
import type { PickerRangeValue } from '../utils/dateRangeUtils';

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<DockedDateRangePicker />', () => {
  const defaultProps = {
    value: [dayjs('2023-05-15'), dayjs('2023-05-20')] as PickerRangeValue,
    onChange: vi.fn(),
    onClose: vi.fn(),
    label: 'Date Range',
    placeholder: 'MM/DD/YYYY – MM/DD/YYYY',
    disabled: false,
    format: 'MM/DD/YYYY',
    required: false,
    readOnly: false,
    disableFuture: false,
    disablePast: false,
    shouldDisableDate: undefined,
    placement: 'bottom-start' as const,
    clearText: 'Clear',
    okText: 'OK',
    cancelText: 'Cancel',
    className: '',
    autoFocus: false,
    closeOnSelect: false,
    onOpen: vi.fn(),
    slots: {},
    slotProps: {},
    calendars: 2 as const,
    separator: ' – ',
  };

  describe('rendering', () => {
    it('should render with default props', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/15/2023 – 05/20/2023')).toBeInTheDocument();
      expect(screen.getByLabelText('Date Range')).toBeInTheDocument();
    });

    it('should render without value', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('placeholder', 'MM/DD/YYYY – MM/DD/YYYY');
    });

    it('should render with partial range value', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} value={[dayjs('2023-05-15'), null]} />
        </PickerProvider>,
      );

      expect(screen.getByDisplayValue('05/15/2023')).toBeInTheDocument();
    });

    it('should render with calendar icon', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const calendarButton = screen.getByRole('button');
      expect(calendarButton).toBeInTheDocument();
    });

    it('should render with custom endDecorator', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker
            {...defaultProps}
            slotProps={{ field: { endDecorator: <span data-testid="custom-icon">📅</span> } }}
          />
        </PickerProvider>,
      );

      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });

    it('should render with custom separator', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} slotProps={{ field: { separator: ' to ' } }} />
        </PickerProvider>,
      );

      const field = screen.getByTestId('date-range-field');
      expect(field).toHaveAttribute('data-separator', ' to ');
    });
  });

  describe('popup behavior', () => {
    it('should open popup when input is clicked', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByTestId('popper')).toBeInTheDocument();
      });
    });

    it('should open popup when calendar icon is clicked', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const calendarButton = screen.getByRole('button');
      fireEvent.click(calendarButton);

      await waitFor(() => {
        expect(screen.getByTestId('popper')).toBeInTheDocument();
      });
    });

    it('should not open popup when disabled', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} disabled />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      const calendarButton = screen.getByRole('button');

      expect(input).toBeDisabled();
      expect(calendarButton).toBeDisabled();
    });

    it('should not open popup when readOnly', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} readOnly />
        </PickerProvider>,
      );

      const calendarButton = screen.getByRole('button');
      expect(calendarButton).toBeDisabled();
    });

    it('should close popup when clicking outside', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByTestId('popper')).toBeInTheDocument();
      });

      // Click cancel button to close popup
      const cancelButton = screen.getByTestId('cancel-button');
      fireEvent.click(cancelButton);

      await waitFor(() => {
        expect(screen.queryByTestId('popper')).not.toBeInTheDocument();
      });
    });

    it('should show calendar content when open', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-calendar')).toBeInTheDocument();
      });
    });
  });

  describe('date selection', () => {
    it('should call onChange when date range is modified', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      // Open popup
      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-calendar')).toBeInTheDocument();
      });

      // Select a different date - get all day 25 elements and click the first one
      const day25Elements = screen.getAllByText('25');
      if (day25Elements.length > 0) {
        fireEvent.click(day25Elements[0]);
        await waitFor(() => {
          expect(defaultProps.onChange).toHaveBeenCalled();
        });
      }
    });

    it('should call onChange when date range is typed in field', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '01/01/2023 – 01/15/2023' } });

      await waitFor(() => {
        expect(defaultProps.onChange).toHaveBeenCalled();
      });
    });

    it('should handle invalid date input', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: 'invalid-date-range' } });

      // Should not crash
      expect(input).toBeInTheDocument();
    });
  });

  describe('footer actions', () => {
    it('should show footer when calendar is open in day view', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByTestId('picker-view-footer')).toBeInTheDocument();
      });
    });

    it('should handle clear button functionality', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        const clearButton = screen.getByTestId('clear-button');
        fireEvent.click(clearButton);
        expect(defaultProps.onChange).toHaveBeenCalledWith([null, null]);
      });
    });

    it('should handle accept button functionality', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        const acceptButton = screen.getByTestId('accept-button');
        fireEvent.click(acceptButton);
      });

      // Should close the popup
      await waitFor(() => {
        expect(screen.queryByTestId('popper')).not.toBeInTheDocument();
      });
    });

    it('should handle cancel button functionality', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        const cancelButton = screen.getByTestId('cancel-button');
        fireEvent.click(cancelButton);
      });

      // Should close the popup
      await waitFor(() => {
        expect(screen.queryByTestId('popper')).not.toBeInTheDocument();
      });
    });
  });

  describe('calendar configuration', () => {
    it('should render with custom number of calendars', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} calendars={3} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        const calendar = screen.getByTestId('date-range-calendar');
        expect(calendar).toHaveAttribute('data-calendars', '3');
      });
    });

    it('should pass date constraints to calendar', async () => {
      const minDate = dayjs('2023-01-01');
      const maxDate = dayjs('2023-12-31');

      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} minDate={minDate} maxDate={maxDate} disableFuture disablePast />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-calendar')).toBeInTheDocument();
      });
    });
  });

  describe('popper configuration', () => {
    it('should render with custom placement', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} placement="top-end" />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        const popper = screen.getByTestId('popper');
        expect(popper).toHaveAttribute('data-placement', 'top-end');
      });
    });

    it('should handle portal configuration', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} slotProps={{ popper: { disablePortal: true } }} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        const popper = screen.getByTestId('popper');
        expect(popper).toHaveAttribute('data-disable-portal', 'true');
      });
    });
  });

  describe('disabled states', () => {
    it('should disable all interactions when disabled', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} disabled />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      const button = screen.getByRole('button');

      expect(input).toBeDisabled();
      expect(button).toBeDisabled();
    });

    it('should disable calendar icon when readOnly', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} readOnly />
        </PickerProvider>,
      );

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });

    it('should respect disableFuture', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} disableFuture />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('should respect disablePast', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} disablePast />
        </PickerProvider>,
      );

      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('slots and slotProps', () => {
    it('should use custom slots', async () => {
      const CustomField = React.forwardRef<HTMLDivElement, any>((props, ref) => (
        <div ref={ref} data-testid="custom-field" {...props}>
          Custom Field
        </div>
      ));

      const CustomCalendar = React.forwardRef<HTMLDivElement, any>((props, ref) => (
        <div ref={ref} data-testid="custom-calendar" {...props}>
          Custom Calendar
        </div>
      ));

      render(
        <PickerProvider>
          <DockedDateRangePicker
            {...defaultProps}
            slots={{
              field: CustomField,
              calendar: CustomCalendar,
            }}
          />
        </PickerProvider>,
      );

      expect(screen.getByTestId('custom-field')).toBeInTheDocument();

      // Open popup to see custom calendar
      fireEvent.click(screen.getByTestId('custom-field'));

      await waitFor(() => {
        expect(screen.getByTestId('custom-calendar')).toBeInTheDocument();
      });
    });

    it('should apply custom slotProps', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker
            {...defaultProps}
            slotProps={{
              field: { 'data-field-test': 'field-value' },
              calendar: { 'data-calendar-test': 'calendar-value' },
              popper: { 'data-popper-test': 'popper-value' },
            }}
          />
        </PickerProvider>,
      );

      expect(screen.getByTestId('date-range-field')).toHaveAttribute('data-field-test', 'field-value');

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-calendar')).toHaveAttribute('data-calendar-test', 'calendar-value');
        expect(screen.getByTestId('popper')).toHaveAttribute('data-popper-test', 'popper-value');
      });
    });
  });

  describe('ref forwarding', () => {
    it('should forward ref correctly', () => {
      const ref = React.createRef<HTMLDivElement>();

      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} ref={ref} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('date-range-field')).toBeInTheDocument();
    });
  });

  describe('edge cases', () => {
    it('should handle undefined values gracefully', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} value={undefined} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('date-range-field')).toBeInTheDocument();
    });

    it('should handle partial range values', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} value={[dayjs('2023-05-15'), null]} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('date-range-field')).toBeInTheDocument();
    });

    it('should handle null range values', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} value={[null, null]} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('date-range-field')).toBeInTheDocument();
    });

    it('should handle invalid date values', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} value={[dayjs('invalid'), dayjs('2023-05-20')]} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('date-range-field')).toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('should have proper accessibility attributes', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      // Check if the input exists and is accessible
      expect(input).toBeInTheDocument();
    });

    it('should associate label with input', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} label="Travel Dates" />
        </PickerProvider>,
      );

      const input = screen.getByLabelText('Travel Dates');
      expect(input).toBeInTheDocument();
    });

    it('should support screen readers', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const calendarButton = screen.getByRole('button');
      expect(calendarButton).toBeInTheDocument();
    });
  });

  describe('performance', () => {
    it('should not render popup when closed', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.queryByTestId('popper')).not.toBeInTheDocument();
      expect(screen.queryByTestId('date-range-calendar')).not.toBeInTheDocument();
    });

    it('should handle rapid open/close efficiently', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');

      // Open
      fireEvent.click(input);
      await waitFor(() => {
        expect(screen.getByTestId('popper')).toBeInTheDocument();
      });

      // Close using cancel button
      const cancelButton = screen.getByTestId('cancel-button');
      fireEvent.click(cancelButton);
      await waitFor(() => {
        expect(screen.queryByTestId('popper')).not.toBeInTheDocument();
      });

      // Open again
      fireEvent.click(input);
      await waitFor(() => {
        expect(screen.getByTestId('popper')).toBeInTheDocument();
      });
    });
  });

  describe('custom button texts', () => {
    it('should use custom button texts', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} clearText="Reset" cancelText="Close" okText="Apply" />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByText('Reset')).toBeInTheDocument();
        expect(screen.getByText('Close')).toBeInTheDocument();
        expect(screen.getByText('Apply')).toBeInTheDocument();
      });
    });
  });

  describe('keyboard interactions', () => {
    it('should handle keyboard navigation in field', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'ArrowUp' });

      expect(input).toBeInTheDocument();
    });

    it('should handle escape key to close popup', async () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      const input = screen.getByRole('textbox');
      fireEvent.click(input);

      await waitFor(() => {
        expect(screen.getByTestId('popper')).toBeInTheDocument();
      });

      fireEvent.keyDown(input, { key: 'Escape' });

      // Component should handle escape key gracefully
      expect(input).toBeInTheDocument();
    });
  });

  describe('placement', () => {
    it('should render with different placements', async () => {
      const placements = ['bottom-start', 'bottom-end', 'top-start', 'top-end'] as const;

      for (const placement of placements) {
        const { unmount } = render(
          <PickerProvider>
            <DockedDateRangePicker {...defaultProps} placement={placement} />
          </PickerProvider>,
        );

        const input = screen.getByRole('textbox');
        fireEvent.click(input);

        await waitFor(() => {
          const popper = screen.getByTestId('popper');
          expect(popper).toHaveAttribute('data-placement', placement);
        });

        unmount();
      }
    });
  });

  describe('required', () => {
    it('should show required indicator', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} required />
        </PickerProvider>,
      );

      const field = screen.getByTestId('date-range-field');
      expect(field).toBeInTheDocument();
    });
  });

  describe('format', () => {
    it('should handle different date formats', () => {
      const formats = ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD'];

      formats.forEach((format) => {
        const { unmount } = render(
          <PickerProvider>
            <DockedDateRangePicker {...defaultProps} format={format} />
          </PickerProvider>,
        );

        expect(screen.getByRole('textbox')).toBeInTheDocument();
        unmount();
      });
    });
  });

  describe('fullWidth', () => {
    it('should render with fullWidth', () => {
      render(
        <PickerProvider>
          <DockedDateRangePicker {...defaultProps} fullWidth />
        </PickerProvider>,
      );

      const field = screen.getByTestId('date-range-field');
      expect(field).toBeInTheDocument();
    });
  });
});
