import { describe, expect, it, beforeEach } from 'vitest';
import dayjs from 'dayjs/esm';
import { DayjsAdapter } from './DayjsAdapter';

describe('DayjsAdapter', () => {
  let adapter: DayjsAdapter;

  beforeEach(() => {
    adapter = new DayjsAdapter();
  });

  describe('constructor', () => {
    it('should create adapter with default locale', () => {
      const adapter = new DayjsAdapter();
      expect(adapter.locale).toBeUndefined();
      expect(adapter.formats).toBeDefined();
    });

    it('should create adapter with custom locale', () => {
      const adapter = new DayjsAdapter({ locale: 'en' });
      expect(adapter.locale).toBe('en');
    });

    it('should handle invalid locale gracefully', () => {
      // Should not throw
      const adapter = new DayjsAdapter({ locale: 'invalid-locale' });
      expect(adapter.locale).toBe('invalid-locale');
    });
  });

  describe('date creation', () => {
    it('should create date from string', () => {
      const result = adapter.date('2023-05-15');
      expect(dayjs.isDayjs(result)).toBe(true);
      expect(result.format('YYYY-MM-DD')).toBe('2023-05-15');
    });

    it('should create date from undefined', () => {
      const result = adapter.date(undefined);
      expect(dayjs.isDayjs(result)).toBe(true);
    });

    it('should return null for null input', () => {
      const result = adapter.date(null);
      expect(result).toBe(null);
    });

    it('should handle locale when creating date', () => {
      const adapter = new DayjsAdapter({ locale: 'en' });
      const result = adapter.date('2023-05-15');
      expect(result.locale()).toBe('en');
    });
  });

  describe('date conversion', () => {
    it('should convert to JavaScript Date', () => {
      const dayValue = dayjs('2023-05-15');
      const result = adapter.toJsDate(dayValue);
      expect(result).toBeInstanceOf(Date);
      expect(result.getFullYear()).toBe(2023);
      expect(result.getMonth()).toBe(4); // 0-indexed
      expect(result.getDate()).toBe(15);
    });
  });

  describe('date parsing', () => {
    it('should parse date with format', () => {
      const result = adapter.parse('15/05/2023', 'DD/MM/YYYY');
      expect(result).not.toBe(null);
      expect(result!.format('YYYY-MM-DD')).toBe('2023-05-15');
    });

    it('should return null for empty string', () => {
      const result = adapter.parse('', 'YYYY-MM-DD');
      expect(result).toBe(null);
    });

    it('should handle invalid date strings', () => {
      const result = adapter.parse('invalid', 'YYYY-MM-DD');
      expect(result).not.toBe(null); // dayjs returns invalid date object
      expect(result!.isValid()).toBe(false);
    });
  });

  describe('formatting', () => {
    const testDate = dayjs('2023-05-15T14:30:00');

    it('should format by format key', () => {
      const result = adapter.format(testDate, 'year');
      expect(result).toBe('2023');
    });

    it('should format by custom string', () => {
      const result = adapter.formatByString(testDate, 'DD/MM/YYYY');
      expect(result).toBe('15/05/2023');
    });

    it('should format different keys correctly', () => {
      expect(adapter.format(testDate, 'month')).toBe('May');
      expect(adapter.format(testDate, 'monthShort')).toBe('May');
      expect(adapter.format(testDate, 'dayOfMonth')).toBe('15');
      expect(adapter.format(testDate, 'weekday')).toBe('Monday');
      expect(adapter.format(testDate, 'weekdayShort')).toBe('Mo');
    });
  });

  describe('date getters', () => {
    const testDate = dayjs('2023-05-15T14:30:25.123');

    it('should get year', () => {
      expect(adapter.getYear(testDate)).toBe(2023);
    });

    it('should get month', () => {
      expect(adapter.getMonth(testDate)).toBe(4); // 0-indexed
    });

    it('should get date', () => {
      expect(adapter.getDate(testDate)).toBe(15);
    });

    it('should get hours', () => {
      expect(adapter.getHours(testDate)).toBe(14);
    });

    it('should get minutes', () => {
      expect(adapter.getMinutes(testDate)).toBe(30);
    });

    it('should get seconds', () => {
      expect(adapter.getSeconds(testDate)).toBe(25);
    });

    it('should get milliseconds', () => {
      expect(adapter.getMilliseconds(testDate)).toBe(123);
    });

    it('should get days in month', () => {
      expect(adapter.getDaysInMonth(testDate)).toBe(31);
    });
  });

  describe('date validation', () => {
    it('should validate valid dates', () => {
      expect(adapter.isValid(dayjs('2023-05-15'))).toBe(true);
      expect(adapter.isValid('2023-05-15')).toBe(true);
    });

    it('should invalidate invalid dates', () => {
      expect(adapter.isValid('invalid-date')).toBe(false);
      expect(adapter.isValid(null)).toBe(false);
    });
  });

  describe('date comparison', () => {
    const date1 = dayjs('2023-05-15');
    const date2 = dayjs('2023-05-15');
    const date3 = dayjs('2023-05-16');
    const date4 = dayjs('2023-06-15');
    const date5 = dayjs('2024-05-15');

    it('should check same day', () => {
      expect(adapter.isSameDay(date1, date2)).toBe(true);
      expect(adapter.isSameDay(date1, date3)).toBe(false);
    });

    it('should check same month', () => {
      expect(adapter.isSameMonth(date1, date3)).toBe(true);
      expect(adapter.isSameMonth(date1, date4)).toBe(false);
    });

    it('should check same year', () => {
      expect(adapter.isSameYear(date1, date4)).toBe(true);
      expect(adapter.isSameYear(date1, date5)).toBe(false);
    });

    it('should check if before', () => {
      expect(adapter.isBefore(date1, date3)).toBe(true);
      expect(adapter.isBefore(date3, date1)).toBe(false);
    });

    it('should check if after', () => {
      expect(adapter.isAfter(date3, date1)).toBe(true);
      expect(adapter.isAfter(date1, date3)).toBe(false);
    });

    it('should check if before day', () => {
      expect(adapter.isBeforeDay(date1, date3)).toBe(true);
      expect(adapter.isBeforeDay(date3, date1)).toBe(false);
    });

    it('should check if after day', () => {
      expect(adapter.isAfterDay(date3, date1)).toBe(true);
      expect(adapter.isAfterDay(date1, date3)).toBe(false);
    });

    it('should check if before year', () => {
      expect(adapter.isBeforeYear(date1, date5)).toBe(true);
      expect(adapter.isBeforeYear(date5, date1)).toBe(false);
    });

    it('should check if after year', () => {
      expect(adapter.isAfterYear(date5, date1)).toBe(true);
      expect(adapter.isAfterYear(date1, date5)).toBe(false);
    });
  });

  describe('date math', () => {
    const testDate = dayjs('2023-05-15');

    it('should add time units', () => {
      const result = adapter.add(testDate, 5, 'day');
      expect(result.format('YYYY-MM-DD')).toBe('2023-05-20');
    });

    it('should subtract time units', () => {
      const result = adapter.subtract(testDate, 5, 'day');
      expect(result.format('YYYY-MM-DD')).toBe('2023-05-10');
    });

    it('should add days', () => {
      const result = adapter.addDays(testDate, 10);
      expect(result.format('YYYY-MM-DD')).toBe('2023-05-25');
    });

    it('should add months', () => {
      const result = adapter.addMonths(testDate, 2);
      expect(result.format('YYYY-MM-DD')).toBe('2023-07-15');
    });

    it('should add years', () => {
      const result = adapter.addYears(testDate, 1);
      expect(result.format('YYYY-MM-DD')).toBe('2024-05-15');
    });
  });

  describe('date setters', () => {
    const testDate = dayjs('2023-05-15T14:30:25.123');

    it('should set year', () => {
      const result = adapter.setYear(testDate, 2024);
      expect(adapter.getYear(result)).toBe(2024);
    });

    it('should set month', () => {
      const result = adapter.setMonth(testDate, 6); // July (0-indexed)
      expect(adapter.getMonth(result)).toBe(6);
    });

    it('should set date', () => {
      const result = adapter.setDate(testDate, 20);
      expect(adapter.getDate(result)).toBe(20);
    });

    it('should set hours', () => {
      const result = adapter.setHours(testDate, 10);
      expect(adapter.getHours(result)).toBe(10);
    });

    it('should set minutes', () => {
      const result = adapter.setMinutes(testDate, 45);
      expect(adapter.getMinutes(result)).toBe(45);
    });

    it('should set seconds', () => {
      const result = adapter.setSeconds(testDate, 30);
      expect(adapter.getSeconds(result)).toBe(30);
    });

    it('should set milliseconds', () => {
      const result = adapter.setMilliseconds(testDate, 500);
      expect(adapter.getMilliseconds(result)).toBe(500);
    });
  });

  describe('date boundaries', () => {
    const testDate = dayjs('2023-05-15T14:30:25.123');

    it('should get start of unit', () => {
      const startOfDay = adapter.startOf(testDate, 'day');
      expect(startOfDay.format('HH:mm:ss')).toBe('00:00:00');
    });

    it('should get end of unit', () => {
      const endOfDay = adapter.endOf(testDate, 'day');
      expect(endOfDay.format('HH:mm:ss')).toBe('23:59:59');
    });

    it('should get start of day', () => {
      const result = adapter.startOfDay(testDate);
      expect(result.format('HH:mm:ss.SSS')).toBe('00:00:00.000');
    });

    it('should get end of day', () => {
      const result = adapter.endOfDay(testDate);
      expect(result.format('HH:mm:ss.SSS')).toBe('23:59:59.999');
    });

    it('should get start of month', () => {
      const result = adapter.startOfMonth(testDate);
      expect(result.format('YYYY-MM-DD')).toBe('2023-05-01');
    });

    it('should get end of month', () => {
      const result = adapter.endOfMonth(testDate);
      expect(result.format('YYYY-MM-DD')).toBe('2023-05-31');
    });

    it('should get start of week', () => {
      const result = adapter.startOfWeek(testDate);
      expect(result.day()).toBe(0); // Sunday
    });

    it('should get end of week', () => {
      const result = adapter.endOfWeek(testDate);
      expect(result.day()).toBe(6); // Saturday
    });

    it('should get start of year', () => {
      const result = adapter.startOfYear(testDate);
      expect(result.format('YYYY-MM-DD')).toBe('2023-01-01');
    });

    it('should get end of year', () => {
      const result = adapter.endOfYear(testDate);
      expect(result.format('YYYY-MM-DD')).toBe('2023-12-31');
    });
  });

  describe('locale methods', () => {
    it('should get current locale code', () => {
      const adapter = new DayjsAdapter();
      expect(adapter.getCurrentLocaleCode()).toBe('en');
    });

    it('should get custom locale code', () => {
      const adapter = new DayjsAdapter({ locale: 'fr' });
      expect(adapter.getCurrentLocaleCode()).toBe('fr');
    });

    it('should detect 12-hour cycle', () => {
      const adapter = new DayjsAdapter();
      const result = adapter.is12HourCycleInCurrentLocale();
      expect(typeof result).toBe('boolean');
    });
  });

  describe('calendar helpers', () => {
    const testDate = dayjs('2023-05-15');

    it('should get week array', () => {
      const weeks = adapter.getWeekArray(testDate);
      expect(Array.isArray(weeks)).toBe(true);
      expect(weeks.length).toBeGreaterThan(0);
      expect(weeks[0].length).toBe(7);
    });

    it('should get week number', () => {
      const weekNumber = adapter.getWeekNumber(testDate);
      expect(typeof weekNumber).toBe('number');
      expect(weekNumber).toBeGreaterThan(0);
    });

    it('should get day of week', () => {
      const dayOfWeek = adapter.getDayOfWeek(testDate);
      expect(typeof dayOfWeek).toBe('number');
      expect(dayOfWeek).toBeGreaterThan(0);
      expect(dayOfWeek).toBeLessThanOrEqual(7);
    });

    it('should generate complete week array for month', () => {
      const weeks = adapter.getWeekArray(testDate);

      // Each week should have 7 days
      weeks.forEach((week) => {
        expect(week.length).toBe(7);
      });

      // Should include days from previous and next month to complete weeks
      const allDays = weeks.flat();
      expect(allDays.length).toBeGreaterThanOrEqual(28);
      expect(allDays.length).toBeLessThanOrEqual(42);
    });
  });

  describe('edge cases', () => {
    it('should handle leap year', () => {
      const leapYear = dayjs('2024-02-15');
      const daysInFeb = adapter.getDaysInMonth(leapYear);
      expect(daysInFeb).toBe(29);
    });

    it('should handle non-leap year', () => {
      const nonLeapYear = dayjs('2023-02-15');
      const daysInFeb = adapter.getDaysInMonth(nonLeapYear);
      expect(daysInFeb).toBe(28);
    });

    it('should handle month boundaries in week array', () => {
      const firstOfMonth = dayjs('2023-05-01');
      const weeks = adapter.getWeekArray(firstOfMonth);

      // Should include previous month days to complete first week
      const firstWeek = weeks[0];
      expect(firstWeek.some((day) => adapter.getMonth(day) === 3)).toBe(true); // April
    });

    it('should handle DST transitions', () => {
      // Test around DST transition dates (this may vary by locale)
      const beforeDST = dayjs('2023-03-26T01:00:00');
      const afterDST = adapter.addDays(beforeDST, 1);

      expect(adapter.isSameDay(beforeDST, afterDST)).toBe(false);
    });
  });
});
