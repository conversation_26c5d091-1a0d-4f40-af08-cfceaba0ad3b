import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import { describe, expect, it, afterEach, vi, beforeEach } from 'vitest';
import dayjs from 'dayjs/esm';
import { MonthListView } from './MonthListView';
import { PickerProvider } from '../../PickerContext';

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<MonthListView />', () => {
  const defaultProps = {
    date: dayjs('2023-05-15'),
    viewDate: dayjs('2023-05-15'),
    onChange: vi.fn(),
    disabled: false,
    readOnly: false,
    minDate: dayjs('1900-01-01'),
    maxDate: dayjs('2099-12-31'),
    disablePast: false,
    disableFuture: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render month list correctly', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} data-testid="month-list-view" />
        </PickerProvider>,
      );

      const monthListView = screen.getByRole('listbox');
      expect(monthListView).toBeInTheDocument();
      expect(monthListView).toHaveAttribute('aria-label', 'Month selection');
    });

    it('should render all 12 months', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByText('Jan')).toBeInTheDocument();
      expect(screen.getByText('Feb')).toBeInTheDocument();
      expect(screen.getByText('Mar')).toBeInTheDocument();
      expect(screen.getByText('Apr')).toBeInTheDocument();
      expect(screen.getByText('May')).toBeInTheDocument();
      expect(screen.getByText('Jun')).toBeInTheDocument();
      expect(screen.getByText('Jul')).toBeInTheDocument();
      expect(screen.getByText('Aug')).toBeInTheDocument();
      expect(screen.getByText('Sep')).toBeInTheDocument();
      expect(screen.getByText('Oct')).toBeInTheDocument();
      expect(screen.getByText('Nov')).toBeInTheDocument();
      expect(screen.getByText('Dec')).toBeInTheDocument();
    });

    it('should highlight selected month', () => {
      const selectedDate = dayjs('2023-05-15'); // May

      render(
        <PickerProvider>
          <MonthListView {...defaultProps} date={selectedDate} />
        </PickerProvider>,
      );

      const selectedMonth = screen.getByText('May');
      expect(selectedMonth).toHaveClass('selected');
      expect(selectedMonth).toHaveAttribute('aria-selected', 'true');
    });

    it('should disable months based on shouldDisableMonth function', () => {
      const shouldDisableMonth = (month: any) => month.month() === 6; // Disable July

      render(
        <PickerProvider>
          <MonthListView {...defaultProps} shouldDisableMonth={shouldDisableMonth} />
        </PickerProvider>,
      );

      const disabledMonth = screen.getByText('Jul');
      expect(disabledMonth).toBeDisabled();
    });
  });

  describe('interaction', () => {
    it('should call onChange when month is clicked', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const juneMonth = screen.getByText('Jun');
      fireEvent.click(juneMonth);

      expect(defaultProps.onChange).toHaveBeenCalledWith(5); // June is index 5
    });

    it('should not call onChange when disabled month is clicked', () => {
      const shouldDisableMonth = (month: any) => month.month() === 6; // Disable July

      render(
        <PickerProvider>
          <MonthListView {...defaultProps} shouldDisableMonth={shouldDisableMonth} />
        </PickerProvider>,
      );

      const disabledMonth = screen.getByText('Jul');
      fireEvent.click(disabledMonth);

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });

    it('should not call onChange when component is disabled', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} disabled />
        </PickerProvider>,
      );

      const juneMonth = screen.getByText('Jun');
      fireEvent.click(juneMonth);

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });

    it('should not call onChange when component is readOnly', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} readOnly />
        </PickerProvider>,
      );

      const juneMonth = screen.getByText('Jun');
      fireEvent.click(juneMonth);

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });
  });

  describe('keyboard navigation', () => {
    it('should handle Enter key to select month', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const juneMonth = screen.getByText('Jun');
      juneMonth.focus();
      fireEvent.keyDown(juneMonth, { key: 'Enter' });

      expect(defaultProps.onChange).toHaveBeenCalledWith(5); // June is index 5
    });

    it('should handle Space key to select month', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const juneMonth = screen.getByText('Jun');
      juneMonth.focus();
      fireEvent.keyDown(juneMonth, { key: ' ' });

      expect(defaultProps.onChange).toHaveBeenCalledWith(5); // June is index 5
    });

    it('should handle ArrowDown to move to next month', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const mayMonth = screen.getByText('May');
      fireEvent.keyDown(mayMonth, { key: 'ArrowDown' });

      // Should focus next month (implementation sets focused month state)
      expect(screen.getByText('Jun')).toBeInTheDocument();
    });

    it('should handle ArrowUp to move to previous month', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const mayMonth = screen.getByText('May');
      fireEvent.keyDown(mayMonth, { key: 'ArrowUp' });

      // Should focus previous month
      expect(screen.getByText('Apr')).toBeInTheDocument();
    });

    it('should handle ArrowRight to move to next month', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const mayMonth = screen.getByText('May');
      fireEvent.keyDown(mayMonth, { key: 'ArrowRight' });

      // Should focus next month (same as ArrowDown for list view)
      expect(screen.getByText('Jun')).toBeInTheDocument();
    });

    it('should handle ArrowLeft to move to previous month', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const mayMonth = screen.getByText('May');
      fireEvent.keyDown(mayMonth, { key: 'ArrowLeft' });

      // Should focus previous month (same as ArrowUp for list view)
      expect(screen.getByText('Apr')).toBeInTheDocument();
    });

    it('should handle Home key to move to first month', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const mayMonth = screen.getByText('May');
      fireEvent.keyDown(mayMonth, { key: 'Home' });

      // Should focus first month (January)
      expect(screen.getByText('Jan')).toBeInTheDocument();
    });

    it('should handle End key to move to last month', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const mayMonth = screen.getByText('May');
      fireEvent.keyDown(mayMonth, { key: 'End' });

      // Should focus last month (December)
      expect(screen.getByText('Dec')).toBeInTheDocument();
    });

    it('should handle PageUp to move up 5 months', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const juneMonth = screen.getByText('Jun'); // index 5
      fireEvent.keyDown(juneMonth, { key: 'PageUp' });

      // Should focus January (index 0) since 5 - 5 = 0
      expect(screen.getByText('Jan')).toBeInTheDocument();
    });

    it('should handle PageDown to move down 5 months', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const janMonth = screen.getByText('Jan'); // index 0
      fireEvent.keyDown(janMonth, { key: 'PageDown' });

      // Should focus June (index 5) since 0 + 5 = 5
      expect(screen.getByText('Jun')).toBeInTheDocument();
    });

    it('should not navigate when disabled', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} disabled />
        </PickerProvider>,
      );

      const mayMonth = screen.getByText('May');
      fireEvent.keyDown(mayMonth, { key: 'Enter' });

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });

    it('should not navigate when readOnly', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} readOnly />
        </PickerProvider>,
      );

      const mayMonth = screen.getByText('May');
      fireEvent.keyDown(mayMonth, { key: 'Enter' });

      expect(defaultProps.onChange).not.toHaveBeenCalled();
    });
  });

  describe('date constraints', () => {
    it('should disable past months when disablePast is true', () => {
      const currentDate = dayjs();
      const currentMonth = currentDate.month();

      render(
        <PickerProvider>
          <MonthListView {...defaultProps} viewDate={currentDate} disablePast />
        </PickerProvider>,
      );

      if (currentMonth > 0) {
        const pastMonth = currentDate.month(currentMonth - 1);
        const pastMonthName = pastMonth.format('MMM');
        const pastMonthElement = screen.getByText(pastMonthName);
        expect(pastMonthElement).toBeDisabled();
      }
    });

    it('should disable future months when disableFuture is true', () => {
      const currentDate = dayjs();
      const currentMonth = currentDate.month();

      render(
        <PickerProvider>
          <MonthListView {...defaultProps} viewDate={currentDate} disableFuture />
        </PickerProvider>,
      );

      if (currentMonth < 11) {
        const futureMonth = currentDate.month(currentMonth + 1);
        const futureMonthName = futureMonth.format('MMM');
        const futureMonthElement = screen.getByText(futureMonthName);
        expect(futureMonthElement).toBeDisabled();
      }
    });

    it('should disable months before minDate', () => {
      const minDate = dayjs('2023-05-01'); // May
      const viewDate = dayjs('2023-03-01'); // March

      render(
        <PickerProvider>
          <MonthListView {...defaultProps} minDate={minDate} viewDate={viewDate} />
        </PickerProvider>,
      );

      const marchMonth = screen.getByText('Mar');
      expect(marchMonth).toBeDisabled();
    });

    it('should disable months after maxDate', () => {
      const maxDate = dayjs('2023-05-31'); // May
      const viewDate = dayjs('2023-07-01'); // July

      render(
        <PickerProvider>
          <MonthListView {...defaultProps} maxDate={maxDate} viewDate={viewDate} />
        </PickerProvider>,
      );

      const julyMonth = screen.getByText('Jul');
      expect(julyMonth).toBeDisabled();
    });
  });

  describe('accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const listbox = screen.getByRole('listbox');
      expect(listbox).toHaveAttribute('aria-label', 'Month selection');

      const monthOptions = screen.getAllByRole('option');
      expect(monthOptions.length).toBe(12);

      monthOptions.forEach((option) => {
        expect(option).toHaveAttribute('aria-selected');
      });
    });

    it('should manage tabIndex correctly for keyboard navigation', () => {
      const selectedDate = dayjs('2023-05-15'); // May

      render(
        <PickerProvider>
          <MonthListView {...defaultProps} date={selectedDate} />
        </PickerProvider>,
      );

      const selectedMonth = screen.getByText('May');
      expect(selectedMonth).toHaveAttribute('tabIndex', '0');

      const otherMonth = screen.getByText('Jun');
      expect(otherMonth).toHaveAttribute('tabIndex', '-1');
    });

    it('should have proper disabled states for accessibility', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} disabled />
        </PickerProvider>,
      );

      const monthButtons = screen.getAllByRole('option');
      monthButtons.forEach((button) => {
        expect(button).toBeDisabled();
      });
    });

    it('should have data-month-index attributes for proper identification', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const mayMonth = screen.getByText('May');
      expect(mayMonth).toHaveAttribute('data-month-index', '4'); // May is index 4

      const decMonth = screen.getByText('Dec');
      expect(decMonth).toHaveAttribute('data-month-index', '11'); // December is index 11
    });
  });

  describe('focus management', () => {
    it('should maintain focus when month changes', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      const mayMonth = screen.getByText('May');
      mayMonth.focus();

      fireEvent.focus(mayMonth);

      expect(document.activeElement).toBe(mayMonth);
    });

    it('should handle focus transitions correctly', () => {
      const selectedDate = dayjs('2023-05-15');

      render(
        <PickerProvider>
          <MonthListView {...defaultProps} date={selectedDate} />
        </PickerProvider>,
      );

      const selectedMonth = screen.getByText('May');
      const otherMonth = screen.getByText('Jun');

      // Initially selected month should be focusable
      expect(selectedMonth).toHaveAttribute('tabIndex', '0');
      expect(otherMonth).toHaveAttribute('tabIndex', '-1');

      // Focus another month
      fireEvent.focus(otherMonth);

      // Both should exist and be accessible
      expect(selectedMonth).toBeInTheDocument();
      expect(otherMonth).toBeInTheDocument();
    });
  });

  describe('month display', () => {
    it('should display month names correctly', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      // Check that all month abbreviations are displayed
      const expectedMonths = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      expectedMonths.forEach((month) => {
        expect(screen.getByText(month)).toBeInTheDocument();
      });
    });

    it('should handle different view dates correctly', () => {
      const viewDate = dayjs('2024-07-15');

      render(
        <PickerProvider>
          <MonthListView {...defaultProps} viewDate={viewDate} />
        </PickerProvider>,
      );

      // All months should still be rendered regardless of view date
      expect(screen.getByText('Jan')).toBeInTheDocument();
      expect(screen.getByText('Jul')).toBeInTheDocument();
      expect(screen.getByText('Dec')).toBeInTheDocument();
    });
  });

  describe('edge cases', () => {
    it('should handle boundary navigation correctly', () => {
      render(
        <PickerProvider>
          <MonthListView {...defaultProps} />
        </PickerProvider>,
      );

      // Test navigation at boundaries
      const janMonth = screen.getByText('Jan');
      fireEvent.keyDown(janMonth, { key: 'ArrowUp' });

      // Should not navigate beyond January (index 0)
      expect(screen.getByText('Jan')).toBeInTheDocument();

      const decMonth = screen.getByText('Dec');
      fireEvent.keyDown(decMonth, { key: 'ArrowDown' });

      // Should not navigate beyond December (index 11)
      expect(screen.getByText('Dec')).toBeInTheDocument();
    });

    it('should handle leap year February correctly', () => {
      const leapYearDate = dayjs('2024-02-15');

      render(
        <PickerProvider>
          <MonthListView {...defaultProps} viewDate={leapYearDate} date={leapYearDate} />
        </PickerProvider>,
      );

      const febMonth = screen.getByText('Feb');
      expect(febMonth).toHaveClass('selected');
      expect(febMonth).toHaveAttribute('aria-selected', 'true');
    });
  });
});
