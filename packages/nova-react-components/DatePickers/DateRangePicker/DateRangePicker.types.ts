import * as React from 'react';
import {
  DockedDateRangePickerProps,
  DockedDateRangePickerSlots,
  DockedDateRangePickerSlotProps,
} from '../DockedDateRangePicker/DockedDateRangePicker.types';
import {
  ModalDateRangePickerProps,
  ModalDateRangePickerSlots,
  ModalDateRangePickerSlotProps,
} from '../ModalDateRangePicker/ModalDateRangePicker.types';

/**
 * Slots for DateRangePicker component - combines desktop and mobile slots
 * Follows MUI's pattern of inheriting from both variants
 */
export interface DateRangePickerSlots extends DockedDateRangePickerSlots, ModalDateRangePickerSlots {}

/**
 * Slot props for DateRangePicker component - combines desktop and mobile slot props
 */
export interface DateRangePickerSlotProps extends DockedDateRangePickerSlotProps, ModalDateRangePickerSlotProps {}

/**
 * Props for DateRangePicker component
 * Follows MUI's responsive pattern that combines both desktop and mobile variants
 */
export interface DateRangePickerProps
  extends Omit<DockedDateRangePickerProps, 'slots' | 'slotProps'>,
    Omit<ModalDateRangePickerProps, 'slots' | 'slotProps'> {
  /**
   * CSS media query when `Mobile` mode will be changed to `Desktop`.
   * @default '@media (pointer: fine)'
   * @example '@media (min-width: 720px)' or theme.breakpoints.up("sm")
   */
  desktopModeMediaQuery?: string;
  /**
   * Overridable component slots.
   * @default {}
   */
  slots?: DateRangePickerSlots;
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps?: DateRangePickerSlotProps;
}
