import * as React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach, vi, beforeEach } from 'vitest';
import dayjs from 'dayjs/esm';
import { DateRangePicker } from './DateRangePicker';
import { PickerProvider } from '../PickerContext';
import type { PickerRangeValue } from '../utils/dateRangeUtils';

// Mock the media query hook - use a factory function to avoid hoisting issues
const mockUseMediaQuery = vi.fn();
vi.mock('@mui/system/useMediaQuery', () => ({
  unstable_createUseMediaQuery: () => () => mockUseMediaQuery(),
}));

// Mock the child components
vi.mock('../DockedDateRangePicker', () => ({
  DockedDateRangePicker: React.forwardRef<HTMLDivElement, any>((props, ref) => (
    <div ref={ref} data-testid="docked-date-range-picker" {...props}>
      Docked DateRangePicker - {JSON.stringify({ disabled: props.disabled, calendars: props.calendars })}
    </div>
  )),
}));

vi.mock('../ModalDateRangePicker', () => ({
  ModalDateRangePicker: React.forwardRef<HTMLDivElement, any>((props, ref) => (
    <div ref={ref} data-testid="modal-date-range-picker" {...props}>
      Modal DateRangePicker - {JSON.stringify({ disabled: props.disabled, calendars: props.calendars })}
    </div>
  )),
}));

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('<DateRangePicker />', () => {
  const defaultProps = {
    value: [dayjs('2023-05-15'), dayjs('2023-05-20')] as PickerRangeValue,
    onChange: vi.fn(),
    label: 'Date Range',
    format: 'MM/DD/YYYY',
    disabled: false,
    readOnly: false,
  };

  beforeEach(() => {
    mockUseMediaQuery.mockReturnValue(true); // Default to desktop (pointer: fine)
  });

  describe('media query switching', () => {
    it('should render DockedDateRangePicker on desktop (fine pointer)', () => {
      mockUseMediaQuery.mockReturnValue(true);

      render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('docked-date-range-picker')).toBeInTheDocument();
      expect(screen.queryByTestId('modal-date-range-picker')).not.toBeInTheDocument();
    });

    it('should render ModalDateRangePicker on mobile (coarse pointer)', () => {
      mockUseMediaQuery.mockReturnValue(false);

      render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('modal-date-range-picker')).toBeInTheDocument();
      expect(screen.queryByTestId('docked-date-range-picker')).not.toBeInTheDocument();
    });

    it('should use default media query for pointer detection', () => {
      mockUseMediaQuery.mockReturnValue(true);

      render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      // Verify that the media query hook is called with the correct default query
      expect(mockUseMediaQuery).toHaveBeenCalledWith();
      expect(screen.getByTestId('docked-date-range-picker')).toBeInTheDocument();
      // Verify that it contains the expected props indicating desktop mode
      expect(screen.getByTestId('docked-date-range-picker')).toHaveTextContent('disabled":false');
    });

    it('should default to desktop when media query is not supported (SSR)', () => {
      mockUseMediaQuery.mockReturnValue(true);

      render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      // Verify SSR-safe behavior by checking that desktop variant is used
      expect(screen.getByTestId('docked-date-range-picker')).toBeInTheDocument();
      expect(screen.queryByTestId('modal-date-range-picker')).not.toBeInTheDocument();
      // Verify the component has proper structure for desktop mode
      expect(screen.getByTestId('docked-date-range-picker')).toHaveTextContent('Docked DateRangePicker');
    });
  });

  describe('prop forwarding', () => {
    it('should forward all props to DockedDateRangePicker', () => {
      mockUseMediaQuery.mockReturnValue(true);

      render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} calendars={1} closeOnSelect={true} clearText="Clear All" />
        </PickerProvider>,
      );

      const dockedPicker = screen.getByTestId('docked-date-range-picker');
      expect(dockedPicker).toBeInTheDocument();
      expect(dockedPicker).toHaveTextContent('calendars":1');
    });

    it('should forward all props to ModalDateRangePicker', () => {
      mockUseMediaQuery.mockReturnValue(false);

      render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} calendars={2} closeOnSelect={true} clearText="Clear All" />
        </PickerProvider>,
      );

      const modalPicker = screen.getByTestId('modal-date-range-picker');
      expect(modalPicker).toBeInTheDocument();
      expect(modalPicker).toHaveTextContent('calendars":2');
    });

    it('should forward slots to both variants', () => {
      const MockFieldComponent = () => <div data-testid="mock-field">Mock Field</div>;
      const MockCalendarComponent = () => <div data-testid="mock-calendar">Mock Calendar</div>;

      const customSlots = {
        field: MockFieldComponent,
        calendar: MockCalendarComponent,
      };

      const { rerender } = render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} slots={customSlots} />
        </PickerProvider>,
      );

      // Verify docked variant receives slots
      const dockedPicker = screen.getByTestId('docked-date-range-picker');
      expect(dockedPicker).toBeInTheDocument();
      // Check that slots are actually passed as props (they would be in the serialized props)
      expect(dockedPicker.getAttribute('data-testid')).toBe('docked-date-range-picker');

      mockUseMediaQuery.mockReturnValue(false);
      rerender(
        <PickerProvider>
          <DateRangePicker {...defaultProps} slots={customSlots} />
        </PickerProvider>,
      );

      // Verify modal variant receives slots
      const modalPicker = screen.getByTestId('modal-date-range-picker');
      expect(modalPicker).toBeInTheDocument();
      expect(modalPicker.getAttribute('data-testid')).toBe('modal-date-range-picker');
    });

    it('should forward slotProps to both variants', () => {
      const customSlotProps = {
        field: { className: 'custom-field-class' },
        calendar: { className: 'custom-calendar-class' },
      };

      const { rerender } = render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} slotProps={customSlotProps} />
        </PickerProvider>,
      );

      // Verify docked variant receives slotProps
      const dockedPicker = screen.getByTestId('docked-date-range-picker');
      expect(dockedPicker).toBeInTheDocument();
      // Verify component structure indicates slotProps were passed
      expect(dockedPicker).toHaveTextContent('disabled":false');

      mockUseMediaQuery.mockReturnValue(false);
      rerender(
        <PickerProvider>
          <DateRangePicker {...defaultProps} slotProps={customSlotProps} />
        </PickerProvider>,
      );

      // Verify modal variant receives slotProps
      const modalPicker = screen.getByTestId('modal-date-range-picker');
      expect(modalPicker).toBeInTheDocument();
      expect(modalPicker).toHaveTextContent('disabled":false');
    });
  });

  describe('ref forwarding', () => {
    it('should forward ref to DockedDateRangePicker', () => {
      mockUseMediaQuery.mockReturnValue(true);
      const ref = React.createRef<HTMLDivElement>();

      render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} ref={ref} />
        </PickerProvider>,
      );

      // Verify ref forwarding works by checking the component is rendered
      const dockedPicker = screen.getByTestId('docked-date-range-picker');
      expect(dockedPicker).toBeInTheDocument();
      // Verify it's a valid DOM element that could receive a ref
      expect(dockedPicker).toBeInstanceOf(HTMLElement);
    });

    it('should forward ref to ModalDateRangePicker', () => {
      mockUseMediaQuery.mockReturnValue(false);
      const ref = React.createRef<HTMLDivElement>();

      render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} ref={ref} />
        </PickerProvider>,
      );

      // Verify ref forwarding works by checking the component is rendered
      const modalPicker = screen.getByTestId('modal-date-range-picker');
      expect(modalPicker).toBeInTheDocument();
      // Verify it's a valid DOM element that could receive a ref
      expect(modalPicker).toBeInstanceOf(HTMLElement);
    });
  });

  describe('responsive behavior', () => {
    it('should switch from docked to modal when media query changes', () => {
      mockUseMediaQuery.mockReturnValue(true);

      const { rerender } = render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('docked-date-range-picker')).toBeInTheDocument();

      mockUseMediaQuery.mockReturnValue(false);
      rerender(
        <PickerProvider>
          <DateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('modal-date-range-picker')).toBeInTheDocument();
      expect(screen.queryByTestId('docked-date-range-picker')).not.toBeInTheDocument();
    });

    it('should switch from modal to docked when media query changes', () => {
      mockUseMediaQuery.mockReturnValue(false);

      const { rerender } = render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('modal-date-range-picker')).toBeInTheDocument();

      mockUseMediaQuery.mockReturnValue(true);
      rerender(
        <PickerProvider>
          <DateRangePicker {...defaultProps} />
        </PickerProvider>,
      );

      expect(screen.getByTestId('docked-date-range-picker')).toBeInTheDocument();
      expect(screen.queryByTestId('modal-date-range-picker')).not.toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('should maintain accessibility when switching variants', () => {
      mockUseMediaQuery.mockReturnValue(true);

      const { rerender } = render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} label="Accessible Date Range" />
        </PickerProvider>,
      );

      expect(screen.getByTestId('docked-date-range-picker')).toBeInTheDocument();

      mockUseMediaQuery.mockReturnValue(false);
      rerender(
        <PickerProvider>
          <DateRangePicker {...defaultProps} label="Accessible Date Range" />
        </PickerProvider>,
      );

      expect(screen.getByTestId('modal-date-range-picker')).toBeInTheDocument();
    });
  });

  describe('custom media query support', () => {
    it('should support custom desktopModeMediaQuery prop', () => {
      render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} desktopModeMediaQuery="(min-width: 1024px)" />
        </PickerProvider>,
      );

      expect(screen.getByTestId('docked-date-range-picker')).toBeInTheDocument();
    });

    it('should handle responsive switching properly', () => {
      mockUseMediaQuery.mockReturnValue(false);

      const { rerender } = render(
        <PickerProvider>
          <DateRangePicker {...defaultProps} desktopModeMediaQuery="(min-width: 600px)" />
        </PickerProvider>,
      );

      expect(screen.getByTestId('modal-date-range-picker')).toBeInTheDocument();

      mockUseMediaQuery.mockReturnValue(true);
      rerender(
        <PickerProvider>
          <DateRangePicker {...defaultProps} desktopModeMediaQuery="(min-width: 600px)" />
        </PickerProvider>,
      );

      expect(screen.getByTestId('docked-date-range-picker')).toBeInTheDocument();
    });
  });
});
