import * as React from 'react';
import { SlotComponentProps } from '@mui/utils';
import { SxProps } from '../../types/theme';
import { BasePickerInputProps } from '../DatePicker/shared';
import { ExportedDateRangeCalendarProps } from '../DateRangeCalendar/DateRangeCalendar.types';
import { PickerRangeValue } from '../utils/dateRangeUtils';
import { BasePickerProps, BasePickerTextProps } from '../types';

/**
 * Base slots for all date range picker variants
 */
export interface BaseDateRangePickerSlots {
  /**
   * Component used for the root element.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * Component used for the field.
   * @default DateRangeField
   */
  field?: React.ElementType;
  /**
   * Custom component for the range calendar.
   * @default DateRangeCalendar
   */
  calendar?: React.ElementType;
  /**
   * Component used for the footer.
   * @default PickerViewFooter
   */
  footer?: React.ElementType;
}

/**
 * Base slot props for all date range picker variants
 */
export interface BaseDateRangePickerSlotProps {
  root?: SlotComponentProps<React.ElementType, Record<string, unknown>, BaseDateRangePickerOwnerState>;
  field?: SlotComponentProps<React.ElementType, Record<string, unknown>, BaseDateRangePickerOwnerState>;
  calendar?: SlotComponentProps<React.ElementType, Record<string, unknown>, BaseDateRangePickerOwnerState>;
  footer?: SlotComponentProps<React.ElementType, Record<string, unknown>, BaseDateRangePickerOwnerState>;
}

/**
 * Base props for all date range picker variants
 * Follows MUI's BaseDateRangePickerProps pattern
 */
export interface BaseDateRangePickerProps
  extends BasePickerTextProps,
    BasePickerInputProps<PickerRangeValue>,
    ExportedDateRangeCalendarProps,
    BasePickerProps {
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps;
}

/**
 * Owner state for base date range picker
 */
export interface BaseDateRangePickerOwnerState extends BaseDateRangePickerProps {}
