import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface ChipClasses {
  /** Class name applied to the root element. */
  root: string;

  /** Class name applied to the Button if `size="small"`. */
  sizeSmall: string;
  /** Class name applied to the Button if `size="medium"`. */
  sizeMedium: string;
  /** Class name applied to the Button if `size="large"`. */
  sizeLarge: string;

  /** Class name applied to the Button startIcon element. */
  iconStart: string;
  /** Class name applied to the Button endIcon element. */
  iconEnd: string;

  /** Class name applied to the Button if `disabled={true}` */
  disabled: string;
  /** Class name applied to the Button if `selected={true}` */
  selected: string;
}

export type ChipClassKey = keyof ChipClasses;

export function getChipUtilityClass(slot: string): string {
  return generateUtilityClass('NovaChip', slot, 'Nova');
}

const chipClasses: ChipClasses = generateUtilityClasses(
  'NovaChip',
  ['root', 'sizeSmall', 'sizeMedium', 'sizeLarge', 'iconStart', 'iconEnd', 'disabled', 'selected'],
  'Nova',
);

export default chipClasses;
