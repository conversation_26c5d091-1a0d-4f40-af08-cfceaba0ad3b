'use client';
import React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import { ChipProps } from './Chip.types';
import { getChipUtilityClass } from './Chip.classes';

const useUtilityClasses = (ownerState: ChipProps) => {
  const { size, disabled, selected } = ownerState;

  const slots = {
    root: ['root', disabled && 'disabled', size && `size${capitalize(size)}`, selected && 'selected'],
    startIcon: ['iconStart'],
    endIcon: ['iconEnd'],
  };

  return composeClasses(slots, getChipUtilityClass, {});
};

const ChipIcon = styled('span')(() => ({
  display: 'inline-flex',
  alignItems: 'center',
  fontSize: 'inherit',
  '& > svg': {
    display: 'block',
    fontSize: '1.5em',
  },
}));

export const ChipRoot = styled('div', {
  shouldForwardProp: (prop) =>
    !['disabled', 'size', 'startIcon', 'endIcon', 'selected', 'label', 'clickable'].includes(prop),
})<Omit<ChipProps, 'startIcon' | 'endIcon' | 'label'> & { clickable: boolean }>(({ theme }) => ({
  position: 'relative',
  whiteSpace: 'nowrap',
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: '0.5rem',
  height: '2rem',
  borderRadius: 9999,
  paddingLeft: '1rem',
  paddingRight: '1rem',
  paddingTop: '0.5rem',
  paddingBottom: '0.5rem',
  borderWidth: 1,
  borderStyle: 'solid',
  borderColor: theme.vars.palette.outline,
  color: theme.vars.palette.onSurface,
  ...theme.typography.labelMedium,
  '&:focus-visible': {
    outline: 'none',
  },

  variants: [
    // size
    {
      props: { size: 'small' },
      style: {
        gap: '0.25rem',
        height: '1.5rem',
        paddingLeft: '0.5rem',
        paddingRight: '0.5rem',
        paddingTop: '0.25rem',
        paddingBottom: '0.25rem',
      },
    },
    {
      props: { size: 'large' },
      style: {
        gap: '0.75rem',
        height: '2.5rem',
        paddingLeft: '1.5rem',
        paddingRight: '1.5rem',
        paddingTop: '0.75rem',
        paddingBottom: '0.75rem',
      },
    },

    // States
    {
      props: { disabled: false },
      style: {
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.secondary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
      },
    },
    {
      props: { disabled: true },
      style: {
        pointerEvents: 'none',
        cursor: 'default',
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabled})`,
        color: theme.vars.palette.onBackgroundDisabled,
        borderColor: theme.vars.palette.outlineDisabled,
      },
    },
    {
      props: { clickable: true, disabled: false },
      style: {
        cursor: 'pointer',
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: { selected: true, disabled: false },
      style: {
        borderColor: theme.vars.palette.primary,
        color: theme.vars.palette.onSecondaryContainer,
        backgroundColor: theme.vars.palette.secondaryContainer,
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.secondary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
        },
      },
    },
    {
      props: { selected: true, disabled: false, clickable: true },
      style: {
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
        },
      },
    },
    {
      props: { selected: true, disabled: true },
      style: {
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabled})`,
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const Chip = React.forwardRef<HTMLDivElement, ChipProps>((props, ref) => {
  const defaultProps: Partial<ChipProps> = {
    size: 'medium',
    disabled: false,
    selected: false,
  };
  const mergedProps = { ...defaultProps, ...props };
  const classes = useUtilityClasses(mergedProps);
  const { className, label, onClick, startIcon, endIcon, ...other } = mergedProps;

  return (
    <ChipRoot
      ref={ref}
      className={[classes.root, className].join(' ')}
      clickable={Boolean(onClick)}
      onClick={onClick}
      tabIndex={mergedProps.disabled ? -1 : 0}
      {...other}
    >
      {startIcon && <ChipIcon className={classes.startIcon}>{startIcon}</ChipIcon>}
      {label}
      {endIcon && <ChipIcon className={classes.endIcon}>{endIcon}</ChipIcon>}
    </ChipRoot>
  );
});
