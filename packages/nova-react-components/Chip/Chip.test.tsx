import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, fireEvent, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Chip } from './Chip';

afterEach(() => {
  cleanup();
});

describe('Chip', () => {
  it('renders Chip component', () => {
    render(<Chip label="My Chip" />);
    expect(screen.getByText('My Chip')).toBeInTheDocument();
  });

  it('renders Chip component with start icon', () => {
    render(<Chip label="My Chip" startIcon={<span data-testid="start-icon" />} />);
    expect(screen.getByTestId('start-icon')).toBeInTheDocument();
  });

  it('renders Chip component with end icon', () => {
    render(<Chip label="My Chip" endIcon={<span data-testid="end-icon" />} />);
    expect(screen.getByTestId('end-icon')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<Chip label="My Chip" className="custom-class" />);
    const chip = screen.getByText('My Chip');
    expect(chip).toHaveClass('custom-class');
  });

  it('handles click event', () => {
    const handleClick = vi.fn();
    render(<Chip label="My Chip" onClick={handleClick} />);
    const chip = screen.getByText('My Chip');
    fireEvent.click(chip);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies disabled state', () => {
    render(<Chip label="My Chip" disabled />);
    const chip = screen.getByText('My Chip');
    expect(chip).toHaveAttribute('tabIndex', '-1');
    expect(chip).toHaveClass('Nova-disabled');
  });

  it('applies selected state', () => {
    render(<Chip label="My Chip" selected />);
    const chip = screen.getByText('My Chip');
    expect(chip).toHaveClass('Nova-selected');
  });
});
