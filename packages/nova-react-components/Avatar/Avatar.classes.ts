import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface AvatarClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Class name applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Class name applied to the root element if `size="large"`. */
  sizeLarge: string;
  /** Class name applied to the img element if either `src` or `srcSet` is defined. */
  img: string;
  /** Class name applied to the fallback icon */
  fallback: string;
  /** Class name applied to the root element if the link is keyboard focused. */
  focusVisible: string;
}

export type AvatarClassKey = keyof AvatarClasses;

export function getAvatarUtilityClass(slot: string): string {
  return generateUtilityClass('NovaAvatar', slot, 'Nova');
}

const avatarClasses: AvatarClasses = generateUtilityClasses(
  'NovaAvatar',
  ['root', 'sizeSmall', 'sizeMedium', 'sizeLarge', 'img', 'fallback', 'focusVisible'],
  'Nova',
);

export default avatarClasses;
