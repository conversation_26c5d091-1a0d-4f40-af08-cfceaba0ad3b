import { OverridableStringUnion, OverrideProps } from '@mui/types';
import { ApplyColorInversion } from '../types/colorSystem';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../types/slot';

export type AvatarSlot = 'root' | 'img' | 'fallback';

export interface AvatarSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the img.
   * @default 'img'
   */
  img?: React.ElementType;
  /**
   * The component that renders the fallback.
   * @default 'svg'
   */
  fallback?: React.ElementType;
}

export interface AvatarPropsStatusOverrides {}

export type AvatarSlotsAndSlotProps = CreateSlotsAndSlotProps<
  AvatarSlots,
  {
    root: SlotProps<'div', object, AvatarOwnerState>;
    img: SlotProps<'img', object, AvatarOwnerState>;
    fallback: SlotProps<'svg', object, AvatarOwnerState>;
  }
>;

export interface AvatarTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    AvatarSlotsAndSlotProps & {
      /**
       * Used in combination with `src` or `srcSet` to
       * provide an alt attribute for the rendered `img` element.
       */
      alt?: string;
      /**
       * Used to render icon or text elements inside the Avatar if `src` is not set.
       * This can be an element, or just a string.
       */
      children?: React.ReactNode;
      /**
       * The  status badge color of the component.
       */
      color?: OverridableStringUnion<'primary' | 'error' | 'info' | 'warning' | 'success', AvatarPropsStatusOverrides>;
      /**
       * The overall size of the button.
       * @default 'medium'
       */
      size?: 'small' | 'medium' | 'large';
      /**
       * The disable state of the avatar
       */
      disabled?: boolean;
      /**
       * The `src` attribute for the `img` element.
       */
      src?: string;
      /**
       * The `srcSet` attribute for the `img` element.
       * Use this attribute for responsive image display.
       */
      srcSet?: string;
    };
  defaultComponent: D;
}

export type AvatarProps<
  D extends React.ElementType = AvatarTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<AvatarTypeMap<P, D>, D>;

export interface AvatarOwnerState extends ApplyColorInversion<AvatarProps> {
  /**
   * If `true`, the element's focus is visible.
   */
  focusVisible?: boolean;
  /**
   * The avatar is wrapped by AvatarGroup component.
   */
  grouped: boolean;
}
