'use client';
import * as React from 'react';
import {
  unstable_capitalize as capitalize,
  unstable_composeClasses as composeClasses,
  unstable_useForkRef as useForkRef,
  unstable_useIsFocusVisible as useIsFocusVisible,
} from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { getAvatarUtilityClass } from './Avatar.classes';
import { AvatarOwnerState, AvatarProps } from './Avatar.types';
import { styled } from '@pigment-css/react';
import Person from '../internal/svg-icons/Person';
import { Badge } from '../Badge';
import { AvatarGroupContext } from '../AvatarGroup/AvatarGroup';

const useUtilityClasses = (ownerState: AvatarOwnerState) => {
  const { size, disabled, color, focusVisible, src, srcSet } = ownerState;

  const slots = {
    root: [
      'root',
      disabled && 'disabled',
      color && `color${capitalize(color)}`,
      size && `size${capitalize(size)}`,
      focusVisible && 'focusVisible',
    ],
    img: [(src || srcSet) && 'img'],
    fallback: ['fallback'],
  };

  return composeClasses(slots, getAvatarUtilityClass, {});
};

export const StyledBadge = styled(Badge)(({ theme }) => ({
  '& .NovaBadge-badge': {
    zIndex: 1,
    boxShadow: `0 0 0 1px ${theme.vars.palette.onPrimary}`,
  },
  '& .NovaBadge-anchorOriginBottomRight': {
    position: 'absolute',
    bottom: '14%',
    right: '14%',
    top: 'unset',
    left: 'unset',
    transformOrigin: '100% 100%',
    transform: 'scale(1) translate(50%, 50%)',
  },
}));

const AvatarRoot = styled('div')<AvatarProps>(({ theme }) => ({
  '--nova-avatar-size-comfortable': '2.5rem',
  '--nova-avatar-size-standard': '2rem',
  '--nova-avatar-size-compact': '1.5rem',
  '--nova-avatar-size': 'var(--nova-avatar-standard-size)',
  '--Icon-color': 'currentColor',
  marginInlineStart: 'var(--nova-avatar-marginInline-start)',
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexShrink: 0,
  lineHeight: 1,
  overflow: 'hidden',
  borderRadius: '50%',
  backgroundColor: theme.vars.palette.surfaceVariant,
  color: theme.vars.palette.inverseOnSurface,
  userSelect: 'none',
  width: 'calc(var(--nova-avatar-size))',
  height: 'calc(var(--nova-avatar-size))',
  fontFamily: theme.typography.fontFamily,
  fontWeight: 500,
  fontSize: 'calc(2rem * 0.4375)', // default as 14px
  '&:focus-visible': {
    outlineOffset: '1.5px',
    outline: `1.5px solid ${theme.vars.palette.primary}`,
  },
  variants: [
    {
      props: { size: 'small' },
      style: {
        '--nova-avatar-size': 'var(--nova-avatar-size-compact, 1.5rem)',
        width: 'var(--nova-avatar-size-compact, 1.5rem)',
        height: 'var(--nova-avatar-size-compact, 1.5rem)',
        fontSize: `calc(var(--nova-avatar-size-compact, 1.5rem) * 0.5)`, // default as 12px
      },
    },
    {
      props: { size: 'medium' },
      style: {
        '--nova-avatar-size': 'var(--nova-avatar-size-standard, 2rem)',
        width: 'var(--nova-avatar-size-standard, 2rem)',
        height: 'var(--nova-avatar-size-standard, 2rem)',
        fontSize: `calc(var(--nova-avatar-size-standard, 2rem) * 0.4375)`, // default as 14px
      },
    },
    {
      props: { size: 'large' },
      style: {
        '--nova-avatar-size': 'var(--nova-avatar-size-comfortable, 2.5rem)',
        width: 'var(--nova-avatar-size-comfortable, 2.5rem)',
        height: 'var(--nova-avatar-size-comfortable, 2.5rem)',
        fontSize: 'calc(var(--nova-avatar-size-comfortable,2.5rem) * 0.4)', // default as 16px
      },
    },
    {
      props: { disabled: true },
      style: {
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.disabledOnSurface})`,
        color: theme.vars.palette.onBackgroundDisabled,
        '& img': { opacity: '50%' },
      },
    },
  ],
}));

const AvatarImg = styled('img')<{ ownerState: AvatarOwnerState }>({
  width: '100%',
  height: '100%',
  textAlign: 'center',
  // Handle non-square image. The property isn't supported by IE11.
  objectFit: 'cover',
  // Hide alt text.
  color: 'neutral',
  // Hide the image broken icon, only works on Chrome.
  textIndent: 10000,
});

const AvatarFallback = styled(Person as unknown as 'svg')<{ ownerState: AvatarOwnerState }>({
  width: '64%',
  height: '64%',
});

type UseLoadedProps = { src?: string; srcSet?: string; crossOrigin?: any; referrerPolicy?: any };

function useLoaded({ crossOrigin, referrerPolicy, src, srcSet }: UseLoadedProps) {
  const [loaded, setLoaded] = React.useState<string | boolean>(false);

  React.useEffect(() => {
    if (!src && !srcSet) {
      return undefined;
    }

    setLoaded(false);

    let active = true;
    const image = new Image();
    image.onload = () => {
      if (!active) {
        return;
      }
      setLoaded('loaded');
    };
    image.onerror = () => {
      if (!active) {
        return;
      }
      setLoaded('error');
    };
    image.crossOrigin = crossOrigin;
    image.referrerPolicy = referrerPolicy;
    if (src) {
      image.src = src;
    }
    if (srcSet) {
      image.srcset = srcSet;
    }

    return () => {
      active = false;
    };
  }, [crossOrigin, referrerPolicy, src, srcSet]);

  return loaded;
}

export const Avatar = React.forwardRef(function Avatar(props: AvatarProps, ref: React.ForwardedRef<Element>) {
  const avatarProps: AvatarProps = {
    size: 'medium',
    slots: {},
    slotProps: {},
    ...props,
  };

  const groupContext = React.useContext(AvatarGroupContext);

  const {
    alt,
    size: sizeProp = 'medium',
    color: colorProp,
    disabled: disabledProp = false,
    src,
    srcSet,
    children: childrenProp,
    onBlur,
    onFocus,
    component,
    slots = {},
    slotProps = {},
    ...other
  } = avatarProps;

  const color = props.color || groupContext?.color || colorProp;
  const size = props.size || groupContext?.size || sizeProp;
  const disabled = props.disabled || groupContext?.disabled || disabledProp;
  const {
    isFocusVisibleRef,
    onBlur: handleBlurVisible,
    onFocus: handleFocusVisible,
    ref: focusVisibleRef,
  } = useIsFocusVisible();
  const [focusVisible, setFocusVisible] = React.useState(false);

  const handleRef = useForkRef(ref, focusVisibleRef);
  const handleBlur = (event: React.FocusEvent<HTMLDivElement>) => {
    handleBlurVisible(event);
    if (isFocusVisibleRef.current === false) {
      setFocusVisible(false);
    }
    if (onBlur) {
      onBlur(event);
    }
  };
  const handleFocus = (event: React.FocusEvent<HTMLDivElement>) => {
    handleFocusVisible(event);
    if (isFocusVisibleRef.current === true) {
      setFocusVisible(true);
    }
    if (onFocus) {
      onFocus(event);
    }
  };

  let children = null;

  const ownerState = {
    ...props,
    disabled,
    color,
    size,
    focusVisible,
    grouped: !!groupContext,
  };

  const classes = useUtilityClasses(ownerState);
  const externalForwardedProps = { ...other };

  const SlotRoot = slots.root ?? AvatarRoot;
  const SlotImg = slots.img ?? AvatarImg;
  const SlotFallback = slots.fallback ?? AvatarFallback;
  const rootProps = useSlotProps({
    additionalProps: {
      onBlur: handleBlur,
      onFocus: handleFocus,
      tabIndex: disabled ? -1 : 0,
      ref: handleRef,
      as: component,
    },
    className: classes.root,
    elementType: AvatarRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps,
    ownerState,
  });

  const imageProps = useSlotProps({
    additionalProps: {
      alt,
      src,
      srcSet,
    },
    className: classes.img,
    elementType: AvatarImg,
    externalSlotProps: slotProps.img,
    ownerState,
  });

  const fallbackProps = useSlotProps({
    className: classes.fallback,
    elementType: AvatarFallback,
    externalSlotProps: slotProps.fallback,
    ownerState,
  });

  // Use a hook instead of onError on the img element to support server-side rendering.
  const loaded = useLoaded({
    ...imageProps,
    src,
    srcSet,
  });

  const hasImg = src || srcSet;
  const hasImgNotFailing = hasImg && loaded !== 'error';

  if (hasImgNotFailing) {
    children = <SlotImg {...imageProps} />;
  } else if (childrenProp != null) {
    children = childrenProp;
  } else if (alt) {
    children = alt[0];
  } else {
    children = <SlotFallback {...fallbackProps} />;
  }

  return !color ? (
    <SlotRoot {...rootProps}>{children}</SlotRoot>
  ) : (
    <StyledBadge
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      size="small"
      color={color}
      disabled={disabled}
    >
      <SlotRoot {...rootProps}>{children}</SlotRoot>
    </StyledBadge>
  );
});
