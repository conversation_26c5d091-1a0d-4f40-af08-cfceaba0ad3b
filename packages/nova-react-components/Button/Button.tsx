'use client';
import React from 'react';
import { styled } from '@pigment-css/react';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import { ButtonProps } from './Button.types';
import { getButtonUtilityClass } from './Button.classes';
import { ButtonBase } from '../ButtonBase';
import clsx from 'clsx';

const useUtilityClasses = (ownerState: ButtonProps) => {
  const { color, variant } = ownerState;

  const slots = {
    root: ['root', variant, color && `color${capitalize(color)}`],
  };

  return composeClasses(slots, getButtonUtilityClass, {});
};

const ButtonBaseRoot = styled(ButtonBase)<ButtonProps>(({ theme }) => ({
  variants: [
    // Text Styles
    {
      props: { variant: 'text' },
      style: {
        backgroundColor: 'transparent',
        color: theme.vars.palette.primary,
        borderWidth: 0,
      },
    },
    {
      props: { variant: 'text', color: 'primary' },
      style: {
        color: theme.vars.palette.primary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.primary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
        },
      },
    },
    {
      props: { variant: 'text', color: 'error' },
      style: {
        color: theme.vars.palette.error,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.error} ${theme.vars.palette.stateLayers.hoverError})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.error}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.error} ${theme.vars.palette.stateLayers.focusError})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.error} ${theme.vars.palette.stateLayers.pressError})`,
        },
      },
    },
    // Outlined Styles
    {
      props: { variant: 'outlined' },
      style: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderStyle: 'solid',
      },
    },
    {
      props: { variant: 'outlined', color: 'primary' },
      style: {
        borderColor: theme.vars.palette.primary,
        color: theme.vars.palette.primary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.primary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressPrimary})`,
        },
      },
    },
    {
      props: { variant: 'outlined', color: 'error' },
      style: {
        borderColor: theme.vars.palette.error,
        color: theme.vars.palette.error,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.error} ${theme.vars.palette.stateLayers.hoverError})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.error}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.error} ${theme.vars.palette.stateLayers.focusError})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.error} ${theme.vars.palette.stateLayers.pressError})`,
        },
      },
    },
    // Filled Styles
    {
      props: { variant: 'filled' },
      style: { border: 'none' },
    },
    {
      props: { variant: 'filled', color: 'primary' },
      style: {
        backgroundColor: theme.vars.palette.primary,
        color: theme.vars.palette.onPrimary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.hoverOnPrimary})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.primary}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.focusOnPrimary})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onPrimary} ${theme.vars.palette.stateLayers.pressOnPrimary})`,
        },
      },
    },
    {
      props: { variant: 'filled', color: 'error' },
      style: {
        backgroundColor: theme.vars.palette.error,
        color: theme.vars.palette.onError,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.error}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.error}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.error}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.error}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },

    // Disabled Styles
    {
      props: { disabled: true },
      style: {
        backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundDisabled}, ${theme.vars.palette.onBackgroundDisabled} ${theme.vars.palette.stateLayers.disabled})`,
        color: theme.vars.palette.onBackgroundDisabled,
        border: 'none',
      },
    },
    {
      props: { disabled: true, variant: 'outlined' },
      style: {
        backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onBackgroundDisabled} ${theme.vars.palette.stateLayers.disabled})`,
        border: `1px solid ${theme.vars.palette.outlineDisabled}`,
      },
    },
    {
      props: { disabled: true, variant: 'text' },
      style: {
        background: 'transparent',
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const Button = React.forwardRef((props: ButtonProps, ref: React.ForwardedRef<HTMLButtonElement>) => {
  const defaultProps: Partial<ButtonProps> = {
    color: 'primary',
    variant: 'filled',
  };
  const mergedProps = { ...defaultProps, ...props };
  const classes = useUtilityClasses(mergedProps);

  const { children, className, ...otherProps } = mergedProps;

  return (
    <ButtonBaseRoot ref={ref} className={clsx(classes.root, className)} {...otherProps}>
      {children}
    </ButtonBaseRoot>
  );
});
