import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ButtonClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the Button if `color="primary"`. */
  colorPrimary: string;
  /** Class name applied to the Button if `color="error"`. */
  colorError: string;

  /** Class name applied to the Button if `variant="filled"`. */
  filled: string;
  /** Class name applied to the Button if `variant="outlined"`. */
  outlined: string;
  /** Class name applied to the Button if `variant="text"`. */
  text: string;
}

export type ButtonClassKey = keyof ButtonClasses;

export function getButtonUtilityClass(slot: string): string {
  return generateUtilityClass('NovaButton', slot, 'Nova');
}

const buttonClasses: ButtonClasses = generateUtilityClasses(
  'NovaButton',
  ['root', 'colorPrimary', 'colorError', 'filled', 'outlined', 'text'],
  'Nova',
);

export default buttonClasses;
