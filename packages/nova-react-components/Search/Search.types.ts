import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { InputProps } from '../Input';
import { InputOwnerState } from '../Input/Input.types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../types/slot';
import { FormControlProps } from '../FormControl';

type InputRootKeys =
  | 'autoComplete'
  | 'autoFocus'
  | 'fullWidth'
  | 'placeholder'
  | 'defaultValue'
  | 'value'
  | 'type'
  | 'size'
  | 'startDecorator'
  | 'endDecorator';

export interface SearchSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the input.
   */
  input?: React.ElementType;
  /**
   * The component that renders the inner html input.
   */
  htmlInput?: React.ElementType;
}

export type SearchSlotsAndSlotProps = CreateSlotsAndSlotProps<
  SearchSlots,
  {
    root: FormControlProps;
    input: Omit<InputProps, InputRootKeys>;
    htmlInput: SlotProps<'input', object, InputOwnerState>;
  }
>;

export interface SearchTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    SearchSlotsAndSlotProps &
    Pick<InputProps, InputRootKeys> & {
      /**
       * The id of the `input` element.
       */
      id?: string;
      /**
       * Name attribute of the `input` element.
       */
      name?: string;
      /**
       * Callback fired when the `input` is blurred.
       *
       * Notice that the first argument (event) might be undefined.
       */
      onBlur?: React.FocusEventHandler<HTMLInputElement | HTMLTextAreaElement>;
      /**
       * Callback fired when the value is changed.
       *
       * @param {React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>} event The event source of the callback.
       * You can pull out the new value by accessing `event.target.value` (string).
       */
      onChange?: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>;
      onFocus?: React.FocusEventHandler<HTMLInputElement | HTMLTextAreaElement>;
      onKeyDown?: React.KeyboardEventHandler<HTMLTextAreaElement | HTMLInputElement>;
      onKeyUp?: React.KeyboardEventHandler<HTMLTextAreaElement | HTMLInputElement>;
    };
  defaultComponent: D;
}

export type SearchProps<
  D extends React.ElementType = SearchTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<SearchTypeMap<P, D>, D>;

export interface SearchOwnerState extends SearchProps {}
