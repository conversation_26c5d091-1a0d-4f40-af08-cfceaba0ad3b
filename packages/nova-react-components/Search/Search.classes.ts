import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface SearchClasses {
  /** Class name applied to the root element. */
  root: string;
}

export type SearchClassKey = keyof SearchClasses;

export function getSearchUtilityClass(slot: string): string {
  return generateUtilityClass('NovaSearch', slot, 'Nova');
}

const SearchClasses: SearchClasses = generateUtilityClasses('NovaSearch', ['root'], 'Nova');

export default SearchClasses;
