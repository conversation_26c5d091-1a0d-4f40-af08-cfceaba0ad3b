import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../../types/slot';

export type AccordionItemSlot = 'root';

export interface AccordionItemSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type AccordionItemSlotsAndSlotProps = CreateSlotsAndSlotProps<
  AccordionItemSlots,
  {
    root: SlotProps<'div', object, AccordionItemOwnerState>;
  }
>;

export interface AccordionItemTypeMap<ExtraProps = object, Tag extends React.ElementType = 'div'> {
  props: ExtraProps & {
    /**
     * The id to be used in the AccordionDetails which is controlled by the AccordionSummary.
     * If not provided, the id is autogenerated.
     */
    accordionId?: string;
    /**
     * If `true`, expands the accordion by default.
     * @default false
     */
    defaultExpanded?: boolean;
    /**
     * If `true`, the component is disabled.
     * @default false
     */
    disabled?: boolean;
    /**
     * If `true`, expands the accordion, otherwise collapse it.
     * Setting this prop enables control over the accordion.
     */
    expanded?: boolean;
    /**
     * Callback fired when the expand/collapse state is changed.
     *
     * @param {React.SyntheticEvent} event The event source of the callback. **Warning**: This is a generic event not a change event.
     * @param {boolean} expanded The `expanded` state of the accordion.
     */
    onChange?: (event: React.SyntheticEvent, expanded: boolean) => void;
  } & AccordionItemSlotsAndSlotProps;
  defaultComponent: Tag;
}

export type AccordionItemProps<
  Tag extends React.ElementType = AccordionItemTypeMap['defaultComponent'],
  ExtraProps = SlotCommonProps,
> = OverrideProps<AccordionItemTypeMap<ExtraProps, Tag>, Tag>;

export interface AccordionItemOwnerState extends AccordionItemProps {}
