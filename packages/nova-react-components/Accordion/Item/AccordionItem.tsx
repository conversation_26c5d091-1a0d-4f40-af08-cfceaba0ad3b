'use client';
import * as React from 'react';
import {
  unstable_composeClasses as composeClasses,
  unstable_useControlled as useControlled,
  unstable_useId as useId,
} from '@mui/utils';
import { styled } from '@pigment-css/react';
import { getAccordionItemUtilityClass } from './AccordionItem.classes';
import { AccordionItemOwnerState, AccordionItemProps } from './AccordionItem.types';
import useSlotProps from '@mui/utils/useSlotProps';
import AccordionItemContext from '../Context/AccordionItemContext';
import AccordionGroupContext from '../Context/AccordionGroupContext';
import { ListItemRoot as StyledListItem } from '../../ListItem/ListItem';
import { accordionDetailsClasses } from '../Details';

const useUtilityClasses = (ownerState: AccordionItemOwnerState) => {
  const { expanded, disabled } = ownerState;
  const slots = {
    root: ['root', expanded && 'expanded', disabled && 'disabled'],
  };

  return composeClasses(slots, getAccordionItemUtilityClass, {});
};

const AccordionItemRoot = styled(StyledListItem as unknown as 'div', {
  name: 'NovaAccordionItem',
  slot: 'Root',
  overridesResolver: (props, styles) => styles.root,
})<AccordionItemOwnerState>(({ theme }) => ({
  ...theme.typography.bodyMedium,
  backgroundColor: theme.vars.palette.surfaceContainer,
  display: 'flex',
  flexDirection: 'column',
  '--nova-listItem-gap': '0px',
  padding: 0,
  borderBottom: 'var(--nova-accordion-borderBottom)',
  '&[data-first-child]': {
    '--nova-listItem-radius': 'var(--nova-unstable_List-childRadius) var(--nova-unstable_List-childRadius) 0 0',
  },
  '&[data-last-child]': {
    '--nova-listItem-radius': '0 0 var(--nova-unstable_List-childRadius) var(--nova-unstable_List-childRadius)',
    '& [aria-expanded="true"]': {
      '--nova-listItem-radius': '0',
    },
    [`& .${accordionDetailsClasses.root}`]: {
      '--nova-accordionDetails-radius':
        '0 0 var(--nova-unstable_List-childRadius) var(--nova-unstable_List-childRadius)',
    },
  },
  '&:not([data-first-child]):not([data-last-child])': {
    '--nova-listItem-radius': '0',
  },
}));

// eslint-disable-next-line react/display-name
export const AccordionItem = React.forwardRef<HTMLDivElement, AccordionItemProps>(function AccordionItem(props, ref) {
  const {
    accordionId: idOverride,
    component = 'div',
    children,
    defaultExpanded = false,
    disabled = false,
    expanded: expandedProp,
    onChange,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const accordionId = useId(idOverride);
  const { density } = React.useContext(AccordionGroupContext);

  const [expanded, setExpandedState] = useControlled({
    controlled: expandedProp,
    default: defaultExpanded,
    name: 'AccordionItem',
    state: 'expanded',
  });

  const handleChange = React.useCallback(
    (event: React.SyntheticEvent) => {
      setExpandedState(!expanded);

      if (onChange) {
        onChange(event, !expanded);
      }
    },
    [expanded, onChange, setExpandedState],
  );

  const contextValue = React.useMemo(
    () => ({ accordionId, expanded, disabled, toggle: handleChange }),
    [accordionId, expanded, disabled, handleChange],
  );

  const ownerState = {
    ...props,
    component,
    density,
    expanded,
    disabled,
    nested: true,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? AccordionItemRoot;
  const rootProps = useSlotProps({
    elementType: AccordionItemRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      as: component,
      onChange: handleChange,
    },
    ownerState,
    className: classes.root,
  });

  return (
    <AccordionItemContext.Provider value={contextValue}>
      <SlotRoot {...rootProps}>
        {React.Children.map(children, (child, index) =>
          React.isValidElement(child) && index === 0
            ? React.cloneElement(child, {
                // @ts-expect-error: to let ListItem knows when to apply margin(Inline|Block)Start
                'data-first-child': '',
              })
            : child,
        )}
      </SlotRoot>
    </AccordionItemContext.Provider>
  );
});
