import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../../types/slot';

export type AccordionDetailsSlot = 'root' | 'content';

export interface AccordionDetailsSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the content.
   * @default 'div'
   */
  content?: React.ElementType;
}

export type AccordionDetailsSlotsAndSlotProps = CreateSlotsAndSlotProps<
  AccordionDetailsSlots,
  {
    root: SlotProps<'div', object, AccordionDetailsOwnerState>;
    content: SlotProps<'div', object, AccordionDetailsOwnerState>;
  }
>;

export interface AccordionDetailsTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & AccordionDetailsSlotsAndSlotProps;
  defaultComponent: D;
}

export type AccordionDetailsProps<
  D extends React.ElementType = AccordionDetailsTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<AccordionDetailsTypeMap<P, D>, D>;

export interface AccordionDetailsOwnerState extends AccordionDetailsProps {
  /**
   * The size of the component.
   * @default 'standard'
   */
  density?: 'standard' | 'compact' | 'comfortable';
  /**
   * If `true`, the accordion details is disabled.
   */
  disabled: boolean;
  /**
   * The expanded state of the accordion.
   */
  expanded: boolean;
}
