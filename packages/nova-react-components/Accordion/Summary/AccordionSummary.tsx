'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import accordionSummaryClasses, { getAccordionSummaryUtilityClass } from './AccordionSummary.classes';
import { AccordionSummaryOwnerState, AccordionSummaryProps } from './AccordionSummary.types';
import useSlotProps from '@mui/utils/useSlotProps';
import { ListItemRoot as StyledListItem } from '../../ListItem/ListItem';
import AccordionItemContext from '../Context/AccordionItemContext';
import KeyboardArrowDown from '../../internal/svg-icons/KeyboardArrowDown';
import { StyledListItemButton } from '../../ListItemButton/ListItemButton';
import AccordionGroupContext from '../Context/AccordionGroupContext';

const useUtilityClasses = (ownerState: AccordionSummaryOwnerState) => {
  const { disabled, expanded } = ownerState;
  const slots = {
    root: ['root', disabled && 'disabled', expanded && 'expanded'],
    button: ['button', disabled && 'disabled', expanded && 'expanded'],
    indicator: ['indicator', disabled && 'disabled', expanded && 'expanded'],
  };

  return composeClasses(slots, getAccordionSummaryUtilityClass, {});
};

const AccordionSummaryRoot = styled(StyledListItem as unknown as 'div', {
  name: 'NovaAccordionSummary',
  slot: 'Root',
  overridesResolver: (props, styles) => styles.root,
})<AccordionSummaryOwnerState>(({ theme }) => ({
  '--nova-listItem-minHeight': '48px',
  variants: [
    {
      props: { density: 'compact' },
      style: {
        '--nova-listItem-minHeight': '40px',
      },
    },
    {
      props: { density: 'comfortable' },
      style: {
        '--nova-listItem-minHeight': '56px',
      },
    },
    {
      props: { disabled: true },
      style: {
        backgroundColor: theme.vars.palette.backgroundDisabled,
      },
    },
  ],
  width: '100%',
  ...theme.typography.bodyMedium,
  fontWeight: 400,
  '--nova-listItemButton-marginInline': '0px',
  '--nova-listItemButton-marginBlock': '0px',
  '--nova-listItem-paddingY': '0px',
  gap: 'calc(var(--nova-listItem-paddingX, 0.75rem) + 0.25rem)',
  [`&.${accordionSummaryClasses.expanded}`]: {
    '--nova-icon-color': 'currentColor',
  },
  padding: 0,
  '&:hover': {
    '& + .NovaAccordionDetails-root': {
      backgroundColor: theme.vars.palette.surfaceContainer,
    },
  },
}));

const AccordionSummaryButton = styled(StyledListItemButton as unknown as 'button', {
  name: 'NovaAccordionSummary',
  slot: 'Button',
  overridesResolver: (props, styles) => styles.button,
})<{ ownerState: AccordionSummaryOwnerState }>(({ theme }) => ({
  gap: 'inherit',
  fontWeight: 'inherit',
  justifyContent: 'space-between',
  font: 'inherit',
  paddingLeft: '17px',
  paddingRight: '17px',
  '&:focus-visible': {
    zIndex: 1,
    outline: `2px solid ${theme.vars.palette.secondary}`,
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },
  [`.${accordionSummaryClasses.root} &`]: {
    '--nova-unstable_ListItem-flex': '1 0 0%',
    color: theme.vars.palette.onSurface,
  },
  [`.${accordionSummaryClasses.disabled} &`]: {
    color: theme.vars.palette.onBackgroundDisabled,
    '&:hover': {
      backgroundColor: 'transparent',
    },
  },
}));

const AccordionSummaryIndicator = styled('span', {
  name: 'NovaAccordionSummary',
  slot: 'Indicator',
  overridesResolver: (props, styles) => styles.indicator,
})<{ ownerState: AccordionSummaryOwnerState }>(({ theme }) => ({
  display: 'inline-flex',
  [`&.${accordionSummaryClasses.expanded}`]: {
    transform: 'rotate(180deg)',
  },
  [`.${accordionSummaryClasses.disabled} & svg`]: {
    fill: theme.vars.palette.onBackgroundDisabled,
  },
}));

// eslint-disable-next-line react/display-name
export const AccordionSummary = React.forwardRef(function AccordionSummary(
  props: AccordionSummaryProps,
  ref: React.Ref<HTMLElement>,
) {
  const {
    component = 'div',
    children,
    indicator = <KeyboardArrowDown />,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const { accordionId, disabled = false, expanded = false, toggle } = React.useContext(AccordionItemContext);
  const { density } = React.useContext(AccordionGroupContext);

  const ownerState = {
    ...props,
    component,
    density,
    disabled,
    expanded,
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (toggle) {
      toggle(event);
    }
    if (typeof slotProps.button === 'function') {
      slotProps.button(ownerState)?.onClick?.(event);
    } else {
      slotProps.button?.onClick?.(event);
    }
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? AccordionSummaryRoot;
  const rootProps = useSlotProps({
    elementType: AccordionSummaryRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    className: classes.root,
  });

  const SlotButton = slots.button ?? AccordionSummaryButton;
  const buttonProps = useSlotProps({
    className: classes.button,
    elementType: AccordionSummaryButton,
    externalSlotProps: slotProps.button,
    additionalProps: {
      as: 'button',
      id: `${accordionId}-summary`,
      'aria-expanded': expanded ? 'true' : 'false',
      'aria-controls': `${accordionId}-details`,
      disabled,
      type: 'button',
      onClick: handleClick,
      tabIndex: disabled ? -1 : 0,
    },
    ownerState,
  });

  const SlotIndicator = slots.indicator ?? AccordionSummaryIndicator;
  const indicatorProps = useSlotProps({
    elementType: AccordionSummaryIndicator,
    externalSlotProps: slotProps.indicator,
    ownerState,
    className: classes.indicator,
  });

  return (
    <SlotRoot {...rootProps} {...ownerState}>
      <SlotButton {...buttonProps}>
        {children}
        {indicator && <SlotIndicator {...indicatorProps}>{indicator}</SlotIndicator>}
      </SlotButton>
    </SlotRoot>
  );
});
