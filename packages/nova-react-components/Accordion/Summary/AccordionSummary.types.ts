import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../../types/slot';

export type AccordionSummarySlot = 'root' | 'button' | 'indicator';

export interface AccordionSummarySlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
  /**
   * The component that renders the button.
   * @default 'button'
   */
  button?: React.ElementType;
  /**
   * The component that renders the indicator.
   * @default 'span'
   */
  indicator?: React.ElementType;
}

export type AccordionSummarySlotsAndSlotProps = CreateSlotsAndSlotProps<
  AccordionSummarySlots,
  {
    root: SlotProps<'div', object, AccordionSummaryOwnerState>;
    button: SlotProps<'button', object, AccordionSummaryOwnerState>;
    indicator: SlotProps<'span', object, AccordionSummaryOwnerState>;
  }
>;

export interface AccordionSummaryTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The indicator element to display.
     * @default <KeyboardArrowDown />
     */
    indicator?: React.ReactNode;
  } & AccordionSummarySlotsAndSlotProps;
  defaultComponent: D;
}

export type AccordionSummaryProps<
  D extends React.ElementType = AccordionSummaryTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<AccordionSummaryTypeMap<P, D>, D>;

export interface AccordionSummaryOwnerState extends AccordionSummaryProps {
  /**
   * If `true`, the accordion is disabled.
   */
  disabled: boolean;
  /**
   * The expanded state of the accordion.
   */
  expanded: boolean;
}
