'use client';
import * as React from 'react';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import { getAccordionGroupUtilityClass } from './AccordionGroup.classes';
import { AccordionGroupOwnerState, AccordionGroupProps } from './AccordionGroup.types';
import useSlotProps from '@mui/utils/useSlotProps';
import { accordionDetailsClasses } from '../Details';
import { accordionItemClasses } from '../Item';
import { ListRoot as StyledList } from '../../List/List';
import AccordionGroupContext from '../Context/AccordionGroupContext';

const useUtilityClasses = (ownerState: AccordionGroupOwnerState) => {
  const { density } = ownerState;
  const slots = {
    root: ['root', density && `density${capitalize(density)}`],
  };

  return composeClasses(slots, getAccordionGroupUtilityClass, {});
};

const AccordionGroupRoot = styled(StyledList as unknown as 'div', {
  name: 'NovaAccordionGroup',
  slot: 'Root',
  overridesResolver: (props, styles) => styles.root,
})<AccordionGroupOwnerState>(({ theme }) => ({
  '--nova-list-padding': '0px',
  '--nova-listDivider-gap': '0px',
  variants: [
    {
      props: (props) => props.disableDivider !== true,
      style: {
        [`& .${accordionItemClasses.root}:not([data-last-child])`]: {
          '--nova-accordion-borderBottom': `1px solid ${theme.vars.palette.outlineVariant}`,
        },
      },
    },
  ],
}));

// eslint-disable-next-line react/display-name
export const AccordionGroup = React.forwardRef(function AccordionGroup(
  props: AccordionGroupProps,
  ref: React.Ref<HTMLElement>,
) {
  const {
    component = 'div',
    children,
    disableDivider = false,
    transition = '0.2s ease',
    density = 'standard',
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const ownerState = {
    ...props,
    component,
    disableDivider,
    transition,
    density,
  };

  const classes = useUtilityClasses(ownerState);

  const transitionStyles = React.useMemo(() => {
    if (transition && typeof transition === 'string') {
      return {
        '--nova-accordionDetails-transition': `grid-template-rows ${transition}, padding-block ${transition}`,
      };
    } else if (transition && typeof transition === 'object') {
      return {
        '--nova-accordionDetails-transition': `grid-template-rows ${transition.initial}, padding-block ${transition.initial}`,
        [`& .${accordionDetailsClasses.root}.${accordionDetailsClasses.expanded}`]: {
          '--nova-accordionDetails-transition': `grid-template-rows ${transition.expanded}, padding-block ${transition.expanded}`,
        },
      };
    } else {
      return { '--nova-accordionDetails-transition': 'grid-template-rows 0.2s ease, padding-block 0.2s ease' };
    }
  }, [transition]);

  const SlotRoot = slots.root ?? AccordionGroupRoot;
  const rootProps = useSlotProps({
    elementType: AccordionGroupRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      as: component,
      ...transitionStyles,
    },
    ownerState,
    className: classes.root,
  });

  return (
    <AccordionGroupContext.Provider value={{ density }}>
      <SlotRoot {...rootProps}>{children}</SlotRoot>
    </AccordionGroupContext.Provider>
  );
});
