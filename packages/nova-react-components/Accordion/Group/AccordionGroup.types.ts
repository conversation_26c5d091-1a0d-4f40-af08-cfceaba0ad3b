import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../../types/slot';

export type AccordionGroupSlot = 'root';

export interface AccordionGroupSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type AccordionGroupSlotsAndSlotProps = CreateSlotsAndSlotProps<
  AccordionGroupSlots,
  {
    root: SlotProps<'div', object, AccordionGroupOwnerState>;
  }
>;

export interface AccordionGroupTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * If `true`, the divider between accordions will be hidden.
     * @default false
     */
    disableDivider?: boolean;
    /**
     * The size of the component.
     * @default 'standard'
     */
    density?: 'standard' | 'compact' | 'comfortable';
    /**
     * The CSS transition for the Accordion details.
     * @default '0.2s ease'
     */
    transition?: string | { initial: string; expanded: string };
  } & AccordionGroupSlotsAndSlotProps;
  defaultComponent: D;
}

export type AccordionGroupProps<
  D extends React.ElementType = AccordionGroupTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<AccordionGroupTypeMap<P, D>, D>;

export interface AccordionGroupOwnerState extends AccordionGroupProps {}
