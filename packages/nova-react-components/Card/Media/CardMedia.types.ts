import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../../types/slot';

export interface CardMediaSlots {
  root?: React.ElementType;
}

export type CardMediaSlotsAndSlotProps = CreateSlotsAndSlotProps<
  CardMediaSlots,
  {
    root: SlotProps<'div', object, CardMediaOwnerState>;
  }
>;

export interface CardMediaTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * Image to be displayed as a background image.
     * Either `image` or `src` prop must be specified.
     * Note that caller must specify height otherwise the image will not be visible.
     */
    image?: string;
    /**
     * An alias for `image` property.
     * Available only with media components.
     * Media components: `video`, `audio`, `picture`, `iframe`, `img`.
     */
    src?: string;
  } & CardMediaSlotsAndSlotProps;
  defaultComponent: D;
}

export type CardMediaProps<
  D extends React.ElementType = CardMediaTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<CardMediaTypeMap<P, D>, D>;

export interface CardMediaOwnerState extends CardMediaProps {
  isMediaComponent: boolean;
  isImageComponent: boolean;
}
