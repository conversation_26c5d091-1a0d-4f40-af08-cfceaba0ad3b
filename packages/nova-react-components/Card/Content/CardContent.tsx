'use client';
import * as React from 'react';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { CardContentOwnerState, CardContentProps } from './CardContent.types';
import { getCardContentUtilityClass } from './CardContent.classes';
import { Typography } from '../../Typography';
import CardContext from '../Context/CardContext';

const useUtilityClasses = (ownerState: CardContentOwnerState) => {
  const { disabled, orientation } = ownerState;
  const slots = {
    root: ['root', orientation && `orientation${capitalize(orientation)}`],
    body: ['body'],
    bodyMain: ['bodyMain'],
    action: ['action'],
    title: ['title', disabled && 'disabled'],
    subtitle: ['subtitle', disabled && 'disabled'],
    supportingText: ['supportingText', disabled && 'disabled'],
  };

  return composeClasses(slots, getCardContentUtilityClass, {});
};

const lineLimit = (line: number = 1): React.CSSProperties => ({
  overflow: 'hidden',
  display: '-webkit-box',
  WebkitLineClamp: line,
  WebkitBoxOrient: 'vertical',
});

const tooltip = (value: any) => typeof value === 'string' && { title: value };

const CardContentRoot = styled('div')<CardContentOwnerState>(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'flex-start',
  alignSelf: 'stretch',
  variants: [
    {
      props: { orientation: 'horizontal' },
      style: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: '12px 16px 12px 24px',
        flex: 1,
        gap: '32px',
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        flexDirection: 'column',
        padding: '24px 16px 0 24px',
        gap: '16px',
        '&:last-child': {
          paddingBottom: '24px',
        },
      },
    },
  ],
}));

const CardContentBody = styled('div')<CardContentOwnerState>(({ theme }) => ({
  display: 'flex',
  width: '100%',
  alignItems: 'center',
}));

const CardContentBodyMain = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  flex: 1,
}));

const CardContentTitle = styled(Typography)<CardContentOwnerState>(({ theme }) => ({
  color: theme.vars.palette.onSurface,
  fontWeight: 400,
  ...lineLimit(1),
  variants: [
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
  ],
}));

const CardContentSubtitle = styled(Typography)<CardContentOwnerState>(({ theme }) => ({
  color: theme.vars.palette.onSurface,
  fontWeight: 400,
  variants: [
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { orientation: 'horizontal', lineLimited: 'one' },
      style: {
        ...lineLimit(1),
      },
    },
    {
      props: { orientation: 'horizontal', lineLimited: 'two' },
      style: {
        ...lineLimit(2),
      },
    },
  ],
}));

const CardContentSupportingText = styled(Typography)<CardContentOwnerState>(({ theme }) => ({
  color: theme.vars.palette.onSurfaceVariant,
  fontWeight: 400,
  variants: [
    {
      props: { disabled: true },
      style: {
        color: theme.vars.palette.onBackgroundDisabled,
      },
    },
    {
      props: { orientation: 'horizontal', lineLimited: 'one' },
      style: {
        ...lineLimit(1),
      },
    },
    {
      props: { orientation: 'horizontal', lineLimited: 'two' },
      style: {
        ...lineLimit(2),
      },
    },
  ],
}));

const CardHeaderAction = styled('div')<CardContentOwnerState>(({ theme }) => ({}));

// eslint-disable-next-line react/display-name
export const CardContent = React.forwardRef((props: CardContentProps, ref: React.ForwardedRef<Element>) => {
  const {
    orientation: orientationProp = 'vertical',
    startDecorator,
    endDecorator,
    action,
    title,
    subtitle,
    supportingText,
    children,
    component,
    slots = {},
    slotProps = {},
    ...rest
  } = props;

  const cardContext = React.useContext(CardContext);
  const orientation = props.orientation ?? cardContext?.orientation ?? orientationProp;
  const disabled = cardContext?.disabled ?? false;
  const hasPropsContent = Boolean(title || subtitle || supportingText || action);
  const lineLimited = subtitle && supportingText ? 'one' : 'two';
  const ownerState: CardContentOwnerState = { ...props, orientation, disabled, lineLimited };
  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? CardContentRoot;
  const SlotBody = slots.body ?? CardContentBody;
  const SlotTitle = slots.title ?? CardContentTitle;
  const SlotSubtitle = slots.subtitle ?? CardContentSubtitle;
  const SlotSupportingText = slots.supportingText ?? CardContentSupportingText;
  const SlotAction = slots.action ?? CardHeaderAction;

  const slotRootProps = useSlotProps({
    elementType: SlotRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: rest,
    additionalProps: {
      ref: ref,
      as: component,
    },
    ownerState: ownerState,
    className: classes.root,
  });

  const slotBodyProps = useSlotProps({
    elementType: SlotBody,
    externalSlotProps: slotProps.body,
    ownerState: ownerState,
    className: classes.body,
  });

  const slotTitleProps = useSlotProps({
    elementType: SlotTitle,
    externalSlotProps: slotProps.title,
    additionalProps: {
      variant: 'titleSmall',
      ...tooltip(title),
    },
    ownerState: ownerState,
    className: classes.title,
  });

  const slotSubtitleProps = useSlotProps({
    elementType: SlotSubtitle,
    externalSlotProps: slotProps.subtitle,
    additionalProps: {
      variant: 'bodyMedium',
      ...tooltip(subtitle),
    },
    ownerState: ownerState,
    className: classes.subtitle,
  });

  const slotSupportingTextProps = useSlotProps({
    elementType: SlotSupportingText,
    externalSlotProps: slotProps.supportingText,
    additionalProps: {
      variant: 'bodyMedium',
      ...tooltip(supportingText),
    },
    ownerState: ownerState,
    className: classes.supportingText,
  });

  const slotActionProps = useSlotProps({
    elementType: SlotAction,
    externalSlotProps: slotProps.action,
    ownerState: ownerState,
    className: classes.action,
  });

  return (
    <SlotRoot {...slotRootProps}>
      {startDecorator}
      {hasPropsContent && (
        <SlotBody {...slotBodyProps}>
          <CardContentBodyMain className={classes.bodyMain}>
            {title && <SlotTitle {...slotTitleProps}>{title}</SlotTitle>}
            {subtitle && <SlotSubtitle {...slotSubtitleProps}>{subtitle}</SlotSubtitle>}
            {supportingText && <SlotSupportingText {...slotSupportingTextProps}>{supportingText}</SlotSupportingText>}
          </CardContentBodyMain>
          {action && <SlotAction {...slotActionProps}>{action}</SlotAction>}
        </SlotBody>
      )}
      {children}
      {endDecorator}
    </SlotRoot>
  );
});
