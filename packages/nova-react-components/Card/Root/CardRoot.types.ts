import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../../types/slot';

export interface CardRootSlots {
  /**
   * The component that renders the root slot.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type CardRootSlotsAndSlotProps = CreateSlotsAndSlotProps<
  CardRootSlots,
  {
    root: SlotProps<'div', object, CardRootOwnerState>;
  }
>;

export interface CardRootTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The orientation of the card.
     * @default 'vertical'
     */
    orientation?: 'vertical' | 'horizontal';
    /**
     * Indicate the disabled state of the card.
     * @default false
     */
    disabled?: boolean;
  } & CardRootSlotsAndSlotProps;
  defaultComponent: D;
}

export type CardRootProps<
  D extends React.ElementType = CardRootTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<CardRootTypeMap<P, D>, D>;

export interface CardRootOwnerState extends CardRootProps {
  clickable: boolean;
}
