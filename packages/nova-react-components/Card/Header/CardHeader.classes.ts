import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface CardHeaderClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the avatar element. */
  avatar: string;
  /** Class name applied to the action element. */
  action: string;
  /** Class name applied to the content element. */
  content: string;
  /** Class name applied to the heading element. */
  heading: string;
  /** Class name applied to the subheading element. */
  subheading: string;
  /** Class name applied to the heading and subheading element if `disabled={true}`. */
  disabled: string;
}

export type CardHeaderClassKey = keyof CardHeaderClasses;

export function getCardHeaderUtilityClass(slot: string): string {
  return generateUtilityClass('NovaCardHeader', slot, 'Nova');
}

const cardHeaderClasses: CardHeaderClasses = generateUtilityClasses(
  'NovaCardHeader',
  ['root', 'avatar', 'action', 'content', 'heading', 'subheading', 'disabled'],
  'Nova',
);

export default cardHeaderClasses;
