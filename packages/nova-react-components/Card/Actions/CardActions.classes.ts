import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface CardActionsClasses {
  /** Class name applied to the root element. */
  root: string;
}

export type CardActionsClassKey = keyof CardActionsClasses;

export function getCardActionsUtilityClass(slot: string): string {
  return generateUtilityClass('NovaCardActions', slot, 'Nova');
}

const cardActionsClasses: CardActionsClasses = generateUtilityClasses('NovaCardActions', ['root'], 'Nova');

export default cardActionsClasses;
