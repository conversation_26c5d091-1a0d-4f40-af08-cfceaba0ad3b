import * as React from 'react';
import { render, screen, cleanup, fireEvent } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { SegmentedButtonGroup } from './SegmentedButtonGroup';
import { SegmentedButton } from '../SegmentedButton';

afterEach(() => {
  cleanup();
});

describe('<SegmentedButtonGroup />', () => {
  it('provide context to SegmentedButton', () => {
    const { container } = render(
      <SegmentedButtonGroup exclusive value="1" onChange={() => {}}>
        <SegmentedButton value="1">Button 1</SegmentedButton>
        <SegmentedButton value="2">Button 2</SegmentedButton>
      </SegmentedButtonGroup>,
    );
    const firstChild = container.firstChild;
    if (firstChild === null) {
      return;
    }
    const buttons = container.firstChild?.firstChild;
    expect(buttons).toHaveClass('NovaButtonBase-sizeMedium');
    expect(buttons).toHaveClass('NovaSegmentedButtonGroup-grouped');
    expect(buttons).toHaveClass('NovaSegmentedButtonGroup-groupedHorizontal');
    expect(buttons).toHaveClass('NovaSegmentedButtonGroup-firstButton');
  });

  it('should call onChange when exclusive is true', () => {
    const onChange = vi.fn();
    render(
      <SegmentedButtonGroup exclusive value="1" onChange={onChange}>
        <SegmentedButton data-testid="first-btn" value="1">
          Button 1
        </SegmentedButton>
        <SegmentedButton data-testid="second-btn" value="2">
          Button 2
        </SegmentedButton>
      </SegmentedButtonGroup>,
    );

    expect(screen.getByTestId('first-btn')).toHaveClass('Nova-selected');

    fireEvent.click(screen.getByTestId('second-btn'));
    expect(onChange).toHaveBeenCalledTimes(1);
  });

  it('should call onChange when exclusive is false', () => {
    const onChange = vi.fn();
    render(
      <SegmentedButtonGroup value={['1', '2']} onChange={onChange}>
        <SegmentedButton data-testid="first-btn" value="1">
          Button 1
        </SegmentedButton>
        <SegmentedButton data-testid="second-btn" value="2">
          Button 2
        </SegmentedButton>
      </SegmentedButtonGroup>,
    );

    expect(screen.getByTestId('first-btn')).toHaveClass('Nova-selected');
    expect(screen.getByTestId('second-btn')).toHaveClass('Nova-selected');

    fireEvent.click(screen.getByTestId('second-btn'));
    expect(onChange).toHaveBeenCalledTimes(1);
  });

  it('should render with disabled slot class', () => {
    render(
      <SegmentedButtonGroup disabled>
        <SegmentedButton>Click Me</SegmentedButton>
      </SegmentedButtonGroup>,
    );
    const button = screen.getByRole('button');
    expect(button).toHaveClass('Nova-disabled');
  });

  it('should render with small size slot class', () => {
    render(
      <SegmentedButtonGroup size="small">
        <SegmentedButton>Click Me</SegmentedButton>
      </SegmentedButtonGroup>,
    );
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaButtonBase-sizeSmall');
  });

  it('should render with large size slot class', () => {
    render(
      <SegmentedButtonGroup size="large">
        <SegmentedButton>Click Me</SegmentedButton>
      </SegmentedButtonGroup>,
    );
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaButtonBase-sizeLarge');
  });

  it('should have `orientation` class when vertical', () => {
    render(
      <SegmentedButtonGroup orientation="vertical">
        <SegmentedButton>Click Me</SegmentedButton>
      </SegmentedButtonGroup>,
    );
    const button = screen.getByRole('button');
    expect(button).toHaveClass('NovaSegmentedButtonGroup-groupedVertical');
  });
});
