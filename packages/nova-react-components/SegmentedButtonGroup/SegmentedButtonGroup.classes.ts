import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface SegmentedButtonGroupClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Class name applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Class name applied to the root element if `size="large"`. */
  sizeLarge: string;
  /** Class name applied to the root element if `orientation="horizontal"`. */
  horizontal: string;
  /** Class name applied to the root element if `orientation="vertical"`. */
  vertical: string;
  /** Class name applied to the children. */
  grouped: string;
  /** Class name applied to the first button in the segmented button group. */
  firstButton: string;
  /** Class name applied to the last button in the segmented button group. */
  lastButton: string;
  /** Class name applied to buttons in the middle of the segmented button group. */
  middleButton: string;
}

export type SegmentedButtonGroupClassKey = keyof SegmentedButtonGroupClasses;

export function getSegmentedButtonGroupUtilityClass(slot: string): string {
  return generateUtilityClass('NovaSegmentedButtonGroup', slot, 'Nova');
}

const segmentedButtonGroupClasses: SegmentedButtonGroupClasses = generateUtilityClasses(
  'NovaSegmentedButtonGroup',
  [
    'root',
    'sizeMedium',
    'sizeSmall',
    'sizeLarge',
    'horizontal',
    'vertical',
    'grouped',
    'firstButton',
    'lastButton',
    'middleButton',
  ],
  'Nova',
);

export default segmentedButtonGroupClasses;
