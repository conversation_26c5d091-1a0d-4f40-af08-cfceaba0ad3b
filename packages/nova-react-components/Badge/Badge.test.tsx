import '@testing-library/jest-dom/vitest';
import { render, screen, cleanup } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, afterEach } from 'vitest';
import { Badge } from './Badge.tsx';

afterEach(() => {
  cleanup();
});

describe('Badge', () => {
  it('should render normal', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        badgeContent={10}
      />,
    );
    expect(screen.getByTestId('NovaBadge-root')).toBeDefined();
    expect(screen.getByTestId('NovaBadge-content')).toBeDefined();
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-colorPrimary');
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-sizeLarge');
    expect(screen.getByTestId('NovaBadge-content')).toHaveTextContent('10');
  });

  it('should render small badge', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        size="small"
        badgeContent={10}
      />,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-sizeSmall');
    expect(screen.getByTestId('NovaBadge-content')).toHaveTextContent('');
  });

  it('should render info badge', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="info"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        badgeContent={10}
      />,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-colorInfo');
  });

  it('should render warning badge', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="warning"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        badgeContent={10}
      />,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-colorWarning');
  });

  it('should render success badge', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="success"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        badgeContent={10}
      />,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-colorSuccess');
  });

  it('should render primary badge', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="primary"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        badgeContent={10}
      />,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-colorPrimary');
  });

  it('should render badge at top-right by default', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="primary"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        badgeContent={10}
      >
        <div>ok</div>
      </Badge>,
    );
    expect(screen.getByText('ok')).toBeInTheDocument();
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-anchorOriginTopRight');
  });

  it('should render badge at top-left', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="primary"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        badgeContent={10}
      >
        <div>ok</div>
      </Badge>,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-anchorOriginTopLeft');
  });

  it('should render badge at bottom-left', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="primary"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        badgeContent={10}
      >
        <div>ok</div>
      </Badge>,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-anchorOriginBottomLeft');
  });

  it('should render disabled state', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        disabled
        badgeContent={10}
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
      >
        <div>ok</div>
      </Badge>,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('Nova-disabled');
  });

  it('should render badge at bottom-right', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="primary"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        badgeContent={10}
      >
        <div>ok</div>
      </Badge>,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveClass('NovaBadge-anchorOriginBottomRight');
  });

  it('should render max number badge with 99+ ', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="primary"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        badgeContent={1000}
      />,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveTextContent('99+');
  });

  it('should render max number badge with 50+ ', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="primary"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        max={50}
        badgeContent={1000}
      />,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveTextContent('50+');
  });

  it('should not show 0 by default', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="primary"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        badgeContent={0}
      />,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveTextContent('');
  });

  it('should show 0 if showZero applied', () => {
    render(
      <Badge
        data-testid="NovaBadge-root"
        color="primary"
        slotProps={{ badge: { 'data-testid': 'NovaBadge-content' } }}
        badgeContent={0}
        showZero
      />,
    );
    expect(screen.getByTestId('NovaBadge-content')).toHaveTextContent('0');
  });
});
