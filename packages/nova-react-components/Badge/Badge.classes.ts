import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface BadgeClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the badge `span` element. */
  badge: string;

  /** Class name applied to the badge `span` element if `size="small"`. */
  sizeSmall: string;
  /** Class name applied to the badge `span` element if `size="large"`. */
  sizeLarge: string;

  /** Class name applied to the badge `span` element if `anchorOrigin={{ 'top', 'right' }}`. */
  anchorOriginTopRight: string;
  /** Class name applied to the badge `span` element if `anchorOrigin={{ 'bottom', 'right' }}`. */
  anchorOriginBottomRight: string;
  /** Class name applied to the badge `span` element if `anchorOrigin={{ 'top', 'left' }}`. */
  anchorOriginTopLeft: string;
  /** Class name applied to the badge `span` element if `anchorOrigin={{ 'bottom', 'left' }}`. */
  anchorOriginBottomLeft: string;

  /** State class applied to the badge `span` element if `invisible={true}`. */
  invisible: string;

  /** Class name applied to the badge `span` element if `color="primary"`. */
  colorPrimary: string;
  /** Class name applied to the badge `span` element if `color="error"`. */
  colorError: string;
  /** Class name applied to the badge `span` element if `color="info"`. */
  colorInfo: string;
  /** Class name applied to the badge `span` element if `color="warning"`. */
  colorWarning: string;
  /** Class name applied to the badge `span` element if `color="success"`. */
  colorSuccess: string;

  /** Class name applied to the badge `span` element if `disabled` is true. */
  disabled: string;
}

export type BadgeClassKey = keyof BadgeClasses;

export function getBadgeUtilityClass(slot: string): string {
  return generateUtilityClass('NovaBadge', slot, 'Nova');
}

const badgeClasses: BadgeClasses = generateUtilityClasses(
  'NovaBadge',
  [
    'root',
    'badge',

    'sizeSmall',
    'sizeLarge',

    'anchorOriginTopRight',
    'anchorOriginBottomRight',
    'anchorOriginTopLeft',
    'anchorOriginBottomLeft',
    'invisible',

    'colorPrimary',
    'colorError',
    'colorInfo',
    'colorWarning',
    'colorSuccess',

    'disabled',
  ],
  'Nova',
);

export default badgeClasses;
