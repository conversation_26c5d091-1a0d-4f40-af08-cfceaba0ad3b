import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface AutocompleteClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `fullWidth={true}`. */
  fullWidth: string;
  /** State class applied to the root element if the listbox is displayed. */
  expanded: string;
  /** Class name applied to the root element if the input is filled. */
  filled: string;
  /** State class applied to the root element if focused. */
  focused: string;
  /** Class name applied to the option elements if they are keyboard focused. */
  focusVisible: string;
  /** State class applied to the root element if `multiple={true}`. */
  multiple: string;
  /** Class name applied to the tag elements, for example the chips. */
  tag: string;
  /** Class name applied to the tag elements, for example the chips if `size="small"`. */
  tagSizeSmall: string;
  /** Class name applied to the tag elements, for example the chips if `size="medium"`. */
  tagSizeMedium: string;
  /** Class name applied to the tag elements, for example the chips if `size="large"`. */
  tagSizeLarge: string;
  /** Class name applied when the popup icon is rendered. */
  hasPopupIcon: string;
  /** Class name applied when the clear icon is rendered. */
  hasClearIcon: string;
  /** Class name applied to the Input element. */
  inputRoot: string;
  /** Class name applied to the endAdornment element. */
  endAdornment: string;
  /** Class name applied to the clear indicator. */
  clearIndicator: string;
  /** Class name applied to the popup indicator. */
  popupIndicator: string;
  /** Class name applied to the popup indicator if the popup is open. */
  popupIndicatorOpen: string;
  /** Class name applied to the listbox component. */
  listbox: string;
  /** Class name applied to the loading wrapper. */
  loading: string;
  /** Class name applied to the no option wrapper. */
  noOptions: string;
  /** Class name applied to the option elements. */
  option: string;
  /** Class name applied to the group's label elements. */
  groupLabel: string;
}

export type AutocompleteClassKey = keyof AutocompleteClasses;

export function getAutocompleteUtilityClass(slot: string): string {
  return generateUtilityClass('NovaAutocomplete', slot, 'Nova');
}

const autocompleteClasses: AutocompleteClasses = generateUtilityClasses(
  'NovaAutocomplete',
  [
    'root',
    'fullWidth',
    'expanded',
    'filled',
    'fullWidth',
    'focused',
    'focusVisible',
    'multiple',
    'tag',
    'tagSizeSmall',
    'tagSizeMedium',
    'tagSizeLarge',
    'hasPopupIcon',
    'hasClearIcon',
    'inputRoot',
    'endAdornment',
    'clearIndicator',
    'popupIndicator',
    'popupIndicatorOpen',
    'listbox',
    'loading',
    'noOptions',
    'option',
    'groupLabel',
  ],
  'Nova',
);

export default autocompleteClasses;
