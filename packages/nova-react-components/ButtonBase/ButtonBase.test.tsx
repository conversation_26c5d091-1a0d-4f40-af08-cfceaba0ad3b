import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { expect, test, afterEach } from 'vitest';
import { ButtonBase } from './ButtonBase';

afterEach(() => {
  cleanup();
});

test('by default, should render with the root and sizeMedium classes', async () => {
  const { getByRole } = render(<ButtonBase>Click Me</ButtonBase>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-root');
  expect(button).toHaveClass('NovaButtonBase-sizeMedium');
});

test('should render a medium size slot class', () => {
  const { getByRole } = render(<ButtonBase size="medium">Click Me</ButtonBase>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeMedium');
});

test('should render a size small slot class', () => {
  const { getByRole } = render(<ButtonBase size="small">Click Me</ButtonBase>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeSmall');
});

test('should render a size large slot class', () => {
  const { getByRole } = render(<ButtonBase size="large">Click Me</ButtonBase>);
  const button = getByRole('button');

  expect(button).toHaveClass('NovaButtonBase-sizeLarge');
});

test('should render with startIcon slot class', () => {
  render(<ButtonBase startIcon={<span data-testid={'startIcon'}>Icon</span>}>Click Me</ButtonBase>);
  const icon = screen.getByTestId('startIcon');
  expect(icon).toBeInTheDocument();
});

test('should render with endIcon slot class', () => {
  render(<ButtonBase endIcon={<span data-testid={'endIcon'}>Icon</span>}>Click Me</ButtonBase>);
  const icon = screen.getByTestId('endIcon');
  expect(icon).toBeInTheDocument();
});

test('should render with disabled slot class', () => {
  render(<ButtonBase disabled>Click Me</ButtonBase>);
  const button = screen.getByRole('button');
  expect(button).toHaveClass('Nova-disabled');
});

test('should render with fullWidth slot class', () => {
  render(<ButtonBase fullWidth>Click Me</ButtonBase>);
  const button = screen.getByRole('button');
  expect(button).toHaveClass('NovaButtonBase-fullWidth');
});
