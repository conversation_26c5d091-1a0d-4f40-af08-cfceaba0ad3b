import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ButtonBaseClasses {
  /** Class name applied to the root element. */
  root: string;

  /** Class name applied to the Button if `size="small"`. */
  sizeSmall: string;
  /** Class name applied to the Button if `size="medium"`. */
  sizeMedium: string;
  /** Class name applied to the Button if `size="large"`. */
  sizeLarge: string;

  /** Class name applied to the Button startIcon element. */
  iconStart: string;
  /** Class name applied to the Button endIcon element. */
  iconEnd: string;
  /** Class name applied to the Button if `fullWidth={true}`. */
  fullWidth: string;

  /** Class name applied to the Button if `disabled={true}` */
  disabled: string;
}

export type ButtonBaseClassKey = keyof ButtonBaseClasses;

export function getButtonBaseUtilityClass(slot: string): string {
  return generateUtilityClass('NovaButtonBase', slot, 'Nova');
}

const buttonBaseClasses: ButtonBaseClasses = generateUtilityClasses(
  'NovaButtonBase',
  ['root', 'sizeSmall', 'sizeMedium', 'sizeLarge', 'iconStart', 'iconEnd', 'fullWidth', 'disabled'],
  'Nova',
);

export default buttonBaseClasses;
