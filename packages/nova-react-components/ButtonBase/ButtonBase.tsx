'use client';
import React from 'react';
import { styled } from '@pigment-css/react';
import { Button as BaseButton } from '../internal/components/Button';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import { ButtonBaseProps } from './ButtonBase.types';
import { getButtonBaseUtilityClass } from './ButtonBase.classes';
import clsx from 'clsx';

const useUtilityClasses = (ownerState: ButtonBaseProps) => {
  const { size, disabled, fullWidth } = ownerState;

  const slots = {
    root: ['root', disabled && 'disabled', size && `size${capitalize(size)}`, fullWidth && 'fullWidth'],
    startIcon: ['iconStart'],
    endIcon: ['iconEnd'],
  };

  return composeClasses(slots, getButtonBaseUtilityClass, {});
};

const ButtonBaseRoot = styled(BaseButton, {
  shouldForwardProp: (prop) => !['fullWidth'].includes(prop),
})<ButtonBaseProps>(({ theme }) => ({
  position: 'relative',
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: '0.5rem',
  verticalAlign: 'middle',
  textAlign: 'center',
  height: '2.5rem',
  padding: '0.5rem 1rem',
  borderRadius: '0.75rem',
  borderWidth: 0,
  transition: 'background 400ms',
  backgroundColor: 'transparent',
  fontFamily: theme.typography.fontFamily,
  fontSize: '1rem',
  lineHeight: '1.5rem',
  cursor: 'pointer',
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  '&:focus-visible': {
    position: 'relative',
    zIndex: 1,
    outlineOffset: 2,
  },
  variants: [
    {
      props: { size: 'small' },
      style: {
        fontSize: '0.875rem',
        height: '2rem',
        padding: '0.25rem 0.5rem',
        borderRadius: '0.5rem',
        gap: '0.25rem',
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: '1.125rem',
        height: '3rem',
        padding: '0.75rem 1.5rem',
        gap: '0.75rem',
      },
    },
    {
      props: { disabled: true },
      style: {
        cursor: 'default',
        pointerEvents: 'none',
        userSelect: 'none',
      },
    },
    {
      props: { fullWidth: true },
      style: { width: '100%' },
    },
  ],
}));

const ButtonBaseIcon = styled('span')<{ position: 'start' | 'end'; size: ButtonBaseProps['size'] }>(() => ({
  display: 'inline-flex',
  alignItems: 'center',
  fontSize: 24,
  variants: [
    {
      props: { size: 'small' },
      style: {
        fontSize: 20,
      },
    },
    {
      props: { size: 'large' },
      style: {
        fontSize: 28,
      },
    },
  ],
}));

export const ButtonBase = React.forwardRef((props: ButtonBaseProps, ref: React.ForwardedRef<HTMLButtonElement>) => {
  const defaultProps: Partial<ButtonBaseProps> = {
    size: 'medium',
  };
  const mergedProps = { ...defaultProps, ...props };
  const classes = useUtilityClasses(mergedProps);

  const { startIcon, endIcon, children, size, className, ...otherProps } = mergedProps;

  const startIconWithFontSize = React.isValidElement(startIcon)
    ? React.cloneElement(startIcon as React.ReactElement, { fontSize: startIcon.props.fontSize ?? 'inherit' })
    : startIcon;

  const endIconWithFontSize = React.isValidElement(endIcon)
    ? React.cloneElement(endIcon as React.ReactElement, { fontSize: endIcon.props.fontSize ?? 'inherit' })
    : endIcon;

  return (
    <ButtonBaseRoot ref={ref} size={size} className={clsx(classes.root, className)} {...otherProps}>
      {startIcon && (
        <ButtonBaseIcon position="start" size={size} className={classes.startIcon}>
          {startIconWithFontSize}
        </ButtonBaseIcon>
      )}
      {children}
      {endIcon && (
        <ButtonBaseIcon position="end" size={size} className={classes.endIcon}>
          {endIconWithFontSize}
        </ButtonBaseIcon>
      )}
    </ButtonBaseRoot>
  );
});
