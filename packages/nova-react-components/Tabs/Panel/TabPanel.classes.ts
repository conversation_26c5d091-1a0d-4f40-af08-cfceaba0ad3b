import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface TabPanelClasses {
  /** Class name applied to the root element. */
  root: string;
}

export type TabPanelClassKey = keyof TabPanelClasses;

export function getTabPanelUtilityClass(slot: string): string {
  return generateUtilityClass('NovaTabPanel', slot, 'Nova');
}

const tabPanelClasses: TabPanelClasses = generateUtilityClasses('NovaTabPanel', ['root'], 'Nova');

export default tabPanelClasses;
