import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SxProps } from '../types/theme';
import { TransitionProps } from '../transitions/transition';
import { CollapseClasses } from './Collapse.classes';
import { SlotCommonProps } from '../types/slot';

export interface CollapseTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The content node to be collapsed.
     */
    children?: React.ReactNode;
    /**
     * Override or extend the styles applied to the component.
     */
    classes?: Partial<CollapseClasses>;
    /**
     * The width (horizontal) or height (vertical) of the container when collapsed.
     * @default '0px'
     */
    collapsedSize?: string | number;
    /**
     * The component used for the root node.
     * Either a string to use a HTML element or a component.
     */
    component?: React.ElementType<TransitionProps>;
    /**
     * The transition timing function.
     * You may specify a single easing or a object containing enter and exit values.
     */
    easing?: TransitionProps['easing'];
    /**
     * If `true`, the component will transition in.
     */
    in?: boolean;
    /**
     * The transition orientation.
     * @default 'vertical'
     */
    orientation?: 'horizontal' | 'vertical';
    /**
     * The duration for the transition, in milliseconds.
     * You may specify a single timeout for all transitions, or individually with an object.
     *
     * Set to 'auto' to automatically calculate transition time based on height.
     * @default duration.standard
     */
    timeout?: TransitionProps['timeout'] | 'auto';
    /**
     * The system prop that allows defining system overrides as well as additional CSS styles.
     */
    sx?: SxProps;
    /**
     * The component used for the transition.
     * @default Transition
     */
    TransitionComponent?: React.ComponentType<any>;
  } & Omit<TransitionProps, 'timeout'>;
  defaultComponent: D;
}

export type CollapseProps<
  D extends React.ElementType = CollapseTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<CollapseTypeMap<P, D>, D>;

export interface CollapseOwnerState extends CollapseProps {}
