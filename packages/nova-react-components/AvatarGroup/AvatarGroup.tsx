'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { getAvatarGroupUtilityClass } from './AvatarGroup.classes';
import { AvatarGroupProps, AvatarGroupOwnerState } from './AvatarGroup.types';

export const AvatarGroupContext = React.createContext<AvatarGroupOwnerState | undefined>(undefined);

if (process.env.NODE_ENV !== 'production') {
  AvatarGroupContext.displayName = 'AvatarGroupContext';
}

const useUtilityClasses = () => {
  const slots = {
    root: ['root'],
  };
  return composeClasses(slots, getAvatarGroupUtilityClass, {});
};

const AvatarGroupRoot = styled('div')<AvatarGroupProps>(({ theme }) => ({
  '--nova-avatar-group-gap': '4px',
  '--nova-avatar-marginInline-start': 'var(--nova-avatar-group-gap)',
  display: 'flex',
  variants: [
    {
      props: { size: 'small' },
      style: {
        '--nova-avatar-group-gap': '2px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        '--nova-avatar-group-gap': '4px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        '--nova-avatar-group-gap': '6px',
        display: 'flex',
      },
    },
  ],
}));

export const AvatarGroup = React.forwardRef(function AvatarGroup(
  props: AvatarGroupProps,
  ref: React.ForwardedRef<Element>,
) {
  const {
    className,
    color,
    component = 'div',
    size = 'medium',
    disabled,
    children,
    slots = {},
    slotProps = {},
    ...other
  } = props;

  const ownerState = {
    ...props,
    color,
    component,
    size,
    disabled,
  };

  const classes = useUtilityClasses();
  const externalForwardedProps = { ...other };
  const SlotRoot = slots.root ?? AvatarGroupRoot;

  const rootProps = useSlotProps({
    additionalProps: {
      ref,
      as: component,
    },
    externalForwardedProps,
    className: classes.root,
    elementType: AvatarGroupRoot,
    externalSlotProps: slotProps.root,
    ownerState,
  });

  return (
    <AvatarGroupContext.Provider value={ownerState}>
      <SlotRoot {...rootProps}>{children}</SlotRoot>
    </AvatarGroupContext.Provider>
  );
});
