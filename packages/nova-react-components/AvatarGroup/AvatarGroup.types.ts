import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { AvatarProps } from '../Avatar/Avatar.types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../types/slot';
import { ApplyColorInversion } from '../types/colorSystem';

export type AvatarGroupSlot = 'root';

export interface AvatarGroupSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type AvatarGroupSlotsAndSlotProps = CreateSlotsAndSlotProps<
  AvatarGroupSlots,
  {
    root: SlotProps<'div', object, AvatarGroupOwnerState>;
  }
>;

export interface AvatarGroupTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & Pick<AvatarProps, 'color' | 'size' | 'disabled'> & AvatarGroupSlotsAndSlotProps;
  defaultComponent: D;
}

export type AvatarGroupProps<
  D extends React.ElementType = AvatarGroupTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<AvatarGroupTypeMap<P, D>, D>;

export interface AvatarGroupOwnerState extends ApplyColorInversion<AvatarGroupProps> {}
