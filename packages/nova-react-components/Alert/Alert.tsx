'use client';

import * as React from 'react';
import { unstable_capitalize as capitalize, unstable_composeClasses as composeClasses } from '@mui/utils';
import { styled } from '@pigment-css/react';
import useSlotProps from '@mui/utils/useSlotProps';
import { getAlertUtilityClass } from './Alert.classes';
import { AlertOwnerState, AlertProps, SystemColor } from './Alert.types';
import { IconButton } from '../IconButton';
import { Typography } from '../Typography';
import CloseOutlined from '../internal/svg-icons/CloseOutlined';
import { Button } from '../Button';

const useUtilityClasses = (ownerState: AlertOwnerState) => {
  const { color, intensity, orientation } = ownerState;

  const slots = {
    root: ['root', intensity && `intensity${capitalize(intensity)}`, color && `color${capitalize(color)}`, orientation],
    startDecorator: ['startDecorator'],
    endDecorator: ['endDecorator'],
    content: ['content'],
    message: ['message'],
    action: ['action'],
    closeIcon: ['closeIcon'],
  };

  return composeClasses(slots, getAlertUtilityClass, {});
};

const AlertRoot = styled('div', {
  name: 'NovaAlert',
  slot: 'Root',
})<AlertProps>(({ theme }) => ({
  display: 'flex',
  minHeight: '3rem',
  justifyContent: 'space-between',
  boxSizing: 'border-box',
  backgroundColor: theme.vars.palette.primary,
  color: theme.vars.palette.onPrimary,
  padding: '0.75rem 0.75rem 0.75rem 1rem',
  borderRadius: '0.25rem',
  variants: [
    {
      props: { orientation: 'horizontal' },
      style: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: '0.5rem',
      },
    },
    {
      props: { orientation: 'vertical' },
      style: {
        flexDirection: 'column',
        gap: '1rem',
        paddingTop: '1rem',
      },
    },
    // Filled Styles
    ...(['success', 'info', 'warning'] as SystemColor[]).map((color: SystemColor) => ({
      props: { intensity: 'bold' as const, color: color },
      style: {
        backgroundColor: theme.vars.palette.system[color],
        color: theme.vars.palette.system[`on${color.charAt(0).toUpperCase()}${color.slice(1)}`],
      },
    })),
    {
      props: { intensity: 'bold', color: 'primary' },
      style: {
        backgroundColor: theme.vars.palette.inverseSurface,
        color: theme.vars.palette.inverseOnSurface,
      },
    },
    {
      props: { intensity: 'bold', color: 'error' },
      style: {
        backgroundColor: theme.vars.palette.error,
        color: theme.vars.palette.onError,
      },
    },
    // Subtle Styles
    ...(['success', 'info', 'warning'] as SystemColor[]).map((color: SystemColor) => ({
      props: { intensity: 'subtle' as const, color: color },
      style: {
        backgroundColor: theme.vars.palette.system[`${color}Container`],
        color: theme.vars.palette.system[`on${color.charAt(0).toUpperCase()}${color.slice(1)}Container`],
      },
    })),
    {
      props: { intensity: 'subtle', color: 'primary' },
      style: {
        backgroundColor: theme.vars.palette.secondaryContainer,
        color: theme.vars.palette.onSecondaryContainer,
      },
    },
    {
      props: { intensity: 'subtle', color: 'error' },
      style: {
        backgroundColor: theme.vars.palette.errorContainer,
        color: theme.vars.palette.onErrorContainer,
      },
    },
  ],
}));

const AlertContent = styled('div', {
  name: 'NovaAlert',
  slot: 'Content',
})<AlertProps>(() => ({
  display: 'flex',
  alignItems: 'stretch',
  gap: '0.5rem',
}));

const AlertMessage = styled('div', {
  name: 'NovaAlert',
  slot: 'Message',
})<AlertProps>(() => ({
  alignSelf: 'center',
}));

const AlertEndDecorator = styled('div', {
  name: 'NovaAlert',
  slot: 'EndDecorator',
})<AlertProps>(() => ({
  display: 'flex',
  alignItems: 'center',
  gap: '0.5rem',
  justifyContent: 'flex-end',
}));

const AlertStartDecorator = styled('span', {
  name: 'NovaAlert',
  slot: 'StartDecorator',
})<AlertProps>(() => ({
  display: 'inherit',
  flex: 'none',
}));

const AlertAction = styled('span', {
  name: 'NovaAlert',
  slot: 'Action',
})<AlertProps>(({ theme }) => ({
  display: 'inherit',
  flex: 'none',
}));

const ActionButton = styled(Button, {
  name: 'NovaAlert',
  slot: 'ActionButton',
})<AlertProps>(({ theme }) => ({
  variants: [
    {
      props: (ownerState) => ownerState.color !== 'primary',
      style: {
        color: 'inherit',
        backgroundColor: 'transparent',
        borderColor: 'inherit',
      },
    },
    {
      props: { intensity: 'bold', color: 'primary' },
      style: {
        color: theme.vars.palette.inversePrimary,
        borderColor: theme.vars.palette.inversePrimary,
        backgroundColor: 'transparent',
      },
    },
  ],
}));

export const Alert = React.forwardRef(function Alert(props: AlertProps, ref: React.Ref<HTMLElement>) {
  const {
    children,
    className,
    color = 'primary',
    intensity = 'bold',
    orientation = 'horizontal',
    startDecorator,
    action,
    slots = {},
    slotProps = {},
    component,
    onClose,
    ...other
  } = props;

  const ownerState = { ...props, color, intensity, orientation };
  const classes = useUtilityClasses(ownerState);

  const RootSlot = slots.root ?? AlertRoot;
  const StartDecoratorSlot = slots.startDecorator ?? AlertStartDecorator;
  const CloseButtonSlot = slots.closeButton ?? IconButton;
  const ActionSlot = slots.action ?? AlertAction;

  const rootSlotProps = useSlotProps({
    elementType: RootSlot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      role: 'alert',
      ref,
      as: component,
    },
    ownerState,
    className: [classes.root, className],
  });

  const startDecoratorSlotProps = useSlotProps({
    elementType: StartDecoratorSlot,
    externalSlotProps: slotProps.startDecorator,
    ownerState,
    className: classes.startDecorator,
  });

  const actionSlotProps = useSlotProps({
    elementType: ActionSlot,
    externalSlotProps: slotProps.action,
    ownerState,
    className: classes.action,
  });

  const actionButtonProps = useSlotProps({
    elementType: ActionButton,
    externalSlotProps: {},
    ownerState,
  });

  const closeButtonSlotProps = useSlotProps({
    elementType: CloseButtonSlot,
    externalSlotProps: slotProps.closeButton,
    ownerState: null,
    additionalProps: {
      variant: 'standard',
      size: 'small',
      style: {
        color: 'inherit',
      },
    },
    className: classes.closeIcon,
  });

  return (
    <RootSlot {...rootSlotProps}>
      <AlertContent className={classes.content}>
        {startDecorator && <StartDecoratorSlot {...startDecoratorSlotProps}>{startDecorator}</StartDecoratorSlot>}
        <AlertMessage className={classes.message}>
          {typeof children === 'string' ? <Typography>{children}</Typography> : children}
        </AlertMessage>
      </AlertContent>
      <AlertEndDecorator className={classes.endDecorator}>
        {action && (
          <ActionSlot {...actionSlotProps}>
            <ActionButton {...actionButtonProps} variant="outlined" size="small" onClick={action.onClick}>
              {action.label}
            </ActionButton>
          </ActionSlot>
        )}
        {onClose && (
          <CloseButtonSlot {...closeButtonSlotProps} onClick={onClose}>
            <CloseOutlined fontSize="inherit" color="inherit" />
          </CloseButtonSlot>
        )}
      </AlertEndDecorator>
    </RootSlot>
  );
});
