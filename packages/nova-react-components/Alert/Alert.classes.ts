import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface AlertClasses {
  /** Class name applied to the root element. */
  root: string;

  /** Class name applied to the root element if `color="primary"`. */
  colorPrimary: string;
  /** Class name applied to the root element if `color="error"`. */
  colorError: string;
  /** Class name applied to the root element if `color="info"`. */
  colorInfo: string;
  /** Class name applied to the root element if `color="warning"`. */
  colorWarning: string;
  /** Class name applied to the root element if `color="success"`. */
  colorSuccess: string;

  /** Class name applied to the root element if `intensity="bold"`. */
  intensityBold: string;
  /** Class name applied to the root element if `intensity="subtle"`. */
  intensitySubtle: string;

  /** Class name applied to the root element if `orientation="horizontal"`. */
  horizontal: string;
  /** Class name applied to the root element if `orientation="vertical"`. */
  vertical: string;

  /** Class name applied to the startDecorator element */
  startDecorator: string;
  /** Class name applied to the endDecorator element */
  endDecorator: string;
  /** Class name applied to the content element */
  content: string;
  /** Class name applied to the message element */
  message: string;
  /** Class name applied to the action element */
  action: string;
  /** Class name applied to the close icon element */
  closeIcon: string;
}

export type AlertClassKey = keyof AlertClasses;

export function getAlertUtilityClass(slot: string): string {
  return generateUtilityClass('NovaAlert', slot, 'Nova');
}

const alertClasses: AlertClasses = generateUtilityClasses(
  'NovaAlert',
  [
    'root',
    'colorPrimary',
    'colorError',
    'colorInfo',
    'colorWarning',
    'colorSuccess',
    'intensityBold',
    'intensitySubtle',
    'horizontal',
    'vertical',
    'startDecorator',
    'endDecorator',
    'content',
    'message',
    'action',
    'closeIcon',
  ],
  'Nova',
);

export default alertClasses;
