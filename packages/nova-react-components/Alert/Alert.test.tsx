import React from 'react';
import '@testing-library/jest-dom/vitest';
import { Alert } from './Alert';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';

afterEach(() => {
  cleanup();
});

describe('Alert', () => {
  it('should render with default slot classes', () => {
    render(<Alert>Message</Alert>);
    const alert = screen.getByRole('alert');
    expect(alert).toHaveClass('NovaAlert-root');
    expect(alert).toHaveClass('NovaAlert-colorPrimary');
    expect(alert).toHaveClass('NovaAlert-intensityBold');
    expect(alert).toHaveClass('NovaAlert-horizontal');
  });

  it('should render with different variant and color slot classes', () => {
    render(
      <Alert intensity="subtle" color="success">
        Message
      </Alert>,
    );
    const alert = screen.getByRole('alert');
    expect(alert).toHaveClass('NovaAlert-intensitySubtle');
    expect(alert).toHaveClass('NovaAlert-colorSuccess');
  });

  it('should render with orientation as vertical', () => {
    render(<Alert orientation="vertical">Message</Alert>);
    const alert = screen.getByRole('alert');
    expect(alert).toHaveClass('NovaAlert-vertical');
  });

  it('should render with startDecorator and endDecorator', () => {
    render(
      <Alert startDecorator={<span>Start</span>} action={{ label: 'End', onClick: () => {} }}>
        Message
      </Alert>,
    );
    const startDecorator = screen.getByText('Start');
    const endDecorator = screen.getByText('End');
    expect(startDecorator).toBeInTheDocument();
    expect(endDecorator).toBeInTheDocument();
  });

  it('should render with closeButton', () => {
    render(<Alert onClose={() => {}}>Message</Alert>);
    const closeButton = screen.getByRole('button');
    expect(closeButton).toBeInTheDocument();
  });

  it('should trigger onClose', () => {
    const onClose = vi.fn();
    render(<Alert onClose={onClose}>Message</Alert>);
    const closeButton = screen.getByRole('button');
    closeButton.click();
    expect(onClose).toHaveBeenCalled();
  });

  it('should render with slotProps.root, slotProps.startDecorator, slotProps.endDecorator, slotProps.closeButton', () => {
    render(
      <Alert
        slotProps={{
          root: { 'data-testid': 'root' },
          startDecorator: { 'data-testid': 'startDecorator' },
          action: { 'data-testid': 'action' },
          closeButton: { 'data-testid': 'closeButton' },
        }}
        startDecorator={<span>Start</span>}
        action={{
          label: 'End',
          onClick: () => {},
        }}
        onClose={() => {}}
      >
        Message
      </Alert>,
    );
    const alert = screen.getByTestId('root');
    expect(alert).toBeInTheDocument();
    const startDecorator = screen.getByTestId('startDecorator');
    expect(startDecorator).toBeInTheDocument();
    const action = screen.getByTestId('action');
    expect(action).toBeInTheDocument();
    const closeButton = screen.getByTestId('closeButton');
    expect(closeButton).toBeInTheDocument();
  });

  it('should render with className', () => {
    render(<Alert className="test">Message</Alert>);
    const alert = screen.getByRole('alert');
    expect(alert).toHaveClass('test');
  });
});
