import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface DataGridClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class applied to the root element if `density="standard"`. */
  densityStandard: string;
  /** Class applied to the root element if `density="compact"`. */
  densityCompact: string;
  /** Class applied to the root element if `density="comfortable"`. */
  densityComfortable: string;
  /** Class name applied to the container element */
  container: string;
  /** Class name applied to the scroll area element. */
  scrollArea: string;
  /** Class name applied to the header element. */
  header: string;
  /** Class name applied to the header cell elements. */
  headerCell: string;
  /** Class name applied to the header cell text element. */
  headerCellText: string;
  /** Class name applied to the header sort icon element. */
  sortIcon: string;
  /** Class name applied to the header sort icon element if the column is sorted in ascending order. */
  sortAsc: string;
  /** Class name applied to the header sort icon element if the column is sorted in descending order. */
  sortDesc: string;
  /** Class name applied to the body element. */
  body: string;
  /** Class name applied to the body row element. */
  row: string;
  /** Class name applied to the body row element once row is selected. */
  rowSelected: string;
  /** Class name applied to the body row element once row is editing. */
  rowEditing: string;
  /** Class name applied to the body row cell elements. */
  rowCell: string;
  /** Class name applied to the cell elements. */
  cell: string;
  /** Class name applied to the body cell element once cell is selected. */
  cellSelected: string;
  /** Class name applied to the body cell element once cell is editing. */
  cellEditing: string;
  /** Class name applied to the body cell element once cell is fixed. */
  cellFixed: string;
  /** Class name applied to the body cell element once cell is left fixed. */
  cellFixedLeft: string;
  /** Class name applied to the body cell element once cell is right fixed. */
  cellFixedRight: string;
  /** Class name applied to the empty cell elements. */
  emptyCell: string;
  /** Class name applied to the checkbox cell elements. */
  checkboxCell: string;
  /** Class name applied to the radio cell elements. */
  radioCell: string;
  /** Class name applied to the expand cell elements. */
  expandCell: string;
  /** Class name applied to the row expanded panel element. */
  rowExpandedPanel: string;
  /** Class name applied to the scrollbar element. */
  scrollbar: string;
  /** Class name applied to the horizontal scrollbar element. */
  scrollHorizontal: string;
  /** Class name applied to the vertical scrollbar element. */
  scrollVertical: string;
  /** Class name applied to the pagination container element. */
  pagination: string;
  /** Class name applied to the column resize container element. */
  columnResizeContainer: string;
  /** Class name applied to the column resize slider element. */
  columnResizeSlider: string;
}

export type DataGridClassKey = keyof DataGridClasses;

export function getDataGridUtilityClass(slot: string): string {
  return generateUtilityClass('NovaDataGrid', slot, 'Nova');
}

const dataGridClasses: DataGridClasses = generateUtilityClasses(
  'NovaDataGrid',
  [
    'root',
    'densityStandard',
    'densityCompact',
    'densityComfortable',
    'container',
    'scrollArea',
    'header',
    'headerCell',
    'headerCellText',
    'sortIcon',
    'sortAsc',
    'sortDesc',
    'body',
    'row',
    'rowSelected',
    'rowEditing',
    'rowCell',
    'cell',
    'cellSelected',
    'cellEditing',
    'cellFixed',
    'cellFixedLeft',
    'cellFixedRight',
    'emptyCell',
    'checkboxCell',
    'radioCell',
    'expandCell',
    'rowExpandedPanel',
    'scrollbar',
    'scrollHorizontal',
    'scrollVertical',
    'pagination',
    'columnResizeContainer',
    'columnResizeSlider',
  ],
  'Nova',
);

export default dataGridClasses;
