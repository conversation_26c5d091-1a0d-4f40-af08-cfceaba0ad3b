import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../../types/slot';

export interface FloatingActionBarSlotPropsOverrides {}

export interface FloatingActionBarSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type FloatingActionBarSlotsAndSlotProps = CreateSlotsAndSlotProps<
  FloatingActionBarSlots,
  {
    root: SlotProps<'div', FloatingActionBarSlotPropsOverrides, FloatingActionBarOwnerState>;
  }
>;

export interface FloatingActionBarTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    FloatingActionBarSlotsAndSlotProps & {
      /**
       * The component orientation.
       * @default 'horizontal'
       */
      orientation?: 'horizontal' | 'vertical';
      /**
       * How large the FloatingActionBar contents should be.
       * @default 'medium'
       */
      size?: 'small' | 'medium' | 'large';
    };
  defaultComponent: D;
}

export type FloatingActionBarProps<
  D extends React.ElementType = FloatingActionBarTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<FloatingActionBarTypeMap<P, D>, D>;

export interface FloatingActionBarOwnerState extends Partial<FloatingActionBarProps> {}
