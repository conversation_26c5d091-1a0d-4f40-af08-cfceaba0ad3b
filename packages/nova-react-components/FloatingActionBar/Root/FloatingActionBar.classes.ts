import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface FloatingActionBarClasses {
  /** Class name applied to the root element. */
  root: string;

  /** Class name applied to the root element if `orientation="horizontal"`. */
  horizontal: string;
  /** Class name applied to the root element if `orientation="vertical"`. */
  vertical: string;

  /** Class name applied to the root element if `size="small"`. */
  sizeSmall: string;
  /** Class name applied to the root element if `size="medium"`. */
  sizeMedium: string;
  /** Class name applied to the root element if `size="large"`. */
  sizeLarge: string;
}

export type FloatingActionBarClassKey = keyof FloatingActionBarClasses;

export function getFloatingActionBarUtilityClass(slot: string): string {
  return generateUtilityClass('NovaFloatingActionBar', slot, 'Nova');
}

const floatingActionBarRootClasses: FloatingActionBarClasses = generateUtilityClasses(
  'NovaFloatingActionBar',
  ['root', 'horizontal', 'vertical', 'sizeSmall', 'sizeMedium', 'sizeLarge'],
  'Nova',
);

export default floatingActionBarRootClasses;
