import '@testing-library/jest-dom/vitest';
import * as React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { FloatingActionBar } from './FloatingActionBar';
import { FloatingAction } from '../Item';

afterEach(() => {
  cleanup();
});

describe('<FloatingActionBar />', () => {
  it('provide context to FloatingAction', () => {
    render(
      <FloatingActionBar orientation="horizontal" size="small">
        <FloatingAction data-testid="btn" />
      </FloatingActionBar>,
    );
    expect(screen.getByTestId('btn')).toHaveClass('NovaButtonBase-sizeSmall');
  });

  it('should render with default slot classes', () => {
    render(<FloatingActionBar>Click Me</FloatingActionBar>);
    const actionBar = screen.getByRole('toolbar');
    expect(actionBar).toHaveClass('NovaFloatingActionBar-root');
    expect(actionBar).toHaveClass('NovaFloatingActionBar-horizontal');
    expect(actionBar).toHaveClass('NovaFloatingActionBar-sizeMedium');
  });

  it('should render with vertical orientation slot class', () => {
    render(<FloatingActionBar orientation="vertical">Click Me</FloatingActionBar>);
    const actionBar = screen.getByRole('toolbar');
    expect(actionBar).toHaveClass('NovaFloatingActionBar-vertical');
  });

  it('should render with small size slot class', () => {
    render(<FloatingActionBar size="small">Click Me</FloatingActionBar>);
    const actionBar = screen.getByRole('toolbar');
    expect(actionBar).toHaveClass('NovaFloatingActionBar-sizeSmall');
  });

  it('should render with large size slot class', () => {
    render(<FloatingActionBar size="large">Click Me</FloatingActionBar>);
    const actionBar = screen.getByRole('toolbar');
    expect(actionBar).toHaveClass('NovaFloatingActionBar-sizeLarge');
  });
});
