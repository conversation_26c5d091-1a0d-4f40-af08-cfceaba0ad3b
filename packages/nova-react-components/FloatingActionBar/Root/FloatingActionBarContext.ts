import * as React from 'react';
import type { FloatingActionBarProps } from './FloatingActionBar.types';

interface FloatingActionBarContextValue {
  size?: FloatingActionBarProps['size'];
  orientation?: FloatingActionBarProps['orientation'];
}

const FloatingActionBarContext = React.createContext<FloatingActionBarContextValue>({});

if (process.env.NODE_ENV !== 'production') {
  FloatingActionBarContext.displayName = 'FloatingActionBarContext';
}

export default FloatingActionBarContext;
