'use client';

import * as React from 'react';
import { unstable_composeClasses as composeClasses, unstable_capitalize as capitalize } from '@mui/utils';
import useSlotProps from '@mui/utils/useSlotProps';
import { styled } from '@pigment-css/react';
import { getFloatingActionBarUtilityClass } from './FloatingActionBar.classes';
import { FloatingActionBarProps, FloatingActionBarOwnerState } from './FloatingActionBar.types';
import FloatingActionBarContext from './FloatingActionBarContext';

const useUtilityClasses = (ownerState: FloatingActionBarOwnerState) => {
  const { orientation, size } = ownerState;
  const slots = {
    root: ['root', orientation, size && `size${capitalize(size)}`],
  };

  return composeClasses(slots, getFloatingActionBarUtilityClass, {});
};

const FloatingActionBarContainer = styled('div')<FloatingActionBarProps>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  maxWidth: 'fit-content',
  alignItems: 'center',
  gap: '4px',
  borderRadius: '12px',
  padding: '4px',
  backgroundColor: theme.vars.palette.surfaceContainer,
  boxShadow: theme.shadows[1],
  border: `1px solid ${theme.vars.palette.outlineVariant}`,
  variants: [
    {
      props: { orientation: 'vertical' },
      style: {
        flexDirection: 'column',
      },
    },
    {
      props: { size: 'large' },
      style: {
        paddingBlock: '8px',
        gap: '8px',
      },
    },
  ],
}));

export const FloatingActionBar = React.forwardRef(function FloatingActionBar(
  props: FloatingActionBarProps,
  ref: React.Ref<HTMLDivElement>,
) {
  const defaultProps: Partial<FloatingActionBarProps> = {
    orientation: 'horizontal',
    size: 'medium',
  };
  const mergedProps = { ...defaultProps, ...props };

  const {
    className,
    component = 'div',
    children,
    orientation,
    size,
    slots = {},
    slotProps = {},
    ...rest
  } = mergedProps;

  const classes = useUtilityClasses(mergedProps);
  const SlotRoot = slots.root ?? FloatingActionBarContainer;

  const rootProps = useSlotProps({
    additionalProps: {
      ref,
      role: 'toolbar',
      ariaLabel: 'Floating Action Bar',
      as: component,
    },
    className: [classes.root, className],
    elementType: FloatingActionBarContainer,
    externalForwardedProps: rest,
    externalSlotProps: slotProps.root,
    ownerState: mergedProps,
  });

  return (
    <FloatingActionBarContext.Provider value={{ size, orientation }}>
      <SlotRoot {...rootProps}>{children}</SlotRoot>
    </FloatingActionBarContext.Provider>
  );
});
