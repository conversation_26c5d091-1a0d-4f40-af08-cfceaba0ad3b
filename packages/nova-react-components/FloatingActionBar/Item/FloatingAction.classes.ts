import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface FloatingActionClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the dropdown element. */
  dropdown: string;
}

export type FloatingActionClassKey = keyof FloatingActionClasses;

export function getFloatingActionUtilityClass(slot: string): string {
  return generateUtilityClass('NovaFloatingAction', slot, 'Nova');
}

export const FloatingActionClasses: FloatingActionClasses = generateUtilityClasses(
  'NovaFloatingAction',
  ['root', 'dropdown'],
  'Nova',
);

export default FloatingActionClasses;
