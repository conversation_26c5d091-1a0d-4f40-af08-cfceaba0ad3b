import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface ListDividerClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `variant="gutter"`. */
  gutter: string;
  /** Class name applied to the root element if `variant="startDecorator"`. */
  startDecorator: string;
  /** Class name applied to the root element if `variant="startContent"`. */
  startContent: string;
  /** Class name applied to the root element if `orientation="horizontal"`. */
  horizontal: string;
  /** Class name applied to the root element if `orientation="vertical"`. */
  vertical: string;
}

export type ListDividerClassKey = keyof ListDividerClasses;

export function getListDividerUtilityClass(slot: string): string {
  return generateUtilityClass('NovaListDivider', slot, 'Nova');
}

const listDividerClasses: ListDividerClasses = generateUtilityClasses(
  'NovaListDivider',
  ['root', 'gutter', 'startDecorator', 'startContent', 'horizontal', 'vertical'],
  'Nova',
);

export default listDividerClasses;
