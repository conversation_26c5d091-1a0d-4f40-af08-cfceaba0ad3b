'use client';
import * as React from 'react';
import { unstable_isMuiElement as isMuiElement, unstable_composeClasses as composeClasses } from '@mui/utils';
import clsx from 'clsx';
import { styled } from '@pigment-css/react';
import { getBreadcrumbsUtilityClass } from './Breadcrumbs.classes';
import { BreadcrumbsOwnerState, BreadcrumbsProps } from './Breadcrumbs.types';
import useSlotProps from '@mui/utils/useSlotProps';

const useUtilityClasses = (ownerState: BreadcrumbsOwnerState) => {
  const slots = {
    root: ['root'],
    li: ['li'],
    ol: ['ol'],
    separator: ['separator'],
  };

  return composeClasses(slots, getBreadcrumbsUtilityClass, {});
};

const BreadcrumbsRoot = styled('nav')<BreadcrumbsOwnerState>(({ theme }) => ({
  ...theme.typography.bodyMedium,
  gap: '0.375rem',
  padding: '0.75rem',
}));

const BreadcrumbsOl = styled('ol')<BreadcrumbsOwnerState>({
  display: 'flex',
  flexWrap: 'wrap',
  alignItems: 'center',
  gap: 'inherit',
  // reset user-agent style
  padding: 0,
  margin: 0,
  listStyle: 'none',
});

const BreadcrumbsLi = styled('li')<BreadcrumbsOwnerState>({
  display: 'flex',
  alignItems: 'center',
  '& a': {
    display: 'inline-flex',
    alignItems: 'center',
  },
  '& a:focus-visible': {
    borderRadius: '4px',
  },
});

const BreadcrumbsSeparator = styled('li')<BreadcrumbsOwnerState>(({ theme }) => ({
  display: 'flex',
  userSelect: 'none',
  color: theme.vars.palette.onSurfaceVariant,
}));

// eslint-disable-next-line react/display-name
export const Breadcrumbs = React.forwardRef(function Breadcrumbs(props: BreadcrumbsProps, ref: React.Ref<HTMLElement>) {
  const { children, className, separator = '/', component, slots = {}, slotProps = {}, ...other } = props;

  const ownerState = {
    ...props,
    separator,
  };

  const classes = useUtilityClasses(ownerState);

  const SlotRoot = slots.root ?? BreadcrumbsRoot;
  const rootProps = useSlotProps({
    elementType: BreadcrumbsRoot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    className: clsx(classes.root, className),
  });

  const SlotOl = slots.ol ?? BreadcrumbsOl;
  const olProps = useSlotProps({
    elementType: BreadcrumbsOl,
    externalSlotProps: slotProps.ol,
    ownerState,
    className: classes.ol,
  });

  const SlotLi = slots.li ?? BreadcrumbsLi;
  const liProps = useSlotProps({
    elementType: BreadcrumbsLi,
    externalSlotProps: slotProps.li,
    ownerState,
    className: classes.li,
  });

  const SlotSeparator = slots.separator ?? BreadcrumbsSeparator;
  const separatorProps = useSlotProps({
    elementType: BreadcrumbsSeparator,
    externalSlotProps: slotProps.separator,
    additionalProps: {
      'aria-hidden': true,
    },
    ownerState,
    className: classes.separator,
  });

  const allItems = (
    React.Children.toArray(children).filter((child) => {
      return React.isValidElement(child);
    }) as Array<React.ReactElement<any>>
  ).map((child, index) => (
    <SlotLi key={`child-${index}`} {...liProps}>
      {isMuiElement(child, ['Typography'])
        ? React.cloneElement(child, { component: child.props.component ?? 'span' })
        : child}
    </SlotLi>
  ));

  return (
    <SlotRoot {...rootProps}>
      <SlotOl {...olProps}>
        {allItems.reduce((acc: React.ReactNode[], current: React.ReactNode, index: number) => {
          if (index < allItems.length - 1) {
            acc = acc.concat(
              current,
              <SlotSeparator key={`separator-${index}`} {...separatorProps}>
                {separator}
              </SlotSeparator>,
            );
          } else {
            acc.push(current);
          }
          return acc;
        }, [])}
      </SlotOl>
    </SlotRoot>
  );
});
