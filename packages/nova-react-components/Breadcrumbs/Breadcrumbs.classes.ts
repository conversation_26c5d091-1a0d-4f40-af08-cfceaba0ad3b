import generateUtilityClasses from '@mui/utils/generateUtilityClasses';
import generateUtilityClass from '@mui/utils/generateUtilityClass';

export interface BreadcrumbsClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the ol element. */
  ol: string;
  /** Class name applied to the li element. */
  li: string;
  /** Class name applied to the separator element. */
  separator: string;
}

export type BreadcrumbsClassKey = keyof BreadcrumbsClasses;

export function getBreadcrumbsUtilityClass(slot: string): string {
  return generateUtilityClass('NovaBreadcrumbs', slot, 'Nova');
}

const breadcrumbsClasses: BreadcrumbsClasses = generateUtilityClasses(
  'NovaBreadcrumbs',
  ['root', 'ol', 'li', 'separator'],
  'Nova',
);

export default breadcrumbsClasses;
