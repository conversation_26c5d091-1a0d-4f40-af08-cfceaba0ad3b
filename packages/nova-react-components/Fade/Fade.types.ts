import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { TransitionProps } from '../transitions/transition';
import { SlotCommonProps } from '../types/slot';

export interface FadeTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * Perform the enter transition when it first mounts if `in` is also `true`.
     * Set this to `false` to disable this behavior.
     * @default true
     */
    appear?: boolean;
    /**
     * A single child content element.
     */
    children: React.ReactElement<unknown, any>;
    /**
     * The transition timing function.
     * You may specify a single easing or a object containing enter and exit values.
     */
    easing?: TransitionProps['easing'];
    /**
     * If `true`, the component will transition in.
     */
    in?: boolean;
    ref?: React.Ref<unknown>;
    /**
     * The duration for the transition, in milliseconds.
     * You may specify a single timeout for all transitions, or individually with an object.
     * @default {
     *   enter: duration.enteringScreen,
     *   exit: duration.leavingScreen,
     * }
     */
    timeout?: TransitionProps['timeout'];
    /**
     * The component used for the transition.
     * @default Transition
     */
    TransitionComponent?: React.ComponentType<any>;
  } & Omit<TransitionProps, 'children'>;
  defaultComponent: D;
}

export type FadeProps<
  D extends React.ElementType = FadeTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<FadeTypeMap<P, D>, D>;

export interface FadeOwnerState extends FadeProps {}
