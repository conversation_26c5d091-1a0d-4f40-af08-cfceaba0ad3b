import React from 'react';
import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { CircularProgress } from './CircularProgress';
import circularProgressClasses from './CircularProgress.classes';

vi.mock('@pigment-css/react', async () => {
  const actual = await import('@pigment-css/react');
  return {
    ...actual,
    keyframes: vi.fn(() => 'mocked-keyframe'),
  };
});

describe('Nova <CircularProgress />', () => {
  it('should render with the primary color by default', () => {
    render(<CircularProgress data-testid="NovaCircularProgress-root" />);
    expect(screen.getByTestId('NovaCircularProgress-root')).toHaveClass(circularProgressClasses.colorPrimary);
  });

  it('should render with the primary color', () => {
    render(<CircularProgress data-testid="NovaCircularProgress-root" color="primary" />);
    expect(screen.getByTestId('NovaCircularProgress-root')).toHaveClass(circularProgressClasses.colorPrimary);
  });

  it('should contain an SVG with the svg class,  a track and a progress', () => {
    const { container } = render(<CircularProgress data-testid="NovaCircularProgress-root" />);
    expect(screen.getByTestId('NovaCircularProgress-root')).toHaveAttribute('role', 'progressbar');
    expect(screen.getByTestId('NovaCircularProgress-root')).toHaveClass(circularProgressClasses.indeterminate);
    const svg = container.querySelector('svg');
    const track = container.querySelectorAll('circle')[0];
    const progress = container.querySelectorAll('circle')[1];
    expect(svg).toBeDefined();
    expect(svg).toHaveClass(circularProgressClasses.svg);
    expect(track).toBeDefined();
    expect(track).toHaveClass(circularProgressClasses.track);
    expect(progress).toBeDefined();
    expect(progress).toHaveClass(circularProgressClasses.progress);
  });

  it('should render indeterminate variant by default', () => {
    const { container } = render(<CircularProgress data-testid="NovaCircularProgress-root" />);
    expect(screen.getByTestId('NovaCircularProgress-root')).toHaveClass(circularProgressClasses.root);
    expect(screen.getByTestId('NovaCircularProgress-root')).toHaveClass(circularProgressClasses.indeterminate);

    const progress = container.querySelectorAll('circle')[1];
    expect(progress).toHaveClass(circularProgressClasses.progress);
  });

  it('should render with a different size', () => {
    const { container } = render(<CircularProgress data-testid="NovaCircularProgress-root" size={60} />);
    expect(screen.getByTestId('NovaCircularProgress-root')).toHaveStyle({ '--nova-circularProgress-size': '60px' });

    const svg = container.querySelector('svg');
    expect(svg).toBeDefined();

    const progress = container.querySelectorAll('circle')[1];
    expect(progress).toBeDefined();
  });

  describe('prop: variant="determinate"', () => {
    it('should render with determinate classes', () => {
      const { container } = render(<CircularProgress data-testid="NovaCircularProgress-root" variant="determinate" />);
      expect(screen.getByTestId('NovaCircularProgress-root')).toHaveClass(circularProgressClasses.root);
      expect(screen.getByTestId('NovaCircularProgress-root')).not.toHaveClass(circularProgressClasses.indeterminate);

      const svg = container.querySelector('svg');
      expect(svg).toBeDefined();

      const progress = container.querySelectorAll('circle')[1];
      expect(progress).toHaveClass(circularProgressClasses.progress);
    });
  });

  describe('slots and slotProps', () => {
    it('should apply slotProps to the root', () => {
      render(
        <CircularProgress
          slotProps={{
            root: { 'data-testid': 'custom-root' },
          }}
        />,
      );
      expect(screen.getByTestId('custom-root')).toBeDefined();
    });

    it('should apply className from slotProps', () => {
      render(
        <CircularProgress
          slotProps={{
            root: { className: 'custom-root-class' },
          }}
        />,
      );
      expect(screen.getByRole('progressbar')).toHaveClass('custom-root-class');
    });
  });
});
