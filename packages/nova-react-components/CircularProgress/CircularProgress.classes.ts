import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

export interface CircularProgressClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the root element if `variant="determinate"`. */
  determinate: string;
  /** Class name applied to the root element if `variant="indeterminate"`. */
  indeterminate: string;
  /** Class name applied to the root element if `color="primary"`. */
  colorPrimary: string;
  /** Class name applied to the root element if `color="error"`. */
  colorError: string;
  /** Class name applied to the root element if `color="info"`. */
  colorInfo: string;
  /** Class name applied to the root element if `color="warning"`. */
  colorWarning: string;
  /** Class name applied to the root element if `color="success"`. */
  colorSuccess: string;
  /** Class name applied to the svg element. */
  svg: string;
  /** Class name applied to the `track` element. */
  track: string;
  /** Class name applied to the `progress` element. */
  progress: string;
}

export function getCircularProgressUtilityClass(slot: string): string {
  return generateUtilityClass('NovaCircularProgress', slot, 'Nova');
}
const circularProgressClasses: CircularProgressClasses = generateUtilityClasses(
  'NovaCircularProgress',
  [
    'root',
    'determinate',
    'indeterminate',
    'colorPrimary',
    'colorError',
    'colorInfo',
    'colorWarning',
    'colorSuccess',
    'svg',
    'track',
    'progress',
  ],
  'Nova',
);

export default circularProgressClasses;
