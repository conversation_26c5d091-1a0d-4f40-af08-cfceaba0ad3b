import * as React from 'react';
import { Breakpoint, styled } from '@pigment-css/react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import capitalize from '@mui/utils/capitalize';
import { ContainerOwnerState, ContainerProps } from './Container.types';
import { getContainerUtilityClass } from './Container.classes';
import clsx from 'clsx';

const useUtilityClasses = (ownerState: ContainerOwnerState) => {
  const { fixed, disableGutters, maxWidth } = ownerState;
  const slots = {
    root: [
      'root',
      maxWidth && `maxWidth${capitalize(String(maxWidth))}`,
      fixed && 'fixed',
      disableGutters && 'disableGutters',
    ],
  };

  return composeClasses(slots, getContainerUtilityClass, {});
};

const ContainerRoot = styled('div')<ContainerOwnerState>(({ theme }) => ({
  width: '100%',
  marginLeft: 'auto',
  boxSizing: 'border-box',
  marginRight: 'auto',
  variants: [
    {
      props: (props) => !props.disableGutters,
      style: {
        paddingLeft: `calc(${theme.vars.spacing} * 2)`,
        paddingRight: `calc(${theme.vars.spacing} * 2)`,
        [theme.breakpoints.up('sm')]: {
          paddingLeft: `calc(${theme.vars.spacing} * 3)`,
          paddingRight: `calc(${theme.vars.spacing} * 3)`,
        },
      },
    },
    {
      props: {
        fixed: true,
      },
      style: Object.keys(theme.breakpoints.values).reduce((acc, breakpointValueKey) => {
        const breakpoint = breakpointValueKey;
        const value = theme.breakpoints.values[breakpoint];

        if (value !== 0) {
          acc[theme.breakpoints.up(breakpoint as Breakpoint)] = {
            maxWidth: `${value}${theme.breakpoints.unit}`,
          };
        }
        return acc;
      }, {}),
    },
    {
      props: {
        maxWidth: 'xs',
      },
      style: {
        [theme.breakpoints.up('xs')]: {
          maxWidth: Math.max(theme.breakpoints.values.xs, 444),
        },
      },
    },
    ...theme.breakpoints.keys
      .filter((breakpoint) => breakpoint && breakpoint !== 'xs')
      .map((breakpoint) => {
        return {
          props: {
            maxWidth: breakpoint,
          },
          style: {
            [theme.breakpoints.up(breakpoint)]: {
              maxWidth: `${theme.breakpoints.values[breakpoint]}${theme.breakpoints.unit}`,
            },
          },
        };
      })
      .filter(Boolean),
  ],
}));

// eslint-disable-next-line react/display-name
export const Container = React.forwardRef((props: ContainerProps, ref: React.ForwardedRef<Element>) => {
  const { className, component = 'div', disableGutters = false, fixed = false, maxWidth = 'lg', ...rest } = props;
  const ownerState = {
    ...rest,
    component,
    disableGutters,
    fixed,
    maxWidth,
  };
  const classes = useUtilityClasses(ownerState);
  return (
    <ContainerRoot
      as={component}
      ownerState={ownerState}
      className={clsx(classes.root, className)}
      disableGutters={disableGutters}
      ref={ref}
      {...(rest as any)}
    />
  );
});
