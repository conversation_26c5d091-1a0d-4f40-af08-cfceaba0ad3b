import React from 'react';
import { <PERSON><PERSON>, Typography, FloatingActionBar, FloatingActionBarProps } from '@hxnova/react-components';
import { Icon } from '@hxnova/icons';

interface ExampleProps {
  orientation: FloatingActionBarProps['orientation'];
  size: FloatingActionBarProps['size'];
}
function Example({ orientation, size }: ExampleProps) {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(!anchorEl ? event.currentTarget : null);
  };

  return (
    <>
      <FloatingActionBar.Root orientation={orientation} size={size}>
        <FloatingActionBar.Item>
          <Icon family="material" name="bookmark_border" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item selected={open} onClick={handleClick} showDropdownIcon showDivider>
          <Icon family="material" name="palette" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item>
          <Icon family="material" name="border_color" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item>
          <Icon family="material" name="person_add" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item disabled>
          <Icon family="material" name="delete" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item>
          <Icon family="material" name="apps" />
        </FloatingActionBar.Item>
      </FloatingActionBar.Root>
      <Popper
        open={open}
        anchorEl={anchorEl}
        placement={orientation === 'horizontal' ? 'top-start' : 'right-start'}
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: { offset: [-4, 10] },
            },
          ],
        }}
      >
        <FloatingActionBar.Root orientation={orientation} size={size}>
          <FloatingActionBar.Item selected>
            <Icon family="material" name="bookmark_border" />
          </FloatingActionBar.Item>
          <FloatingActionBar.Item>
            <Icon family="material" name="border_color" />
          </FloatingActionBar.Item>
          <FloatingActionBar.Item disabled>
            <Icon family="material" name="delete" />
          </FloatingActionBar.Item>
        </FloatingActionBar.Root>
      </Popper>
    </>
  );
}

export default function FloatingActionBarKitchen() {
  return (
    <div>
      {(['horizontal', 'vertical'] as Array<FloatingActionBarProps['orientation']>).map((orientation, index) => (
        <div key={index} sx={{ marginBlock: '0px' }}>
          <div sx={{ display: 'flex', flexDirection: 'row', gap: '60px' }}>
            {(['small', 'medium', 'large'] as Array<FloatingActionBarProps['size']>).map((size, index) => (
              <div key={index} sx={{ display: 'flex', flexDirection: 'column', gap: '60px' }}>
                <Typography variant={'titleSmall'}>{size}</Typography>
                <Example orientation={orientation} size={size} />
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
