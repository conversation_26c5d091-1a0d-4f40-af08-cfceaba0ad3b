import * as React from 'react';
import { Pagination } from '@hxnova/react-components/Pagination';
import { TablePagination } from '@hxnova/react-components/TablePagination';
import { PaginationItem } from '@hxnova/react-components/PaginationItem';
import Icon from '@hxnova/icons/Icon';
const ArrowBackIcon = () => <Icon family="material" name="arrow_back" size={24} />;
const ArrowForwardIcon = () => <Icon family="material" name="arrow_forward" size={24} />;

const PaginationKitchen = () => {
  const [page, setPage] = React.useState(1);
  const [tablePage, setTablePage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(5);

  const handleChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setTablePage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.SyntheticEvent | null, newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setTablePage(0);
  };

  return (
    <div
      sx={{
        padding: '2rem',
      }}
    >
      <div
        sx={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '2rem',
        }}
      >
        {/* Left Column - Pagination Examples */}
        <div
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '2rem',
          }}
        >
          <h2>Pagination</h2>

          {/* Basic pagination */}
          <div
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem',
            }}
          >
            <h3 sx={{ margin: 0 }}>Basic pagination</h3>
            <Pagination count={10} />
          </div>

          {/* Controlled pagination */}
          <div
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem',
            }}
          >
            <h3 sx={{ margin: 0 }}>Controlled pagination</h3>
            <Pagination count={10} page={page} onChange={handleChange} />
          </div>

          {/* Different sizes */}
          <div
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem',
            }}
          >
            <h3 sx={{ margin: 0 }}>Sizes</h3>
            <Pagination count={10} size="small" />
            <Pagination count={10} />
            <Pagination count={10} size="large" />
          </div>

          {/* With buttons */}
          <div
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem',
            }}
          >
            <h3 sx={{ margin: 0 }}>With navigation buttons</h3>
            <Pagination count={10} showFirstButton showLastButton />
            <Pagination count={10} hidePrevButton hideNextButton />
          </div>

          {/* Disabled */}
          <div
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem',
            }}
          >
            <h3 sx={{ margin: 0 }}>Disabled</h3>
            <Pagination count={10} disabled />
          </div>

          {/* Custom icons */}
          <div
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem',
            }}
          >
            <h3 sx={{ margin: 0 }}>Custom icons</h3>
            <Pagination
              count={10}
              renderItem={(item) => (
                <PaginationItem slots={{ previous: ArrowBackIcon, next: ArrowForwardIcon }} {...item} />
              )}
            />
          </div>

          {/* Pagination ranges */}
          <div
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem',
            }}
          >
            <h3 sx={{ margin: 0 }}>Pagination ranges</h3>
            <Pagination count={11} defaultPage={6} siblingCount={0} />
            <Pagination count={11} defaultPage={6} /> {/* Default ranges */}
            <Pagination count={11} defaultPage={6} siblingCount={0} boundaryCount={2} />
            <Pagination count={11} defaultPage={6} boundaryCount={2} />
          </div>
        </div>

        {/* Right Column - Table Pagination Examples */}
        <div
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '2rem',
          }}
        >
          <h2>Table Pagination</h2>

          {/* Basic Table Pagination */}
          <div sx={{ border: '1px solid #e0e0e0', padding: '1rem' }}>
            <h3 sx={{ margin: '0 0 1rem 0' }}>Basic</h3>
            <TablePagination
              component="div"
              count={100}
              page={tablePage}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </div>

          {/* Table Pagination with Custom Rows Per Page Options */}
          <div sx={{ border: '1px solid #e0e0e0', padding: '1rem' }}>
            <h3 sx={{ margin: '0 0 1rem 0' }}>Custom Rows Per Page Options</h3>
            <TablePagination
              component="div"
              count={100}
              page={tablePage}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={[5, 10, 25, { label: 'All', value: -1 }]}
            />
          </div>

          {/* Table Pagination with Navigation Buttons */}
          <div sx={{ border: '1px solid #e0e0e0', padding: '1rem' }}>
            <h3 sx={{ margin: '0 0 1rem 0' }}>With First/Last Buttons</h3>
            <TablePagination
              component="div"
              count={100}
              page={tablePage}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              showFirstButton
              showLastButton
            />
          </div>

          {/* Disabled Table Pagination */}
          <div sx={{ border: '1px solid #e0e0e0', padding: '1rem' }}>
            <h3 sx={{ margin: '0 0 1rem 0' }}>Disabled</h3>
            <TablePagination
              component="div"
              count={100}
              page={0}
              onPageChange={handleChangePage}
              rowsPerPage={5}
              disabled
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaginationKitchen;
