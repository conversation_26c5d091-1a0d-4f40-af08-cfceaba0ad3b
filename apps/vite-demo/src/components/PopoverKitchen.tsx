import { Popover } from '@hxnova/react-components/Popover';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function PopoverKitchen() {
  return (
    <div sx={{ padding: '24px' }}>
      <div sx={{ display: 'flex', gap: '24px', alignItems: 'center', marginTop: '50px', flexWrap: 'wrap' }}>
        {/* Basic Popover */}
        <Popover title="Basic Popover" description="This is a basic popover with title and description">
          <Button variant="outlined">Basic Popover</Button>
        </Popover>

        {/* Popover with Arrow */}
        <Popover
          title="Popover with Arrow"
          description="This popover has an arrow pointing to the trigger element"
          showArrow
          placement="bottom"
        >
          <Button variant="outlined">With Arrow</Button>
        </Popover>

        {/* Popover with Close Button */}
        <Popover
          title="Closable Popover"
          description="This popover has a close button in the top-right corner"
          showClose
          placement="right"
        >
          <Button variant="outlined">With Close Button</Button>
        </Popover>

        {/* Popover with Custom Content */}
        <Popover
          content={
            <Box
              sx={{
                display: 'flex',
                width: '280px',
                height: '140px',
                flexDirection: 'column',
                justifyContent: 'space-between',
                gap: 'var(--spaceBetween-vertical-lg, 24px)',
                marginBottom: '16px',
              }}
            >
              <Typography variant="titleSmall" sx={{ color: 'var(--palette-onSurfaceVariant)' }}>
                Custom Content
              </Typography>
              <Typography variant="bodySmall" sx={{ color: 'var(--palette-onSurfaceVariant)' }}>
                This popover has custom content with action buttons
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 'var(--spaceBetween-horizontal-lg, 16px)' }}>
                <Button variant="text">Cancel</Button>
                <Button variant="filled">Confirm</Button>
              </Box>
            </Box>
          }
          showArrow
          showClose
          placement="left"
        >
          <Button variant="outlined">Custom Content</Button>
        </Popover>

        {/* Popover with Backdrop */}
        <Popover
          title="Modal Popover"
          description="This popover has a backdrop and blocks interaction with the page"
          showBackdrop
          showArrow
          placement="top"
        >
          <Button variant="outlined">With Backdrop</Button>
        </Popover>
      </div>
    </div>
  );
}
