import React from 'react';
import { Typography } from '@hxnova/react-components/Typography';
import { ColumnType, DataGrid, SortDirection } from '@hxnova/react-components/DataGrid';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Link } from '@hxnova/react-components/Link';
import { Tag } from '@hxnova/react-components/Tag';
import Icon from '@hxnova/icons/Icon';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  address: string;
  status: 'active' | 'pending' | 'deactivated';
};
const data: DataType[] = [
  {
    id: 1,
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    status: 'active',
    email: '<EMAIL>',
    address: '123 Oak St, Springfield, IL',
  },
  {
    id: 2,
    firstName: 'Isla',
    lastName: '<PERSON>',
    status: 'active',
    email: '<EMAIL>',
    address: '456 Maple Ave, Rivertown, CA',
  },
  {
    id: 3,
    firstName: '<PERSON><PERSON>',
    lastName: '<PERSON>',
    status: 'deactivated',
    email: '<EMAIL>',
    address: '789 Pine Rd, Lakeview, TX',
  },
  {
    id: 4,
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    status: 'active',
    email: '<EMAIL>',
    address: '321 Birch Blvd, Hilltop, NY',
  },
  {
    id: 5,
    firstName: 'Ava',
    lastName: 'Brown',
    status: 'pending',
    email: '<EMAIL>',
    address: '654 Cedar Ln, Westfield, FL',
  },
  {
    id: 6,
    firstName: 'Noah',
    lastName: 'Williams',
    status: 'deactivated',
    email: '<EMAIL>',
    address: '987 Spruce Cir, Eastside, WA',
  },
  {
    id: 7,
    firstName: 'Olivia',
    lastName: 'Jones',
    status: 'active',
    email: '<EMAIL>',
    address: '159 Elm St, Downtown, OR',
  },
  {
    id: 8,
    firstName: 'Mason',
    lastName: 'Garcia',
    status: 'pending',
    email: '<EMAIL>',
    address: '753 Willow Way, Sunnyside, AZ',
  },
  {
    id: 9,
    firstName: 'Sophia',
    lastName: 'Martinez',
    status: 'active',
    email: '<EMAIL>',
    address: '852 Fir Pl, Crestview, NV',
  },
  {
    id: 10,
    firstName: 'Jacob',
    lastName: 'Davis',
    status: 'active',
    email: '<EMAIL>',
    address: '951 Oak St, Meadowbrook, MN',
  },
  {
    id: 11,
    firstName: 'Charlotte',
    lastName: 'Rodriguez',
    status: 'deactivated',
    email: '<EMAIL>',
    address: '159 Maple Ave, Greenfield, MI',
  },
  {
    id: 12,
    firstName: 'Ethan',
    lastName: 'Wilson',
    status: 'active',
    email: '<EMAIL>',
    address: '753 Pine Rd, Lakeside, KY',
  },
  {
    id: 13,
    firstName: 'Amelia',
    lastName: 'Anderson',
    status: 'active',
    email: '<EMAIL>',
    address: '654 Cedar Ln, Fairview, OH',
  },
  {
    id: 14,
    firstName: 'James',
    lastName: 'Taylor',
    status: 'active',
    email: '<EMAIL>',
    address: '321 Birch Blvd, Coral Springs, FL',
  },
  {
    id: 15,
    firstName: 'Avery',
    lastName: 'Thomas',
    status: 'deactivated',
    email: '<EMAIL>',
    address: '987 Spruce Cir, Kingston, NY',
  },
  {
    id: 16,
    firstName: 'Logan',
    lastName: 'Hernandez',
    status: 'active',
    email: '<EMAIL>',
    address: '159 Elm St, Pineville, VA',
  },
  {
    id: 17,
    firstName: 'Ella',
    lastName: 'Moore',
    status: 'active',
    email: '<EMAIL>',
    address: '951 Oak St, Ridgewood, NJ',
  },
  {
    id: 18,
    firstName: 'Lucas',
    lastName: 'Martin',
    status: 'deactivated',
    email: '<EMAIL>',
    address: '852 Fir Pl, Belleville, IL',
  },
  {
    id: 19,
    firstName: 'Mia',
    lastName: 'Lee',
    status: 'active',
    email: '<EMAIL>',
    address: '753 Willow Way, Brookfield, WI',
  },
  {
    id: 20,
    firstName: 'Jackson',
    lastName: 'Lee',
    status: 'active',
    email: '<EMAIL>',
    address: '654 Cedar Ln, Maplewood, MN',
  },
  {
    id: 21,
    firstName: 'Grace',
    lastName: 'Lewis',
    status: 'active',
    email: '<EMAIL>',
    address: '321 Birch Blvd, Springfield, MO',
  },
  {
    id: 22,
    firstName: 'Benjamin',
    lastName: 'Walker',
    status: 'deactivated',
    email: '<EMAIL>',
    address: '987 Spruce Cir, Seattle, WA',
  },
  {
    id: 23,
    firstName: 'Scarlett',
    lastName: 'Hall',
    status: 'active',
    email: '<EMAIL>',
    address: '159 Elm St, Portland, OR',
  },
  {
    id: 24,
    firstName: 'Oliver',
    lastName: 'Allen',
    status: 'pending',
    email: '<EMAIL>',
    address: '951 Oak St, Denver, CO',
  },
  {
    id: 25,
    firstName: 'Chloe',
    lastName: 'Young',
    status: 'deactivated',
    email: '<EMAIL>',
    address: '852 Fir Pl, Atlanta, GA',
  },
  {
    id: 26,
    firstName: 'Henry',
    lastName: 'Hernandez',
    status: 'active',
    email: '<EMAIL>',
    address: '753 Willow Way, Nashville, TN',
  },
  {
    id: 27,
    firstName: 'Sofia',
    lastName: 'King',
    status: 'active',
    email: '<EMAIL>',
    address: '654 Cedar Ln, Tucson, AZ',
  },
  {
    id: 28,
    firstName: 'William',
    lastName: 'Wright',
    status: 'active',
    email: '<EMAIL>',
    address: '321 Birch Blvd, Omaha, NE',
  },
  {
    id: 29,
    firstName: 'Zoe',
    lastName: 'Scott',
    status: 'active',
    email: '<EMAIL>',
    address: '987 Spruce Cir, Charlotte, NC',
  },
  {
    id: 30,
    firstName: 'Samuel',
    lastName: 'Green',
    status: 'active',
    email: '<EMAIL>',
    address: '159 Elm St, Miami, FL',
  },
  {
    id: 31,
    firstName: 'Lily',
    lastName: 'Adams',
    status: 'active',
    email: '<EMAIL>',
    address: '951 Oak St, Albuquerque, NM',
  },
  {
    id: 32,
    firstName: 'David',
    lastName: 'Baker',
    status: 'deactivated',
    email: '<EMAIL>',
    address: '852 Fir Pl, Dallas, TX',
  },
  {
    id: 33,
    firstName: 'Addison',
    lastName: 'Gonzalez',
    status: 'active',
    email: '<EMAIL>',
    address: '753 Willow Way, Boston, MA',
  },
  {
    id: 34,
    firstName: 'Matthew',
    lastName: 'Nelson',
    status: 'active',
    email: '<EMAIL>',
    address: '654 Cedar Ln, Seattle, WA',
  },
  {
    id: 35,
    firstName: 'Natalie',
    lastName: 'Carter',
    status: 'active',
    email: '<EMAIL>',
    address: '321 Birch Blvd, San Diego, CA',
  },
  {
    id: 36,
    firstName: 'Daniel',
    lastName: 'Mitchell',
    status: 'deactivated',
    email: '<EMAIL>',
    address: '987 Spruce Cir, Riverside, CA',
  },
  {
    id: 37,
    firstName: 'Lucy',
    lastName: 'Perez',
    status: 'active',
    email: '<EMAIL>',
    address: '159 Elm St, Little Rock, AR',
  },
  {
    id: 38,
    firstName: 'Joseph',
    lastName: 'Roberts',
    status: 'active',
    email: '<EMAIL>',
    address: '951 Oak St, Sacramento, CA',
  },
  {
    id: 39,
    firstName: 'Victoria',
    lastName: 'Turner',
    status: 'active',
    email: '<EMAIL>',
    address: '852 Fir Pl, Tucson, AZ',
  },
  {
    id: 40,
    firstName: 'Isaac',
    lastName: 'Phillips',
    status: 'active',
    email: '<EMAIL>',
    address: '753 Willow Way, Orlando, FL',
  },
  {
    id: 41,
    firstName: 'Ella',
    lastName: 'Campbell',
    status: 'pending',
    email: '<EMAIL>',
    address: '654 Cedar Ln, Boise, ID',
  },
  {
    id: 42,
    firstName: 'Leo',
    lastName: 'Parker',
    status: 'active',
    email: '<EMAIL>',
    address: '321 Birch Blvd, Anchorage, AK',
  },
  {
    id: 43,
    firstName: 'Aria',
    lastName: 'Evans',
    status: 'active',
    email: '<EMAIL>',
    address: '987 Spruce Cir, Fargo, ND',
  },
  {
    id: 44,
    firstName: 'Caleb',
    lastName: 'Edwards',
    status: 'active',
    email: '<EMAIL>',
    address: '159 Elm St, Richmond, VA',
  },
  {
    id: 45,
    firstName: 'Aubrey',
    lastName: 'Collins',
    status: 'deactivated',
    email: '<EMAIL>',
    address: '951 Oak St, Wilmington, DE',
  },
  {
    id: 46,
    firstName: 'Anthony',
    lastName: 'Stewart',
    status: 'active',
    email: '<EMAIL>',
    address: '852 Fir Pl, Salt Lake City, UT',
  },
  {
    id: 47,
    firstName: 'Hailey',
    lastName: 'Sanchez',
    status: 'active',
    email: '<EMAIL>',
    address: '753 Willow Way, Omaha, NE',
  },
  {
    id: 48,
    firstName: 'Dylan',
    lastName: 'Morris',
    status: 'active',
    email: '<EMAIL>',
    address: '654 Cedar Ln, Atlanta, GA',
  },
  {
    id: 49,
    firstName: 'Stella',
    lastName: 'Rogers',
    status: 'deactivated',
    email: '<EMAIL>',
    address: '321 Birch Blvd, Philadelphia, PA',
  },
  {
    id: 50,
    firstName: 'Gabriel',
    lastName: 'Reed',
    status: 'active',
    email: '<EMAIL>',
    address: '987 Spruce Cir, Detroit, MI',
  },
  {
    id: 51,
    firstName: 'AA',
    lastName: 'BB',
    status: 'active',
    email: '<EMAIL>',
    address: '159 Elm St, Columbus, OH',
  },
];

const basicData = data.slice(0, 5);

const basicColumns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
  { field: 'fullName', header: 'Full Name', width: 300, cell: (row) => `${row.firstName} ${row.lastName}` },
];

const flexColumns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', flex: 1, minWidth: 100 },
  { field: 'firstName', header: 'First Name', flex: 1, minWidth: 200 },
  { field: 'lastName', header: 'Last Name', flex: 1, minWidth: 200 },
  {
    field: 'fullName',
    header: 'Full Name',
    flex: 1.5,
    minWidth: 200,
    cell: (row) => `${row.firstName} ${row.lastName}`,
  },
];

const cellCustomColumns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150, visible: false },
  {
    field: 'avatar',
    header: 'User',
    width: 300,
    cell: (paras: DataType) => (
      <div sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Avatar>
          {paras.firstName ? paras.firstName.charAt(0).toUpperCase() : ''}
          {paras.lastName ? paras.lastName.charAt(0).toUpperCase() : ''}
        </Avatar>
        <Link href="https://www.google.com" target="_blank">
          <Typography variant="bodySmall">{`${paras.firstName} ${paras.lastName}`}</Typography>
        </Link>
      </div>
    ),
  },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
  {
    field: 'status',
    header: 'Status',
    width: 200,
    cell: (paras: DataType) => (
      <Tag
        intensity="subtle"
        variant={paras.status === 'active' ? 'success' : paras.status === 'pending' ? 'warning' : 'error'}
        label={`${paras.status.charAt(0).toUpperCase()}${paras.status.substring(1, paras.status.length)}`}
      />
    ),
  },
];

const sortCustomColumns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  {
    field: 'avatar',
    header: 'User',
    width: 300,
    cell: (paras: DataType) => (
      <div sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Avatar>
          {paras.firstName ? paras.firstName.charAt(0).toUpperCase() : ''}
          {paras.lastName ? paras.lastName.charAt(0).toUpperCase() : ''}
        </Avatar>
        <Link href="https://www.google.com" target="_blank">
          <Typography variant="bodySmall">{`${paras.firstName} ${paras.lastName}`}</Typography>
        </Link>
      </div>
    ),
    sortFn: (d1: DataType, d2: DataType, sort: SortDirection) => {
      const d1Name = `${d1.firstName} ${d1.lastName}`;
      const d2Name = `${d2.firstName} ${d2.lastName}`;
      return sort === 'asc' ? d1Name.localeCompare(d2Name) : d2Name.localeCompare(d1Name);
    },
  },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
  {
    field: 'status',
    header: 'Status',
    width: 200,
    cell: (paras: DataType) => (
      <Tag
        intensity="subtle"
        variant={paras.status === 'active' ? 'success' : paras.status === 'pending' ? 'warning' : 'error'}
        label={`${paras.status.charAt(0).toUpperCase()}${paras.status.substring(1, paras.status.length)}`}
      />
    ),
    sortFn: (d1: DataType, d2: DataType, sort: SortDirection) => {
      const sortOrder = ['active', 'pending', 'deactivated'];
      const statusA = sortOrder.indexOf(d1.status);
      const statusB = sortOrder.indexOf(d2.status);

      if (statusA === -1) {
        return 1;
      }
      if (statusB === -1) {
        return -1;
      }
      if (sort === 'asc') {
        return statusA - statusB;
      } else {
        return statusB - statusA;
      }
    },
  },
];

const isRowExpandable = (row: DataType) => {
  return row.status === 'active';
};

const expandedRowPanelRender = (row: DataType) => {
  return (
    <div
      sx={{
        height: '100%',
        padding: '24px',
        backgroundColor: 'var(--palette-background-paper)',
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '32px',
        borderTop: '1px solid var(--palette-divider)',
      }}
    >
      <div sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        <Typography variant="titleMedium" sx={{ marginBottom: '8px' }}>
          Contact Information
        </Typography>

        <div sx={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Icon family="material" name="email" size="24" color="primary" />
          <div>
            <Typography variant="bodyMedium" color="textSecondary">
              Email
            </Typography>
            <Typography variant="bodyMedium">{row.email}</Typography>
          </div>
        </div>

        <div sx={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Icon family="material" name="location_on" size="24" color="primary" />
          <div>
            <Typography variant="bodyMedium" color="textSecondary">
              Address
            </Typography>
            <Typography variant="bodyMedium">{row.address}</Typography>
          </div>
        </div>
      </div>
      {row.id !== 1 && (
        <div
          sx={{
            gridColumn: '1 / -1',
            borderTop: '1px solid var(--palette-divider)',
          }}
        >
          <Typography variant="titleMedium" sx={{ marginBottom: '8px' }}>
            Recent Activity
          </Typography>
          <div sx={{ display: 'flex', gap: '24px' }}>
            <div sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Icon family="material" name="shopping_cart" size="24" />
              <Typography variant="bodyMedium">3 recent orders</Typography>
            </div>
            <div sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Icon family="material" name="support_agent" size="24" />
              <Typography variant="bodyMedium">2 support tickets</Typography>
            </div>
          </div>
          <DataGrid
            sx={{ marginTop: '8px' }}
            columns={[
              { field: 'id', header: 'Order ID', width: 300, cell: (row) => <Link href="#">{row.id}</Link> },
              { field: 'event', header: 'Event', flex: 1 },
            ]}
            data={[
              { id: 'order-001', event: 'Created a ticket for #122.' },
              { id: 'order-002', event: 'Added user James A to Group A.' },
              { id: 'order-003', event: 'Removed user James B from Group B.' },
            ]}
            pagination={false}
          />
        </div>
      )}
    </div>
  );
};

const getExpandedRowHeight = (row: DataType) => {
  return row.id === 1 ? 200 : 400;
};

export default function DataGridKitchen() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
      <div sx={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Basic</Typography>
        <DataGrid columns={basicColumns} data={basicData} />
      </div>

      <div sx={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Responsive flex columns</Typography>
        <DataGrid columns={flexColumns} data={basicData} />
      </div>

      <div sx={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Checkbox selection</Typography>
        <DataGrid
          columns={basicColumns}
          data={basicData}
          rowSelectionMode="checkboxSelection"
          initialState={{ selectedRows: [2, 4] }}
        />
      </div>

      <div sx={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Radio Selection</Typography>
        <DataGrid
          columns={basicColumns}
          data={basicData}
          rowSelectionMode="radioSelection"
          initialState={{ selectedRows: [2] }}
        />
      </div>

      <div sx={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Custom cell</Typography>
        <DataGrid columns={cellCustomColumns} data={basicData} />
      </div>

      <div sx={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Sort</Typography>
        <DataGrid
          columns={sortCustomColumns}
          data={basicData}
          initialState={{ sortedColumns: [{ field: 'status', sort: 'asc' }] }}
        />
      </div>

      <div sx={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Scrollbar</Typography>
        <div sx={{ display: 'flex', flexDirection: 'column', maxHeight: '400px', maxWidth: '600px' }}>
          <DataGrid columns={cellCustomColumns} data={data} />
        </div>
      </div>

      <div sx={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Pagination</Typography>
        <div sx={{ display: 'flex', flexDirection: 'column', maxHeight: '400px' }}>
          <DataGrid
            columns={cellCustomColumns}
            data={data}
            initialState={{
              pagination: {
                rowsPerPage: 5,
                page: 1, // zero-based index
              },
            }}
            paginationConfig={{
              labelRowsPerPage: 'Rows per page',
              renderPageInfo: (page, totalPages) => {
                return `Rows ${page + 1} of ${totalPages}`;
              },
              rowsPerPageOptions: [5, 10, 25, 50],
            }}
          />
        </div>
      </div>

      <div sx={{ display: 'flex', flexDirection: 'column' }}>
        <Typography variant="titleSmall">Expanded Row</Typography>
        <DataGrid
          columns={basicColumns}
          data={basicData}
          isRowExpandable={isRowExpandable}
          expandedRowPanelRender={expandedRowPanelRender}
          getExpandedRowHeight={getExpandedRowHeight}
        />
      </div>
    </div>
  );
}
