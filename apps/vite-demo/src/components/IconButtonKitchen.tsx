import { IconButtonProps, IconButton, Typography } from '@hxnova/react-components';
import { Icon } from '@hxnova/icons';

export default function IconButtonKitchen() {
  return (
    <div sx={{ margin: '20px', display: 'flex', flexDirection: 'row', gap: '30px' }}>
      {(['small', 'medium', 'large'] as Array<IconButtonProps['size']>).map((size) => (
        <div key={size}>
          <Typography
            variant={'labelMedium'}
            sx={(theme) => ({ display: 'block', marginBottom: '12px', color: theme.vars.palette.onSurface })}
          >
            {size}
          </Typography>
          <div
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '20px',
            }}
          >
            {[false, true].map((disabled) => (
              <div key={`${size}-${disabled}`}>
                <div
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '12px',
                  }}
                >
                  <div
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '12px',
                    }}
                  >
                    {(['filled', 'outlined', 'standard', 'neutral'] as Array<IconButtonProps['variant']>).map(
                      (variant) => (
                        <IconButton
                          key={`${size}-${disabled}-${variant}`}
                          size={size}
                          disabled={disabled}
                          variant={variant}
                        >
                          <Icon family="material" name="edit" />
                        </IconButton>
                      ),
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
