import { Alert, AlertProps } from '@hxnova/react-components';
import Icon from '@hxnova/icons/Icon';

export default function SegmentedButtonGroupKitchen() {
  return (
    <div sx={{ margin: '20px', display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <div sx={{ display: 'flex', flexDirection: 'row', gap: '12px' }}>
        {(['bold', 'subtle'] as Array<AlertProps['intensity']>).map((intensity) => (
          <div key={intensity} sx={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {(['primary', 'error', 'warning', 'info', 'success'] as Array<AlertProps['color']>).map((color, index) => (
              <Alert
                sx={{ width: '360px' }}
                key={`${intensity}-${color}`}
                intensity={intensity}
                orientation={index > 3 ? 'vertical' : 'horizontal'}
                color={color}
                startDecorator={<Icon family="material" name="info" size={24} />}
                action={{
                  label: 'Button',
                  onClick: () => {},
                }}
                onClose={() => {}}
              >
                {index < 2
                  ? 'Single line text'
                  : index < 4
                    ? 'Two line snackbar with close affordance'
                    : 'Two line snackbar with close affordance and longer actions'}
              </Alert>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}
