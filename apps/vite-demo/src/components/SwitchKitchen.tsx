import { Switch } from '@hxnova/react-components/Switch';
import Icon from '@hxnova/icons/Icon';

export default function SwitchKitchen() {
  return (
    <div sx={(theme) => ({ padding: '24px', color: theme.vars.palette.onSurfaceVariant })}>
      <div sx={{ marginBottom: '32px' }}>
        <h3>Basic Switches with Label</h3>
        <div sx={{ display: 'flex', gap: 150 }}>
          <Switch defaultChecked endDecorator={'Default Checked'} />
          <Switch required defaultChecked endDecorator={'Required'} />
          <Switch checked disabled defaultChecked endDecorator={'Disabled Checked'} />
          <Switch disabled defaultChecked endDecorator={'Disabled Unchecked'} />
        </div>
      </div>

      {/* Density Variants */}
      <div sx={{ marginBottom: '32px' }}>
        <h3>Size Variants</h3>
        <div sx={{ display: 'flex', gap: '24px', alignItems: 'center' }}>
          <div>
            <h4>Medium</h4>
            <Switch size="medium" defaultChecked />
            <Switch size="medium" />
          </div>
          <div>
            <h4>Small</h4>
            <Switch size="small" defaultChecked />
            <Switch size="small" />
          </div>
          <div>
            <h4>Large</h4>
            <Switch size="large" defaultChecked />
            <Switch size="large" />
          </div>
        </div>
      </div>

      {/* With Icons */}
      <div sx={{ marginBottom: '32px' }}>
        <h3>With Icons</h3>
        <div sx={{ display: 'flex', gap: '24px', alignItems: 'center' }}>
          <div>
            <h4>Medium with Icon</h4>
            <Switch
              onIcon={<Icon family="material" name="check" size={24} />}
              offIcon={<Icon family="material" name="close" size={24} />}
              size="medium"
              defaultChecked
            />
            <Switch
              onIcon={<Icon family="material" name="check" size={24} />}
              offIcon={<Icon family="material" name="close" size={24} />}
              size="medium"
            />
          </div>
          <div>
            <h4>Small with Icon</h4>
            <Switch
              onIcon={<Icon family="material" name="check" size={24} />}
              offIcon={<Icon family="material" name="close" size={24} />}
              size="small"
              defaultChecked
            />
            <Switch
              onIcon={<Icon family="material" name="check" size={24} />}
              offIcon={<Icon family="material" name="close" size={24} />}
              size="small"
            />
          </div>
          <div>
            <h4>Large with Icon</h4>
            <Switch
              onIcon={<Icon family="material" name="check" size={24} />}
              offIcon={<Icon family="material" name="close" size={24} />}
              size="large"
              defaultChecked
            />
            <Switch
              onIcon={<Icon family="material" name="check" size={24} />}
              offIcon={<Icon family="material" name="close" size={24} />}
              size="large"
            />
          </div>
          <div>
            <h4>Disabled with Icon</h4>
            <Switch
              disabled
              onIcon={<Icon family="material" name="check" size={24} />}
              offIcon={<Icon family="material" name="close" size={24} />}
              size="medium"
              defaultChecked
            />
            <Switch
              disabled
              onIcon={<Icon family="material" name="check" size={24} />}
              offIcon={<Icon family="material" name="close" size={24} />}
              size="medium"
            />
          </div>
        </div>
      </div>

      {/* With Decorators */}
      <div sx={{ marginBottom: '32px' }}>
        <h3>With Decorators</h3>
        <div
          sx={{
            display: 'flex',
            flexDirection: 'row',
            gap: '150px',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}
        >
          <Switch startDecorator="Start Text" defaultChecked />
          <Switch endDecorator="End Text" defaultChecked />
          <Switch startDecorator="Start" endDecorator="End" defaultChecked />
          <Switch disabled startDecorator="Disabled" endDecorator="Switch" />
        </div>
      </div>

      {/* Size + Icon + Decorator Combinations */}
      <div>
        <h3>Combined Features</h3>
        <div
          sx={{
            display: 'flex',
            flexDirection: 'row',
            gap: '200px',
            width: '100%',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}
        >
          <Switch size="medium" startDecorator="Medium Unchecked" />
          <Switch
            size="small"
            startDecorator="Small With Icons"
            onIcon={<Icon family="material" name="check" size={24} />}
            offIcon={<Icon family="material" name="close" size={24} />}
            defaultChecked
          />
          <Switch size="large" endDecorator="Large Checked" defaultChecked onChange={() => console.log('onChange')} />
        </div>
      </div>
    </div>
  );
}
