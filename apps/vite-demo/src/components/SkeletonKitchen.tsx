import React from 'react';
import { Typography } from '@hxnova/react-components/Typography';
import { Grid } from '@hxnova/react-components/Grid';
import { Card } from '@hxnova/react-components/Card';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Skeleton } from '@hxnova/react-components/Skeleton';
import avatarSrc from '../assets/avatar.jpeg';

function TypographyDemo(props: { loading?: boolean }) {
  const { loading = false } = props;

  return (
    <div>
      <Typography variant="bodyLarge">{loading ? <Skeleton variant="text" /> : 'bodyLarge'}</Typography>
      <Typography variant="bodyMedium">{loading ? <Skeleton variant="text" /> : 'bodyMedium'}</Typography>
      <Typography variant="bodySmall">{loading ? <Skeleton variant="text" /> : 'bodySmall'}</Typography>
    </div>
  );
}

function SkeletonChildrenDemo(props: { loading?: boolean }) {
  const { loading = false } = props;
  return (
    <Card.Root>
      <Card.Header
        avatar={
          loading ? (
            <Skeleton variant="circular">
              <Avatar />
            </Skeleton>
          ) : (
            <Avatar alt="Remy Sharp" src={avatarSrc} />
          )
        }
        heading={
          loading ? (
            <Skeleton variant="text" width="100%">
              <Typography variant="titleSmall">.</Typography>
            </Skeleton>
          ) : (
            'Daniela Maas'
          )
        }
        subheading={
          loading ? (
            <Skeleton variant="text" width="100%">
              <Typography variant="bodyMedium">.</Typography>
            </Skeleton>
          ) : (
            <Typography>Yesterday</Typography>
          )
        }
      />

      {loading ? (
        <Skeleton variant="rectangular" width="100%">
          <div sx={{ paddingTop: '57%' }} />
        </Skeleton>
      ) : (
        <Card.Media
          component="img"
          image={'https://cdn.sanity.io/images/eqlh3dcx/dev/c2ab4812a5485ba4a15aa8f4517b94edb5728c1d-1142x643.png'}
        />
      )}
    </Card.Root>
  );
}

export default function SkeletonKitchen() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Typography variant="titleLarge" sx={{ color: 'var(--palette-onSurface)' }}>
        Variants
      </Typography>

      <div sx={{ display: 'flex', flexDirection: 'column', gap: 8, width: 210 }}>
        {/* For variant="text", adjust the height via line-height */}
        <Skeleton variant="text" sx={{ lineHeight: '2rem' }} />

        {/* For other variants, adjust the size with `width` and `height` */}
        <Skeleton variant="circular" width={40} height={40} />
        <Skeleton variant="rectangular" height={60} />
        <Skeleton variant="rounded" height={60} />
      </div>

      <Typography variant="titleLarge" sx={{ color: 'var(--palette-onSurface)' }}>
        Animations
      </Typography>

      <div sx={{ display: 'flex', flexDirection: 'column', gap: 8, width: 210 }}>
        <Skeleton />
        <Skeleton animation="wave" />
        <Skeleton animation={false} />
      </div>

      <Typography variant="titleLarge" sx={{ color: 'var(--palette-onSurface)' }}>
        Inferring dimensions
      </Typography>

      <div sx={{ display: 'flex', flexDirection: 'column', gap: 16, width: 600 }}>
        <Typography>It works well when it comes to typography as its height is set using em units.</Typography>
        <Grid container spacing={8}>
          <Grid size={6}>
            <TypographyDemo loading />
          </Grid>
          <Grid size={6}>
            <TypographyDemo />
          </Grid>
        </Grid>
        <Typography>You can pass children and it will infer its width and height from them.</Typography>
        <Grid container spacing={8}>
          <Grid size={6}>
            <SkeletonChildrenDemo loading />
          </Grid>
          <Grid size={6}>
            <SkeletonChildrenDemo />
          </Grid>
        </Grid>
      </div>
    </div>
  );
}
