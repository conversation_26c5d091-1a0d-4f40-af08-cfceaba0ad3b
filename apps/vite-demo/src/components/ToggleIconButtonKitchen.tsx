import { ToggleIconButtonProps, ToggleIconButton, Typography } from '@hxnova/react-components';
import { Icon } from '@hxnova/icons';

export default function IconButtonKitchen() {
  return (
    <div sx={{ margin: '20px', display: 'flex', flexDirection: 'row', gap: '30px' }}>
      {(['small', 'medium', 'large'] as Array<ToggleIconButtonProps['size']>).map((size) => (
        <div key={size}>
          <Typography
            variant={'labelMedium'}
            sx={(theme) => ({ display: 'block', marginBottom: '12px', color: theme.vars.palette.onSurface })}
          >
            {size}
          </Typography>
          <div
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}
          >
            {(['filled', 'outlined', 'standard', 'neutral'] as Array<ToggleIconButtonProps['variant']>).map(
              (variant) => (
                <div
                  key={`${size}-${variant}`}
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '12px',
                  }}
                >
                  {([false, true] as Array<ToggleIconButtonProps['selected']>).map((selected) => (
                    <div
                      key={`${size}-${variant}-${selected}`}
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '20px',
                      }}
                    >
                      {([false, true] as Array<ToggleIconButtonProps['disabled']>).map((disabled) => (
                        <div key={`${size}-${variant}-${selected}-${disabled}`}>
                          <ToggleIconButton
                            key={`${size}-${disabled}`}
                            size={size}
                            disabled={disabled}
                            variant={variant}
                            selected={selected}
                          >
                            <Icon family="material" name="settings" />
                          </ToggleIconButton>
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              ),
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
