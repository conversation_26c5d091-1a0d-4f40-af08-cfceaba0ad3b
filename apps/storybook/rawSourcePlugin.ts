import type { Plugin } from 'vite';
import fs from 'fs/promises';

const mergeFile = (absoluteDir: string, relativePath: string) => {
  const absParts = absoluteDir.split('/').filter((part) => part !== '');
  const relParts = relativePath.split('/');

  for (const part of relParts) {
    if (part === '' || part === '.') continue;
    if (part === '..') {
      if (absParts.length > 0) absParts.pop();
    } else {
      absParts.push(part);
    }
  }

  return '/' + absParts.join('/');
};

export default function rawSourcePlugin(): Plugin {
  return {
    name: 'vite-raw-source-plugin',
    enforce: 'pre',

    async load(id) {
      if (id.includes('tsx?raw_source')) {
        try {
          const filePath = id.replace('._tsx?raw_source', '.tsx');
          const source = await fs.readFile(filePath, 'utf-8');
          return `export default ${JSON.stringify(source)}`;
        } catch (error) {
          console.error(`failed to resolve ${id}`, error);
        }
      }
    },

    resolveId(source: string, importer: string | undefined) {
      if (source.includes('tsx?raw')) {
        const filePath = source.split('?')[0];
        if (importer) {
          const resolvedPath = mergeFile(importer.substring(0, importer.lastIndexOf('/')), filePath);
          const finalPath = resolvedPath.replace('.tsx', '._tsx?raw_source');
          return finalPath;
        }
        return `${source}`;
      }
    },
  };
}
