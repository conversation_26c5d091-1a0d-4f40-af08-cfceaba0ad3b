# API Documentation

- [NavigationTop](#navigationtop)

# NavigationTop

API reference docs for the React NavigationTop component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `NavigationTop` component, you can choose to import it directly or through the main entry point.

```jsx
import { NavigationTop } from '@hxnova/react-components/NavigationTop';
// or
import { NavigationTop } from '@hxnova/react-components';
```

## Props

The properties available for the `NavigationTop` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **divider** | ``false ⏐ true`` | `true` | Whether or not to show a divider below the bar |
| **iconActions** | ``{ icon: ReactNode; onClick: () => void; label: string; }[]`` | - | Secondary actions to display in the toolbar. These will be presented as icon buttons. |
| **onSearchChange** | ``(value: string) => void`` | - | Callback function executed when the search value changes. If provided, a search bar will be displayed in the toolbar<br>@param value the current text in the search field |
| **pageTitle** | `ReactNode` | - | Title to display in the toolbar |
| **primaryActions** | `ButtonProps[]` | - | Primary actions to display in the toolbar. These will be presented as buttons. |
| **productLogo** | `ReactNode` | - | Product Logo to display in the toolbar |
| **slotProps** | ``{ pageTitle?: SlotProps<"span", TypographyProps, NavigationTopOwnerState> ⏐ undefined; searchBar?: SlotProps<"input", TextFieldProps, NavigationTopOwnerState> ⏐ undefined; iconAction?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `NavigationTopSlots` | `{}` | The components used for each slot inside. |
| **userAvatar** | `ReactNode` | - | User avatar to display at the far right of the toolbar |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `NavigationTop` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| pageTitle | .NovaNavigationTop-pageTitle | `'Typography'` | The component that renders the page title. |
| searchBar | .NovaNavigationTop-searchBar | `'TextField'` | The component that renders the SearchBar. |
| iconAction | .NovaNavigationTop-iconAction | `'IconButton'` | The component that renders the icon action. |

## CSS classes

CSS classes for different states and variations of the `NavigationTop` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaNavigationTop-root | `root` | Class name applied to the root element. |
| .NovaNavigationTop-productLogo | `productLogo` | Class name applied to the logo element. |
| .NovaNavigationTop-navigationContent | `navigationContent` | Class name applied to the children. |
| .NovaNavigationTop-iconActions | `iconActions` | Class name applied to the icon actions element. |
| .NovaNavigationTop-primaryActions | `primaryActions` | Class name applied to the primary actions element. |
| .NovaNavigationTop-userAvatar | `userAvatar` | Class name applied to the user avatar element. |

