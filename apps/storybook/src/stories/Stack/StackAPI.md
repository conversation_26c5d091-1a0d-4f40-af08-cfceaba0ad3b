# API Documentation

- [Stack](#stack)

# Stack

API reference docs for the React Stack component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Stack` component, you can choose to import it directly or through the main entry point.

```jsx
import { Stack } from '@hxnova/react-components/Stack';
// or
import { Stack } from '@hxnova/react-components';
```

## Props

The properties available for the `Stack` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **direction** | ``"row" ⏐ "row-reverse" ⏐ "column" ⏐ "column-reverse" ⏐ ("row" ⏐ "row-reverse" ⏐ "column" ⏐ "column-reverse")[] ⏐ Partial<Record<Breakpoint, "row" ⏐ "row-reverse" ⏐ "column" ⏐ "column-reverse">>`` | `'column'` | Defines the `flex-direction` style property.<br>It is applied for all screen sizes. |
| **divider** | `ReactNode` | - | Add an element between each child. |
| **spacing** | ``string ⏐ number ⏐ (string ⏐ number)[] ⏐ Partial<Record<Breakpoint, string ⏐ number>>`` | `0` | Defines the space between immediate children. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

CSS classes for different states and variations of the `Stack` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaStack-root | `root` | Class name applied to the root element. |

