# API Documentation

- [Drawer.Body](#drawerbody)
- [Drawer.Footer](#drawerfooter)
- [Drawer.Header](#drawerheader)
- [Drawer.NavGroup](#drawernavgroup)
- [Drawer.NavItem](#drawernavitem)
- [Drawer.Root](#drawerroot)

# Drawer.Body

API reference docs for the React Drawer.Body component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Drawer.Body` component, you can choose to import it directly or through the main entry point.

```jsx
import { Drawer } from '@hxnova/react-components/Drawer';
// or
import { Drawer } from '@hxnova/react-components';

// usage
<Drawer.Body>{children}</Drawer.Body>
```

## Props

The properties available for the `Drawer.Body` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, DrawerBodyOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `DrawerBodySlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Drawer.Body` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDrawerBody-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `Drawer.Body` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-expanded | `expanded` | Class name applied to the root element if `expanded=true`. |
| .NovaDrawerBody-collapsed | `collapsed` | Class name applied to the root element if `expanded=false`. |

<br><br>

# Drawer.Footer

API reference docs for the React Drawer.Footer component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Drawer.Footer` component, you can choose to import it directly or through the main entry point.

```jsx
import { Drawer } from '@hxnova/react-components/Drawer';
// or
import { Drawer } from '@hxnova/react-components';

// usage
<Drawer.Footer>{children}</Drawer.Footer>
```

## Props

The properties available for the `Drawer.Footer` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, DrawerFooterOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `DrawerFooterSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Drawer.Footer` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDrawerFooter-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `Drawer.Footer` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-expanded | `expanded` | Class name applied to the root element if `expanded=true`. |
| .NovaDrawerFooter-collapsed | `collapsed` | Class name applied to the root element if `expanded=false`. |

<br><br>

# Drawer.Header

API reference docs for the React Drawer.Header component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Drawer.Header` component, you can choose to import it directly or through the main entry point.

```jsx
import { Drawer } from '@hxnova/react-components/Drawer';
// or
import { Drawer } from '@hxnova/react-components';

// usage
<Drawer.Header>{children}</Drawer.Header>
```

## Props

The properties available for the `Drawer.Header` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **endDecorator** | `ReactNode` | - | Element placed after the children. |
| **pageTitle** | `ReactNode` | - | The name of the current product. |
| **productLogo** | `ReactNode` | - | A component for the specific product logo. It is recommended to use a product logo component from<br>@nexusui /branding. |
| **slotProps** | ``{ root?: SlotProps<"div", object, DrawerHeaderOwnerState> ⏐ undefined; productLogo?: SlotProps<"div", object, DrawerHeaderOwnerState> ⏐ undefined; pageTitle?: SlotProps<...> ⏐ undefined; endDecorator?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `DrawerHeaderSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Drawer.Header` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDrawerHeader-root | `'div'` | The component that renders the root. |
| productLogo | .NovaDrawerHeader-productLogo | `'div'` | The component that renders the product logo. |
| pageTitle | .NovaDrawerHeader-pageTitle | `'span'` | The component that renders the page title. |
| endDecorator | .NovaDrawerHeader-endDecorator | `'div'` | The component that renders the end decorator. |

## CSS classes

CSS classes for different states and variations of the `Drawer.Header` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaDrawerHeader-container | `container` | Class name applied to the container element. |
| .Nova-expanded | `expanded` | Class name applied to the root element if `expanded=true`. |
| .NovaDrawerHeader-collapsed | `collapsed` | Class name applied to the root element if `expanded=false`. |

<br><br>

# Drawer.NavGroup

API reference docs for the React Drawer.NavGroup component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Drawer.NavGroup` component, you can choose to import it directly or through the main entry point.

```jsx
import { Drawer } from '@hxnova/react-components/Drawer';
// or
import { Drawer } from '@hxnova/react-components';

// usage
<Drawer.NavGroup>{children}</Drawer.NavGroup>
```

## Props

The properties available for the `Drawer.NavGroup` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **header** | `ReactNode` | - | The header content of the DrawerNavGroup. |
| **slotProps** | ``{ root?: SlotProps<"ul", object, DrawerNavGroupOwnerState> ⏐ undefined; header?: SlotProps<"span", TypographyProps, DrawerNavGroupOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `DrawerNavGroupSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Drawer.NavGroup` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDrawerNavGroup-root | `'ul'` | The component that renders the root. |
| header | .NovaDrawerNavGroup-header | `Typography` | The component that renders the header. |

## CSS classes

CSS classes for different states and variations of the `Drawer.NavGroup` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-expanded | `expanded` | Class name applied to the root element if `expanded=true`. |
| .NovaDrawerNavGroup-collapsed | `collapsed` | Class name applied to the root element if `expanded=false`. |

<br><br>

# Drawer.NavItem

API reference docs for the React Drawer.NavItem component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Drawer.NavItem` component, you can choose to import it directly or through the main entry point.

```jsx
import { Drawer } from '@hxnova/react-components/Drawer';
// or
import { Drawer } from '@hxnova/react-components';

// usage
<Drawer.NavItem>{children}</Drawer.NavItem>
```

## Props

The properties available for the `Drawer.NavItem` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **itemId** | `string` | - | The unique identifier of the DrawerNavItem. item will be selected if the itemId matches the `activeItem`. |
| **label** | `ReactNode` | - | The label to display in the DrawerNavItem. |
| **selected** | ``false ⏐ true`` | `false` | Use to apply selected styling. |
| **slotProps** | ``{ root?: SlotProps<"li", object, DrawerNavItemOwnerState> ⏐ undefined; startDecorator?: SlotProps<"div", object, DrawerNavItemOwnerState> ⏐ undefined; ... 4 more ...; trailingBadge?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `DrawerNavItemSlots` | `{}` | The components used for each slot inside. |
| **startDecorator** | `ReactNode` | - | Element placed before the children. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **trailingAction** | `ReactNode` | - | The action to display at the end of the DrawerNavItem. |
| **trailingBadge** | `BadgeProps` | - | The Badge to display at the right of the DrawerNavItem. |
| **trailingLabel** | `ReactNode` | - | The label to display at the right of the DrawerNavItem. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Drawer.NavItem` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDrawerNavItem-root | `'li'` | The component that renders the root. |
| startDecorator | .NovaDrawerNavItem-startDecorator | `'div'` | The component that renders the start decorator. |
| label | .NovaDrawerNavItem-label | `'span'` | The component that renders the label. |
| railLabel | .NovaDrawerNavItem-railLabel | `'span'` | The component that renders the rail label. |
| trailingLabel | .NovaDrawerNavItem-trailingLabel | `'span'` | The component that renders the trailing label. |
| trailingAction | .NovaDrawerNavItem-trailingAction | `'div'` | The component that renders the end decorator. |
| trailingBadge | .NovaDrawerNavItem-trailingBadge | `'div'` | The component that renders the badge. |

## CSS classes

CSS classes for different states and variations of the `Drawer.NavItem` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaDrawerNavItem-container | `container` | Class name applied to the container element. |
| .Nova-expanded | `expanded` | Class name applied to the root element if `expanded=true`. |
| .NovaDrawerNavItem-collapsed | `collapsed` | Class name applied to the root element if `expanded=false`. |

<br><br>

# Drawer.Root

API reference docs for the React Drawer.Root component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Drawer.Root` component, you can choose to import it directly or through the main entry point.

```jsx
import { Drawer } from '@hxnova/react-components/Drawer';
// or
import { Drawer } from '@hxnova/react-components';

// usage
<Drawer.Root>{children}</Drawer.Root>
```

## Props

The properties available for the `Drawer.Root` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **activeItem** | `string` | - | `itemId` of the active navigation item. |
| **anchor** | ``"left" ⏐ "top" ⏐ "right" ⏐ "bottom"`` | `'left'` | Side from which the drawer will appear. |
| **closeAfterTransition** | ``false ⏐ true`` | `false` | When set to true the Modal waits until a nested Transition is completed before closing. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **container** | ``null ⏐ Element ⏐ () => Element ⏐ null`` | - | An HTML element or function that returns one.<br>The `container` will have the portal children appended to it.<br>You can also provide a callback, which is called in a React layout effect.<br>This lets you set the container from a ref, and also makes server-side rendering possible.<br>By default, it uses the body of the top-level document object,<br>so it's simply `document.body` most of the time. |
| **disableAutoFocus** | ``false ⏐ true`` | `false` | If `true`, the modal will not automatically shift focus to itself when it opens, and<br>replace it to the last focused element when it closes.<br>This also works correctly with any modal children that have the `disableAutoFocus` prop.<br>Generally this should never be set to `true` as it makes the modal less<br>accessible to assistive technologies, like screen readers. |
| **disableEnforceFocus** | ``false ⏐ true`` | `false` | If `true`, the modal will not prevent focus from leaving the modal while open.<br>Generally this should never be set to `true` as it makes the modal less<br>accessible to assistive technologies, like screen readers. |
| **disableEscapeKeyDown** | ``false ⏐ true`` | `false` | If `true`, hitting escape will not fire the `onClose` callback. |
| **disablePortal** | ``false ⏐ true`` | `false` | The `children` will be under the DOM hierarchy of the parent component. |
| **disableRestoreFocus** | ``false ⏐ true`` | `false` | If `true`, the modal will not restore focus to previously focused element once<br>modal is hidden or unmounted. |
| **disableScrollLock** | ``false ⏐ true`` | `false` | Disable the scroll lock behavior. |
| **expanded** | ``false ⏐ true`` | `true` | If `false`, the drawer displays in a collapsed mode, showing only icons and titles. |
| **hideBackdrop** | ``false ⏐ true`` | `false` | If `true`, the backdrop is not rendered. |
| **onBackdropClick** | ``ReactEventHandler<object>`` | - | ⚠️ **Deprecated**: Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.<br><br>Callback fired when the backdrop is clicked. |
| **onClose** | ``(event: object, reason: "backdropClick" ⏐ "escapeKeyDown") => void`` | - | Callback fired when the component requests to be closed.<br>The `reason` parameter can optionally be used to control the response to `onClose`.<br>@param event The event source of the callback.<br>@param reason Can be: `"escapeKeyDown"`, `"backdropClick"`. |
| **onItemSelect** | ``(itemId: string) => void`` | - | Callback fired whenever an item is clicked. |
| **onToggleSubNavDrawer** | ``(isOpen: boolean) => void`` | - | Callback fired when the sub navigation drawer is toggled.<br>@param isOpen - `true` if the sub navigation drawer is opening, `false` if closing. |
| **onTransitionEnter** | ``() => void`` | - | A function called when a transition enters. |
| **onTransitionExited** | ``() => void`` | - | A function called when a transition has exited. |
| **open** | ``false ⏐ true`` | `false` | If `true`, the component is shown. |
| **showRailLabel** | ``false ⏐ true`` | `false` | If `true`, the labels of navigation items will be shown (navigation rail only). |
| **showSubNavDrawerHeader** | ``false ⏐ true`` | `true` | If `true`, the sub navigation drawer header will be shown, displaying the label of the hovered or clicked nav item (navigation rail only). |
| **slotProps** | ``{ root?: SlotComponentProps<"div", ModalRootSlotPropsOverrides, { children: ReactElement<any, string ⏐ JSXElementConstructor<any>>; ... 17 more ...; exited: boolean; }> ⏐ undefined; backdrop?: SlotComponentProps<...> ⏐ undefined; } & { ...; }`` | `{} {}` | The props used for each slot inside the Modal.<br>The props used for each slot inside. |
| **slots** | `ModalSlots & DrawerRootSlots` | `{} {}` | The components used for each slot inside the Modal.<br>Either a string to use a HTML element or a component.<br>The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **transitionDuration** | ``{ enter?: number ⏐ undefined; exit?: number ⏐ undefined; }`` | `{ enter: 225, exit: 195 }` | The duration for the transition, in milliseconds.<br>You may specify different durations for different transitions:<br>- enter: Duration for the enter transition.<br>- exit: Duration for the exit transition. |
| **variant** | ``"permanent" ⏐ "persistent" ⏐ "temporary"`` | `'temporary'` | The variant to use. |
| **width** | ``string ⏐ number ⏐ string & {}`` | `'360px'` | Width of the Drawer component. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Drawer.Root` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDrawerRoot-root | `'div'` | The component that renders the root. |
| backdrop | .NovaDrawerRoot-backdrop | `'div'` | The component that renders the backdrop. |
| container | .NovaDrawerRoot-container | `'div'` | The component that renders the content of the drawer. |
| subNavContainer | .NovaDrawerRoot-subNavContainer | `'div'` | The component that renders the sub navigation drawer. |

## CSS classes

CSS classes for different states and variations of the `Drawer.Root` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaDrawerRoot-hidden | `hidden` | Class name applied to the root element when open is false. |
| .NovaDrawerRoot-variantPermanent | `variantPermanent` | Class name applied to the root element if `variant="permanent"`. |
| .NovaDrawerRoot-variantPersistent | `variantPersistent` | Class name applied to the root element if `variant="persistent"`. |
| .NovaDrawerRoot-variantTemporary | `variantTemporary` | Class name applied to the root element if `variant="temporary"`. |
| .NovaDrawerRoot-anchorLeft | `anchorLeft` | Class name applied to the root element if `anchor="left"`. |
| .NovaDrawerRoot-anchorTop | `anchorTop` | Class name applied to the root element if `anchor="top"`. |
| .NovaDrawerRoot-anchorRight | `anchorRight` | Class name applied to the root element if `anchor="right"`. |
| .NovaDrawerRoot-anchorBottom | `anchorBottom` | Class name applied to the root element if `anchor="bottom"`. |
| .Nova-expanded | `expanded` | Class name applied to the root element if `expanded=true`. |
| .NovaDrawerRoot-collapsed | `collapsed` | Class name applied to the root element if `expanded=false`. |

