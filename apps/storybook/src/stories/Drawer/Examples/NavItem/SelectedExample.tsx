import React from 'react';

import { Drawer } from '@hxnova/react-components/Drawer';

export default function Demo() {
  return (
    <div
      sx={{
        minHeight: 150,
        position: 'relative',
        border: '1px solid rgba(0,0,0,0.35)',
      }}
    >
      <Drawer.Root
        width={360}
        variant={'permanent'}
        sx={{
          width: 360,
          '& > .NovaDrawerRoot-container': { position: 'absolute' },
        }}
        activeItem="2"
      >
        <Drawer.NavItem itemId="0" sx={{ maxWidth: 360 }} label="Label 1" selected />
        <Drawer.NavItem itemId="1" sx={{ maxWidth: 360 }} label="Label 2" />
        <Drawer.NavItem itemId="2" sx={{ maxWidth: 360 }} label="Label 3" />
      </Drawer.Root>
    </div>
  );
}
