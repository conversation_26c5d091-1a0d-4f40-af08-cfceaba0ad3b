import React from 'react';

import { Drawer } from '@hxnova/react-components/Drawer';
import { Button } from '@hxnova/react-components/Button';

export default function Demo() {
  const [openTemporary, setOpenTemporary] = React.useState(false);

  return (
    <div
      sx={{
        minHeight: 150,
        display: 'flex',
        flexDirection: 'row',
        position: 'relative',
        border: '1px solid rgba(0,0,0,0.35)',
        overflow: 'hidden',
      }}
    >
      <Button sx={{ margin: 16 }} onClick={() => setOpenTemporary(true)}>
        Open Temporary Drawer
      </Button>
      <Drawer.Root
        disablePortal
        disableScrollLock
        open={openTemporary}
        onClose={() => setOpenTemporary(false)}
        sx={{
          position: 'absolute',
          '& > .NovaDrawerRoot-container': { position: 'absolute' },
          '& .NovaDrawerRoot-backdrop': { position: 'absolute' },
        }}
      >
        <span>Temporary Drawer</span>
      </Drawer.Root>
    </div>
  );
}
