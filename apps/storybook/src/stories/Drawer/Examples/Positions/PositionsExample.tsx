import React from 'react';

import { Drawer } from '@hxnova/react-components/Drawer';
import { Button } from '@hxnova/react-components/Button';

export default function Demo() {
  const [openLeft, setOpenLeft] = React.useState(false);
  const [openRight, setOpenRight] = React.useState(false);
  const [openTop, setOpenTop] = React.useState(false);
  const [openBottom, setOpenBottom] = React.useState(false);

  return (
    <div
      sx={{
        minHeight: 150,
        display: 'flex',
        flexDirection: 'row',
        position: 'relative',
        border: '1px solid rgba(0,0,0,0.35)',
        overflow: 'hidden',
      }}
    >
      <Button
        sx={{ margin: 16 }}
        onClick={() => {
          setOpenLeft(true);
        }}
      >
        Open Left Drawer
      </Button>
      <Button
        sx={{ margin: 16 }}
        onClick={() => {
          setOpenRight(true);
        }}
      >
        Open Right Drawer
      </Button>
      <Button
        sx={{ margin: 16 }}
        onClick={() => {
          setOpenTop(true);
        }}
      >
        Open Top Drawer
      </Button>
      <Button
        sx={{ margin: 16 }}
        onClick={() => {
          setOpenBottom(true);
        }}
      >
        Open Bottom Drawer
      </Button>
      <Drawer.Root
        anchor={'left'}
        disablePortal
        disableScrollLock
        open={openLeft}
        onClose={() => setOpenLeft(false)}
        sx={{
          position: 'absolute',
          '& > .NovaDrawerRoot-container': { position: 'absolute' },
          '& .NovaDrawerRoot-backdrop': { position: 'absolute' },
        }}
      >
        <span>Drawer Contents</span>
      </Drawer.Root>
      <Drawer.Root
        anchor={'right'}
        disablePortal
        disableScrollLock
        open={openRight}
        onClose={() => setOpenRight(false)}
        sx={{
          position: 'absolute',
          '& > .NovaDrawerRoot-container': { position: 'absolute' },
          '& .NovaDrawerRoot-backdrop': { position: 'absolute' },
        }}
      >
        <span>Drawer Contents</span>
      </Drawer.Root>
      <Drawer.Root
        anchor={'top'}
        disablePortal
        disableScrollLock
        open={openTop}
        onClose={() => setOpenTop(false)}
        width={100}
        sx={{
          position: 'absolute',
          '& > .NovaDrawerRoot-container': { position: 'absolute' },
          '& .NovaDrawerRoot-backdrop': { position: 'absolute' },
        }}
      >
        <span>Drawer Contents</span>
      </Drawer.Root>
      <Drawer.Root
        anchor={'bottom'}
        disablePortal
        disableScrollLock
        open={openBottom}
        onClose={() => setOpenBottom(false)}
        width={100}
        sx={{
          position: 'absolute',
          '& > .NovaDrawerRoot-container': { position: 'absolute' },
          '& .NovaDrawerRoot-backdrop': { position: 'absolute' },
        }}
      >
        <span>Drawer Contents</span>
      </Drawer.Root>
    </div>
  );
}
