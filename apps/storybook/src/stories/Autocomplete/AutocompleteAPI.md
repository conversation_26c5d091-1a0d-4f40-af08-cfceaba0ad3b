# API Documentation

- [Autocomplete](#autocomplete)

# Autocomplete

API reference docs for the React Autocomplete component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Autocomplete` component, you can choose to import it directly or through the main entry point.

```jsx
import { Autocomplete } from '@hxnova/react-components/Autocomplete';
// or
import { Autocomplete } from '@hxnova/react-components';
```

## Props

The properties available for the `Autocomplete` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **options*** | `readonly T[]` | - | A list of options that will be shown in the Autocomplete. |
| **renderInput*** | ``(params: AutocompleteRenderInputParams) => ReactNode`` | - | Render the input.<br>@param params<br>@returns |
| **autoComplete** | ``false ⏐ true`` | `false` | If `true`, the portion of the selected suggestion that the user hasn't typed,<br>known as the completion string, appears inline after the input cursor in the textbox.<br>The inline completion string is visually highlighted and has a selected state. |
| **autoHighlight** | ``false ⏐ true`` | `false` | If `true`, the first option is automatically highlighted. |
| **autoSelect** | ``false ⏐ true`` | `false` | If `true`, the selected option becomes the value of the input<br>when the Autocomplete loses focus unless the user chooses<br>a different option or changes the character string in the input.<br>When using the `freeSolo` mode, the typed value will be the input value<br>if the Autocomplete loses focus without highlighting an option. |
| **blurOnSelect** | ``false ⏐ true ⏐ "touch" ⏐ "mouse"`` | `false` | Control if the input should be blurred when an option is selected:<br>- `false` the input is not blurred.<br>- `true` the input is always blurred.<br>- `touch` the input is blurred after a touch event.<br>- `mouse` the input is blurred after a mouse event. |
| **clearIcon** | `ReactNode` | `<CloseOutlined />` | The icon to display in place of the default clear icon. |
| **clearOnBlur** | ``false ⏐ true`` | `!props.freeSolo` | If `true`, the input's text is cleared on blur if no value is selected.<br>Set it to `true` if you want to help the user enter a new value.<br>Set it to `false` if you want to help the user resume their search. |
| **clearOnEscape** | ``false ⏐ true`` | `false` | If `true`, clear all values when the user presses escape and the popup is closed. |
| **clearText** | `string` | `'Clear'` | Override the default text for the *clear* icon button. |
| **closeText** | `string` | `'Close'` | Override the default text for the *close popup* icon button. |
| **componentName** | `string` | - | The component name that is using this hook. Used for warnings. |
| **defaultValue** | ``AutocompleteValue<T, Multiple, DisableClearable, FreeSolo>`` | `props.multiple ? [] : null` | The default value. Use when the component is not controlled. |
| **disableClearable** | ``false ⏐ true`` | `false` | If `true`, the input can't be cleared. |
| **disableCloseOnSelect** | ``false ⏐ true`` | `false` | If `true`, the popup won't close when a value is selected. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **disabledItemsFocusable** | ``false ⏐ true`` | `false` | If `true`, will allow focus on disabled items. |
| **disableListWrap** | ``false ⏐ true`` | `false` | If `true`, the list box in the popup will not wrap focus. |
| **disablePortal** | ``false ⏐ true`` | `false` | If `true`, the `Popper` content will be under the DOM hierarchy of the parent component. |
| **filterOptions** | ``(options: T[], state: FilterOptionsState<T>) => T[]`` | `createFilterOptions()` | A function that determines the filtered options to be rendered on search.<br>@param options The options to render.<br>@param state The state of the component.<br>@returns |
| **filterSelectedOptions** | ``false ⏐ true`` | `false` | If `true`, hide the selected options from the list box. |
| **forcePopupIcon** | ``false ⏐ true ⏐ "auto"`` | `'auto'` | Force the visibility display of the popup icon. |
| **freeSolo** | ``false ⏐ true`` | `false` | If `true`, the Autocomplete is free solo, meaning that the user input is not bound to provided options. |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the input will take up the full width of its container. |
| **getLimitTagsText** | ``((more: number) => ReactNode)`` | `(more) => \`+${more}\`` | The label to display when the tags are truncated (`limitTags`).<br>@param more The number of truncated tags.<br>@returns |
| **getOptionDisabled** | ``(option: T) => boolean`` | - | Used to determine the disabled state for a given option.<br>@param option The option to test.<br>@returns |
| **getOptionKey** | ``(option: T ⏐ AutocompleteFreeSoloValueMapping<FreeSolo>) => string ⏐ number`` | - | Used to determine the key for a given option.<br>This can be useful when the labels of options are not unique (since labels are used as keys by default).<br>@param option The option to get the key for.<br>@returns |
| **getOptionLabel** | ``(option: T ⏐ AutocompleteFreeSoloValueMapping<FreeSolo>) => string`` | `(option) => option.label ?? option` | Used to determine the string value for a given option.<br>It's used to fill the input (and the list box options if `renderOption` is not provided).<br>If used in free solo mode, it must accept both the type of the options and a string.<br>@param option<br>@returns |
| **groupBy** | ``(option: T) => string`` | - | If provided, the options will be grouped under the returned string.<br>The groupBy value is also used as the text for group headings when `renderGroup` is not provided.<br>@param options The options to group.<br>@returns |
| **handleHomeEndKeys** | ``false ⏐ true`` | `!props.freeSolo` | If `true`, the component handles the "Home" and "End" keys when the popup is open.<br>It should move focus to the first option and last option, respectively. |
| **id** | `string` | - | This prop is used to help implement the accessibility logic.<br>If you don't provide an id it will fall back to a randomly generated one. |
| **includeInputInList** | ``false ⏐ true`` | `false` | If `true`, the highlight can move to the input. |
| **inputValue** | `string` | - | The input value. |
| **isOptionEqualToValue** | ``(option: T, value: T) => boolean`` | - | Used to determine if the option represents the given value.<br>Uses strict equality by default.<br>⚠️ Both arguments need to be handled, an option can only match with one value.<br>@param option The option to test.<br>@param value The value to test against.<br>@returns |
| **limitTags** | `number` | `-1` | The maximum number of tags that will be visible when not focused.<br>Set `-1` to disable the limit. |
| **loading** | ``false ⏐ true`` | `false` | If `true`, the component is in a loading state.<br>This shows the `loadingText` in place of suggestions (only if there are no suggestions to show, for example `options` are empty). |
| **loadingText** | `ReactNode` | `'Loading…'` | Text to display when in a loading state. |
| **multiple** | ``false ⏐ true`` | `false` | If `true`, `value` must be an array and the menu will support multiple selections. |
| **noOptionsText** | `ReactNode` | `'No options'` | Text to display when there are no options. |
| **onChange** | ``(event: SyntheticEvent<Element, Event>, value: AutocompleteValue<T, Multiple, DisableClearable, FreeSolo>, reason: AutocompleteChangeReason, details?: AutocompleteChangeDetails<...> ⏐ undefined) => void`` | - | Callback fired when the value changes.<br>@param event The event source of the callback.<br>@param value The new value of the component.<br>@param reason One of "createOption", "selectOption", "removeOption", "blur" or "clear".<br>@param details |
| **onClose** | ``(event: SyntheticEvent<Element, Event>, reason: AutocompleteChangeReason) => void`` | - | Callback fired when the popup requests to be closed.<br>Use in controlled mode (see open).<br>@param event The event source of the callback.<br>@param reason Can be: `"toggleInput"`, `"escape"`, `"selectOption"`, `"removeOption"`, `"blur"`. |
| **onHighlightChange** | ``(event: SyntheticEvent<Element, Event>, option: T ⏐ null, reason: AutocompleteHighlightChangeReason) => void`` | - | Callback fired when the highlight option changes.<br>@param event The event source of the callback.<br>@param option The highlighted option.<br>@param reason Can be: `"keyboard"`, `"mouse"`, `"touch"`. |
| **onInputChange** | ``(event: SyntheticEvent<Element, Event>, value: string, reason: AutocompleteInputChangeReason) => void`` | - | Callback fired when the input value changes.<br>@param event The event source of the callback.<br>@param value The new value of the text input.<br>@param reason Can be: `"input"` (user input), `"reset"` (programmatic change), `"clear"`, `"blur"`, `"selectOption"`, `"removeOption"` |
| **onKeyDown** | ``(event: KeyboardEvent<HTMLDivElement> & { defaultMuiPrevented?: boolean ⏐ undefined; }) => void`` | - |  |
| **onOpen** | ``(event: SyntheticEvent<Element, Event>) => void`` | - | Callback fired when the popup requests to be opened.<br>Use in controlled mode (see open).<br>@param event The event source of the callback. |
| **open** | ``false ⏐ true`` | - | If `true`, the component is shown. |
| **openOnFocus** | ``false ⏐ true`` | `false` | If `true`, the popup will open on input focus. |
| **openText** | `string` | `'Open'` | Override the default text for the *open popup* icon button. |
| **popupIcon** | `ReactNode` | `<KeyboardArrowDown />` | The icon to display in place of the default popup icon. |
| **readOnly** | ``false ⏐ true`` | `false` | If `true`, the component becomes readonly. It is also supported for multiple tags where the tag cannot be deleted. |
| **renderGroup** | ``((params: AutocompleteRenderGroupParams) => ReactNode)`` | - | Render the group.<br>@param params The group to render.<br>@returns |
| **renderOption** | ``((props: HTMLAttributes<HTMLLIElement> & { key: string ⏐ number; }, option: T, state: AutocompleteRenderOptionState, ownerState: AutocompleteOwnerState<...>) => ReactNode)`` | - | Render the option, use `getOptionLabel` by default.<br>@param props The props to apply on the li element.<br>@param option The option to render.<br>@param state The state of each option.<br>@param ownerState The state of the Autocomplete component.<br>@returns |
| **renderValue** | ``((value: AutocompleteValue<T, Multiple, DisableClearable, FreeSolo>, getItemProps: AutocompleteRenderValueGetItemProps<Multiple>, ownerState: AutocompleteOwnerState<...>) => ReactNode)`` | - | Renders the selected value(s) as rich content in the input for both single and multiple selections.<br>@param value The `value` provided to the component.<br>@param getItemProps The value item props.<br>@param ownerState The state of the Autocomplete component.<br>@returns |
| **selectOnFocus** | ``false ⏐ true`` | `!props.freeSolo` | If `true`, the input's text is selected on focus.<br>It helps the user clear the selected value. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, AutocompleteOwnerState<T, Multiple, DisableClearable, FreeSolo>> ⏐ undefined; ... 8 more ...; chip?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `AutocompleteSlots` | `{}` | The components used for each slot inside. |
| **unstable_classNamePrefix** | `string` | `'Nova'` | @internal The prefix of the state class name |
| **unstable_isActiveElementInListbox** | ``((listbox: RefObject<HTMLElement ⏐ null>) => boolean)`` | - | @param listbox The ref of the listbox<br>@returns true if the active element is in the listbox |
| **value** | ``AutocompleteValue<T, Multiple, DisableClearable, FreeSolo>`` | - | The value of the autocomplete.<br>The value must have reference equality with the option in order to be selected.<br>You can customize the equality behavior with the `isOptionEqualToValue` prop. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Autocomplete` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAutocomplete-root | `'div'` | The component that renders the root. |
| listbox | .NovaAutocomplete-listbox | `'ul'` | The component that renders the listbox. |
| option | .NovaAutocomplete-option | `'li'` | The component that renders the option. |
| groupLabel | .NovaAutocomplete-groupLabel | `'li'` | The component that renders the group label. |
| clearIndicator | .NovaAutocomplete-clearIndicator | `'button'` | The component that renders the clear indicator. |
| popupIndicator | .NovaAutocomplete-popupIndicator | `'button'` | The component that renders the popup indicator. |
| loading | .NovaAutocomplete-loading | `'li'` | The component that renders the loading. |
| noOptions | .NovaAutocomplete-noOptions | `'li'` | The component that renders the no-options. |
| limitTag | .NovaAutocomplete-limitTag | `'span'` | The component that renders the limit tag. |
| chip | .NovaAutocomplete-chip | `'div'` | The component that renders the chip. |

## CSS classes

CSS classes for different states and variations of the `Autocomplete` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaAutocomplete-fullWidth | `fullWidth` | Class name applied to the root element if `fullWidth={true}`. |
| .Nova-expanded | `expanded` | State class applied to the root element if the listbox is displayed. |
| .NovaAutocomplete-filled | `filled` | Class name applied to the root element if the input is filled. |
| .Nova-focused | `focused` | State class applied to the root element if focused. |
| .Nova-focusVisible | `focusVisible` | Class name applied to the option elements if they are keyboard focused. |
| .NovaAutocomplete-multiple | `multiple` | State class applied to the root element if `multiple={true}`. |
| .NovaAutocomplete-tag | `tag` | Class name applied to the tag elements, for example the chips. |
| .NovaAutocomplete-tagSizeSmall | `tagSizeSmall` | Class name applied to the tag elements, for example the chips if `size="small"`. |
| .NovaAutocomplete-tagSizeMedium | `tagSizeMedium` | Class name applied to the tag elements, for example the chips if `size="medium"`. |
| .NovaAutocomplete-tagSizeLarge | `tagSizeLarge` | Class name applied to the tag elements, for example the chips if `size="large"`. |
| .NovaAutocomplete-hasPopupIcon | `hasPopupIcon` | Class name applied when the popup icon is rendered. |
| .NovaAutocomplete-hasClearIcon | `hasClearIcon` | Class name applied when the clear icon is rendered. |
| .NovaAutocomplete-inputRoot | `inputRoot` | Class name applied to the Input element. |
| .NovaAutocomplete-endAdornment | `endAdornment` | Class name applied to the endAdornment element. |
| .NovaAutocomplete-popupIndicatorOpen | `popupIndicatorOpen` | Class name applied to the popup indicator if the popup is open. |

