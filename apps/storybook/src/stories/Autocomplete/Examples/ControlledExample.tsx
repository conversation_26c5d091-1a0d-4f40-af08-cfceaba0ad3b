import React from 'react';
import { Autocomplete } from '@hxnova/react-components/Autocomplete';
import { TextField } from '@hxnova/react-components/TextField';

const options = ['Option 1', 'Option 2'];

export default function Demo() {
  const [value, setValue] = React.useState<string | null>(options[0]);
  const [inputValue, setInputValue] = React.useState('');
  return (
    <div>
      <div>{`value: ${value !== null ? `'${value}'` : 'null'}`}</div>
      <div>{`inputValue: '${inputValue}'`}</div>
      <br />
      <Autocomplete
        sx={{ minWidth: '400px' }}
        value={value}
        onChange={(_event, newValue) => {
          setValue(newValue);
        }}
        inputValue={inputValue}
        onInputChange={(_event, newInputValue) => {
          setInputValue(newInputValue);
        }}
        id="controllable-states-demo"
        options={options}
        renderInput={(params) => <TextField {...params} label="Controllable" placeholder="Placeholder" />}
      />
    </div>
  );
}
