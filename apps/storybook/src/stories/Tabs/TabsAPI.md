# API Documentation

- [Tabs.Tab](#tabstab)
- [Tabs.Indicator](#tabsindicator)
- [Tabs.Panel](#tabspanel)
- [Tabs.List](#tabslist)
- [Tabs.Root](#tabsroot)

# Tabs.Tab

API reference docs for the React Tabs.Tab component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Tabs.Tab` component, you can choose to import it directly or through the main entry point.

```jsx
import { Tabs } from '@hxnova/react-components/Tabs';
// or
import { Tabs } from '@hxnova/react-components';

// usage
<Tabs.Tab>{children}</Tabs.Tab>
```

## Props

The properties available for the `Tabs.Tab` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **className** | ``string ⏐ (state: State) => string`` | - | CSS class applied to the element, or a function that<br>returns a class based on the component’s state. |
| **icon** | `ReactNode` | - | Icon to display in the tab |
| **iconPosition** | ``"top" ⏐ "side"`` | - | Icon position relative to the label |
| **render** | ``ReactElement<Record<string, unknown>, string ⏐ JSXElementConstructor<any>> ⏐ ComponentRenderFn<GenericHTMLProps, State>`` | - | Allows you to replace the component’s HTML element<br>with a different tag, or compose it with another component.<br>Accepts a `ReactElement` or a function that returns the element to render. |

## CSS classes

CSS classes for different states and variations of the `Tabs.Tab` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaTab-root | `root` | Class name applied to the root element. |
| .NovaTab-iconPositionSide | `iconPositionSide` | Class name applied when `iconPosition="side"`. |
| .NovaTab-iconPositionTop | `iconPositionTop` | Class name applied when `iconPosition="top"`. |

<br><br>

# Tabs.Indicator

API reference docs for the React Tabs.Indicator component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Tabs.Indicator` component, you can choose to import it directly or through the main entry point.

```jsx
import { Tabs } from '@hxnova/react-components/Tabs';
// or
import { Tabs } from '@hxnova/react-components';

// usage
<Tabs.Indicator>{children}</Tabs.Indicator>
```

## Props

The properties available for the `Tabs.Indicator` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **className** | ``string ⏐ (state: State) => string`` | - | CSS class applied to the element, or a function that<br>returns a class based on the component’s state. |
| **render** | ``ReactElement<Record<string, unknown>, string ⏐ JSXElementConstructor<any>> ⏐ ComponentRenderFn<GenericHTMLProps, State>`` | - | Allows you to replace the component’s HTML element<br>with a different tag, or compose it with another component.<br>Accepts a `ReactElement` or a function that returns the element to render. |

## CSS classes

CSS classes for different states and variations of the `Tabs.Indicator` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaTabIndicator-root | `root` | Class name applied to the root element. |

<br><br>

# Tabs.Panel

API reference docs for the React Tabs.Panel component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Tabs.Panel` component, you can choose to import it directly or through the main entry point.

```jsx
import { Tabs } from '@hxnova/react-components/Tabs';
// or
import { Tabs } from '@hxnova/react-components';

// usage
<Tabs.Panel>{children}</Tabs.Panel>
```

## Props

The properties available for the `Tabs.Panel` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **className** | ``string ⏐ (state: State) => string`` | - | CSS class applied to the element, or a function that<br>returns a class based on the component’s state. |
| **render** | ``ReactElement<Record<string, unknown>, string ⏐ JSXElementConstructor<any>> ⏐ ComponentRenderFn<GenericHTMLProps, State>`` | - | Allows you to replace the component’s HTML element<br>with a different tag, or compose it with another component.<br>Accepts a `ReactElement` or a function that returns the element to render. |

## CSS classes

CSS classes for different states and variations of the `Tabs.Panel` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaTabPanel-root | `root` | Class name applied to the root element. |

<br><br>

# Tabs.List

API reference docs for the React Tabs.List component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Tabs.List` component, you can choose to import it directly or through the main entry point.

```jsx
import { Tabs } from '@hxnova/react-components/Tabs';
// or
import { Tabs } from '@hxnova/react-components';

// usage
<Tabs.List>{children}</Tabs.List>
```

## Props

The properties available for the `Tabs.List` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **className** | ``string ⏐ (state: State) => string`` | - | CSS class applied to the element, or a function that<br>returns a class based on the component’s state. |
| **divider** | ``false ⏐ true`` | - | If true, shows a divider between tabs and main content. |
| **render** | ``ReactElement<Record<string, unknown>, string ⏐ JSXElementConstructor<any>> ⏐ ComponentRenderFn<GenericHTMLProps, State>`` | - | Allows you to replace the component’s HTML element<br>with a different tag, or compose it with another component.<br>Accepts a `ReactElement` or a function that returns the element to render. |

## CSS classes

CSS classes for different states and variations of the `Tabs.List` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaTabsList-root | `root` | Class name applied to the root element. |
| .NovaTabsList-divider | `divider` | Class name applied when `divider="true"`. |

<br><br>

# Tabs.Root

API reference docs for the React Tabs.Root component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Tabs.Root` component, you can choose to import it directly or through the main entry point.

```jsx
import { Tabs } from '@hxnova/react-components/Tabs';
// or
import { Tabs } from '@hxnova/react-components';

// usage
<Tabs.Root>{children}</Tabs.Root>
```

## Props

The properties available for the `Tabs.Root` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **className** | ``string ⏐ (state: State) => string`` | - | CSS class applied to the element, or a function that<br>returns a class based on the component’s state. |
| **render** | ``ReactElement<Record<string, unknown>, string ⏐ JSXElementConstructor<any>> ⏐ ComponentRenderFn<GenericHTMLProps, State>`` | - | Allows you to replace the component’s HTML element<br>with a different tag, or compose it with another component.<br>Accepts a `ReactElement` or a function that returns the element to render. |

## CSS classes

CSS classes for different states and variations of the `Tabs.Root` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaTabsRoot-root | `root` | Class name applied to the root element. |

