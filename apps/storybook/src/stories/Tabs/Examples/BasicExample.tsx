import { Tabs } from '@hxnova/react-components/Tabs';

export default function Demo() {
  return (
    <Tabs.Root defaultValue={1}>
      <Tabs.List>
        <Tabs.Tab value={1}>Tab 1</Tabs.Tab>
        <Tabs.Tab value={2}>Tab 2</Tabs.Tab>
        <Tabs.Tab value={3}>Tab 3</Tabs.Tab>
        <Tabs.Tab value={4}>Tab 4</Tabs.Tab>
        <Tabs.Indicator />
      </Tabs.List>
      <Tabs.Panel value={1} sx={{ padding: 16 }}>
        Tab 1 Content
      </Tabs.Panel>
      <Tabs.Panel value={2} sx={{ padding: 16 }}>
        Tab 2 Content
      </Tabs.Panel>
      <Tabs.Panel value={3} sx={{ padding: 16 }}>
        Tab 3 Content
      </Tabs.Panel>
      <Tabs.Panel value={4} sx={{ padding: 16 }}>
        Tab 4 Content
      </Tabs.Panel>
    </Tabs.Root>
  );
}
