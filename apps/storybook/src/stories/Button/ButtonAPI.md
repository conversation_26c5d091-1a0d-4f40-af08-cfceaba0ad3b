# API Documentation

- [But<PERSON>](#button)

# Button

API reference docs for the React Button component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Button` component, you can choose to import it directly or through the main entry point.

```jsx
import { Button } from '@hxnova/react-components/Button';
// or
import { Button } from '@hxnova/react-components';
```

## Props

The properties available for the `Button` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``Ref<ButtonActions>`` | - | A ref for imperative actions. It currently only supports `focusVisible()` action. |
| **className** | `string` | - |  |
| **color** | ``"primary" ⏐ "error"`` | `'primary'` | The color scheme used by the Button |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **endIcon** | `ReactNode` | - | An optional icon to show at the end of the button |
| **focusableWhenDisabled** | ``false ⏐ true`` | `false` | If `true`, allows a disabled button to receive focus. |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the button will take up the full width of its container. |
| **href** | `string` | - |  |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **rootElementName** | `keyof HTMLElementTagNameMap` | `'button'` | The HTML element that is ultimately rendered, for example 'button' or 'a' |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The overall size of the button. |
| **slotProps** | ``{ root?: SlotComponentProps<"button", ButtonRootSlotPropsOverrides, { action?: Ref<ButtonActions>; children?: ReactNode; ... 13 more ...; focusVisible: boolean; }> ⏐ undefined; } ⏐ undefined`` | `{}` | The props used for each slot inside the Button. |
| **slots** | `ButtonSlots` | `{}` | The components used for each slot inside the Button.<br>Either a string to use a HTML element or a component. |
| **startIcon** | `ReactNode` | - | An optional icon to show at the start of the button |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **tabIndex** | `number` | - |  |
| **to** | `string` | - |  |
| **type** | ``"button" ⏐ "submit" ⏐ "reset"`` | `'button'` | Type attribute applied when the `component` is `button`. |
| **variant** | ``"text" ⏐ "filled" ⏐ "outlined"`` | `'filled'` | The style of button to use |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Button` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaButton-root | ``props.href ⏐⏐ props.to ? 'a' : 'button'`` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `Button` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaButton-colorPrimary | `colorPrimary` | Class name applied to the Button if `color="primary"`. |
| .NovaButton-colorError | `colorError` | Class name applied to the Button if `color="error"`. |
| .NovaButton-filled | `filled` | Class name applied to the Button if `variant="filled"`. |
| .NovaButton-outlined | `outlined` | Class name applied to the Button if `variant="outlined"`. |
| .NovaButton-text | `text` | Class name applied to the Button if `variant="text"`. |

