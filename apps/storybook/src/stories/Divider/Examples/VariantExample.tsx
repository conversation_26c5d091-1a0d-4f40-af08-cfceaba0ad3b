import React from 'react';
import { Divider } from '@hxnova/react-components/Divider';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';

const style = {
  py: 0,
  maxWidth: 360,
  borderRadius: '8px',
  border: '1px solid',
  borderColor: 'var(--palette-outline)',
  backgroundColor: 'var(--palette-surfaceContainer)',
};

export default function DividerVariants() {
  return (
    <div sx={style}>
      <List>
        <ListItem>
          <ListItemButton>
            <ListItemContent primary="Full width variant below" />
          </ListItemButton>
        </ListItem>
        <Divider component="li" />
        <ListItem>
          <ListItemButton>
            <ListItemContent primary="Inset variant below" />
          </ListItemButton>
        </ListItem>
        <Divider variant="inset" component="li" />
        <ListItem>
          <ListItemButton>
            <ListItemContent primary="List item" />
          </ListItemButton>
        </ListItem>
      </List>
    </div>
  );
}
