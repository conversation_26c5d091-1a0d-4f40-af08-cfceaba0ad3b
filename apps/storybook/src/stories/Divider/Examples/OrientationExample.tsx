import * as React from 'react';
import Icon from '@hxnova/icons/Icon';
import { Divider, dividerClasses } from '@hxnova/react-components/Divider';

export default function VerticalDividers() {
  return (
    <div
      sx={{
        display: 'flex',
        alignItems: 'center',
        border: '1px solid',
        borderColor: 'var(--palette-outline)',
        borderRadius: '8px',
        backgroundColor: 'var(--palette-surfaceContainer)',
        color: 'var(--palette-onSurfaceVariant)',
        width: 'fit-content',

        [`& .${dividerClasses.root}`]: {
          marginInline: 0.5,
        },
      }}
    >
      <Icon family="material" name="format_align_left" sx={{ padding: '16px' }} />
      <Divider orientation="vertical" />
      <Icon family="material" name="format_align_center" sx={{ padding: '16px' }} />
      <Divider orientation="vertical" />
      <Icon family="material" name="format_align_right" sx={{ padding: '16px' }} />
      <Divider orientation="vertical" />
      <Icon family="material" name="format_bold" sx={{ padding: '16px' }} />
    </div>
  );
}
