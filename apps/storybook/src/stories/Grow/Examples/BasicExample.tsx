import { useState } from 'react';
import { Grow } from '@hxnova/react-components/Grow';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function BasicExample() {
  const [checked, setChecked] = useState(false);

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
          Basic Grow Transition
        </Typography>
        <div sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
          <Switch checked={checked} onChange={() => setChecked((prev) => !prev)} endDecorator="Toggle Content" />
          <Grow in={checked} timeout="auto">
            <Box
              sx={{
                padding: '8px',
                border: '1px solid var(--palette-outlineVariant)',
                borderRadius: 'var(--radius-xs)',
                backgroundColor: 'var(--palette-surfaceContainer)',
                width: 300,
                textAlign: 'center',
              }}
            >
              <Typography variant="bodyMedium">
                This content grows in and out smoothly with scale and opacity animations.
              </Typography>
            </Box>
          </Grow>
        </div>
      </div>

      <div>
        <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
          Different Timing Options
        </Typography>
        <div sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <div sx={{ display: 'flex', justifyContent: 'space-between', gap: '16px' }}>
            <div sx={{ flex: 1, textAlign: 'center' }}>
              <Typography variant="bodySmall" sx={{ marginBottom: '8px' }}>
                Fast (150ms)
              </Typography>
              <Grow in={checked} timeout={150}>
                <Box
                  sx={{
                    padding: '8px',
                    backgroundColor: 'var(--palette-inversePrimary)',
                    border: '1px solid var(--palette-outline)',
                    borderRadius: 'var(--radius-2xs)',
                    height: 60,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="bodySmall">Fast</Typography>
                </Box>
              </Grow>
            </div>
            <div sx={{ flex: 1, textAlign: 'center' }}>
              <Typography variant="bodySmall" sx={{ marginBottom: '8px' }}>
                Auto Duration
              </Typography>
              <Grow in={checked} timeout="auto">
                <Box
                  sx={{
                    padding: '8px',
                    backgroundColor: 'var(--palette-secondaryContainer)',
                    border: '1px solid var(--palette-outline)',
                    borderRadius: 'var(--radius-2xs)',
                    height: 60,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="bodySmall">Auto</Typography>
                </Box>
              </Grow>
            </div>
            <div sx={{ flex: 1, textAlign: 'center' }}>
              <Typography variant="bodySmall" sx={{ marginBottom: '8px' }}>
                Slow (800ms)
              </Typography>
              <Grow in={checked} timeout={800}>
                <Box
                  sx={{
                    padding: '8px',
                    backgroundColor: 'var(--palette-surfaceContainer)',
                    border: '1px solid var(--palette-outline)',
                    borderRadius: 'var(--radius-2xs)',
                    height: 60,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="bodySmall">Slow</Typography>
                </Box>
              </Grow>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
