import { useState } from 'react';
import { Grow } from '@hxnova/react-components/Grow';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Button } from '@hxnova/react-components/Button';
import { Card } from '@hxnova/react-components/Card';

export default function InteractiveExample() {
  const [showCards, setShowCards] = useState(false);
  const [selectedCard, setSelectedCard] = useState<number | null>(null);

  const cards = [
    { id: 1, title: 'Card 1', description: 'This is the first card with some content.' },
    { id: 2, title: 'Card 2', description: 'This is the second card with different content.' },
    { id: 3, title: 'Card 3', description: 'This is the third card with more content.' },
  ];

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px', maxWidth: '800px' }}>
      <div>
        <Typography variant="titleMedium" sx={{ marginBottom: '16px' }}>
          Interactive Grow Example
        </Typography>
        <Typography variant="bodyMedium" sx={{ marginBottom: '16px', color: 'var(--palette-onSurfaceVariant)' }}>
          Toggle the cards below and click on individual cards to see staggered animations.
        </Typography>

        <div sx={{ display: 'flex', alignItems: 'center', marginBottom: '24px' }}>
          <Switch
            checked={showCards}
            onChange={() => {
              setShowCards((prev) => !prev);
              if (!showCards) setSelectedCard(null);
            }}
            endDecorator="Show Cards"
          />

          {showCards && (
            <Button variant="outlined" sx={{ marginLeft: '100px' }} size="small" onClick={() => setSelectedCard(null)}>
              Reset Selection
            </Button>
          )}
        </div>

        <div sx={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
          {cards.map((card, index) => (
            <Grow
              key={card.id}
              in={showCards}
              timeout="auto"
              sx={{ transitionDelay: showCards ? `${index * 100}ms` : '0ms' }}
            >
              <Card.Root
                style={{
                  width: 200,
                  cursor: 'pointer',
                  transition: 'transform 0.2s ease-in-out',
                  transform: selectedCard === card.id ? 'scale(1.05)' : 'scale(1)',
                  border:
                    selectedCard === card.id
                      ? '2px solid var(--palette-primary)'
                      : '1px solid var(--palette-outlineVariant)',
                }}
                onClick={() => setSelectedCard(selectedCard === card.id ? null : card.id)}
              >
                <Card.Content title={card.title} supportingText={card.description} />
              </Card.Root>
            </Grow>
          ))}
        </div>

        {selectedCard && (
          <Grow in={!!selectedCard} timeout={300}>
            <Box
              sx={{
                marginTop: '24px',
                padding: '16px',
                backgroundColor: 'var(--palette-primaryContainer)',
                borderRadius: 'var(--radius-sm)',
                border: '1px solid var(--palette-primary)',
                color: 'var(--palette-onPrimaryContainer)',
              }}
            >
              <Typography variant="titleSmall" sx={{ marginBottom: '8px' }}>
                Selected Card Details
              </Typography>
              <Typography variant="bodyMedium">
                You have selected {cards.find((card) => card.id === selectedCard)?.title}. This detail panel also uses
                the Grow transition to appear smoothly.
              </Typography>
            </Box>
          </Grow>
        )}
      </div>

      <div>
        <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
          Nested Grow Animations
        </Typography>

        <Grow in={showCards} timeout="auto">
          <Box
            sx={{
              padding: '24px',
              backgroundColor: 'var(--palette-surfaceContainer)',
              borderRadius: 'var(--radius-md)',
              border: '1px solid var(--palette-outlineVariant)',
            }}
          >
            <Typography variant="bodyMedium" sx={{ marginBottom: '16px' }}>
              This container grows in first, then its children animate in sequence.
            </Typography>

            <div sx={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
              {[1, 2, 3, 4].map((item, index) => (
                <Grow
                  key={item}
                  in={showCards}
                  timeout={200}
                  style={{ transitionDelay: showCards ? `${(index + 2) * 150}ms` : '0ms' }}
                >
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      backgroundColor: 'var(--palette-secondary)',
                      borderRadius: 'var(--radius-sm)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="bodySmall" sx={{ color: 'var(--palette-onSecondary)' }}>
                      {item}
                    </Typography>
                  </Box>
                </Grow>
              ))}
            </div>
          </Box>
        </Grow>
      </div>
    </div>
  );
}
