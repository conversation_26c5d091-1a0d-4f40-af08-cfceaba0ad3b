# API Documentation

- [Grow](#grow)

# Grow

API reference docs for the React Grow component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Grow` component, you can choose to import it directly or through the main entry point.

```jsx
import { Grow } from '@hxnova/react-components/Grow';
// or
import { Grow } from '@hxnova/react-components';
```

## Props

The properties available for the `Grow` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **appear** | ``false ⏐ true`` | `true` | Perform the enter transition when it first mounts if `in` is also `true`.<br>Set this to `false` to disable this behavior.<br>Normally a component is not transitioned if it is shown when the<br>`<Transition>` component mounts. If you want to transition on the first<br>mount set  appear to true, and the component will transition in as soon<br>as the `<Transition>` mounts. Note: there are no specific "appear" states.<br>appear only adds an additional enter transition. |
| **children** | ``ReactElement<unknown, any> & ReactNode`` | - | A single child content element. |
| **easing** | ``string ⏐ { enter?: string ⏐ undefined; exit?: string ⏐ undefined; }`` | - | The transition timing function.<br>You may specify a single easing or a object containing enter and exit values. |
| **in** | ``false ⏐ true`` | - | If `true`, the component will transition in.<br>Show the component; triggers the enter or exit states |
| **ref** | ``Ref<unknown>`` | - |  |
| **timeout** | ``number ⏐ { appear?: number ⏐ undefined; enter?: number ⏐ undefined; exit?: number ⏐ undefined; } ⏐ { appear?: number ⏐ undefined; enter?: number ⏐ undefined; exit?: number ⏐ undefined; } ⏐ "auto"`` | `'auto'` | The duration for the transition, in milliseconds.<br>You may specify a single timeout for all transitions, or individually with an object.<br>Set to 'auto' to automatically calculate transition time based on height. |
| **TransitionComponent** | ``ComponentClass<any, any> ⏐ FunctionComponent<any>`` | `Transition` | The component used for the transition. |

