import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import { Grow } from '@hxnova/react-components/Grow';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import InteractiveExample from './Examples/InteractiveExample';
import InteractiveExampleSource from './Examples/InteractiveExample.tsx?raw';

<Meta title="@hxnova/react-components/Transitions/Grow/Examples" />

## Grow Transition

The Grow component provides smooth scale and opacity transitions for showing and hiding content. It uses the **`in` prop** to control animations that combine both opacity (from 0 to 1) and scale (from 0.75 to 1) to create a growing effect. The component is built on top of `react-transition-group` and provides automatic duration calculation with the **`timeout="auto"` prop** based on content height.

### Basic Usage

The Grow component animates both the opacity and scale of its child element using the **`in` prop**. Use the **`timeout` prop** to control animation duration - set it to `"auto"` for automatic duration calculation based on the element's height, or specify a number in milliseconds (e.g., `timeout={300}` for a 300ms animation).

<div className="doc-story sb-story sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

### Interactive Example

Here's a more complex example showing multiple Grow components working together with staggered animations:

<div className="doc-story sb-story sb-unstyled">
  <InteractiveExample />
</div>
<CodeExpand code={InteractiveExampleSource} showBorderTop style={{marginTop: 16}}/>
