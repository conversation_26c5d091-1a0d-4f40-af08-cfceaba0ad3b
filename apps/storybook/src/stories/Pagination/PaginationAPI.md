# API Documentation

- [Pagination](#pagination)
- [PaginationItem](#paginationitem)
- [TablePagination](#tablepagination)
- [TablePaginationActions](#tablepaginationactions)

# Pagination

API reference docs for the React Pagination component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Pagination` component, you can choose to import it directly or through the main entry point.

```jsx
import { Pagination } from '@hxnova/react-components/Pagination';
// or
import { Pagination } from '@hxnova/react-components';
```

## Props

The properties available for the `Pagination` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **boundaryCount** | `number` | `1` | Number of always visible pages at the beginning and end. |
| **count** | `number` | `1` | The total number of pages. |
| **defaultPage** | `number` | `1` | The page selected by default when the component is uncontrolled. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **getItemAriaLabel** | ``(type: "page" ⏐ "next" ⏐ "previous" ⏐ "first" ⏐ "last" ⏐ "start-ellipsis" ⏐ "end-ellipsis", page: number ⏐ null, selected: boolean) => string`` | - | Accepts a function which returns a string value that provides a user-friendly name for the current page.<br>This is important for screen reader users.<br>@param type The link or button type to format ('page' | 'first' | 'last' | 'next' | 'previous' | 'start-ellipsis' | 'end-ellipsis'). Defaults to 'page'.<br>@param page The page number to format.<br>@param selected If true, the current page is selected.<br>@returns |
| **hideNextButton** | ``false ⏐ true`` | `false` | If `true`, hide the next-page button. |
| **hidePrevButton** | ``false ⏐ true`` | `false` | If `true`, hide the previous-page button. |
| **onChange** | ``(event: ChangeEvent<unknown>, page: number) => void`` | - | Callback fired when the page is changed.<br>@param event The event source of the callback.<br>@param page The page selected. |
| **page** | `number` | - | The current page. Unlike `TablePagination`, which starts numbering from `0`, this pagination starts from `1`. |
| **renderItem** | ``((params: PaginationRenderItemParams) => ReactNode)`` | `(item) => <PaginationItem {...item} />` | Render the item.<br>@param params The props to spread on a PaginationItem.<br>@returns |
| **showFirstButton** | ``false ⏐ true`` | `false` | If `true`, show the first-page button. |
| **showLastButton** | ``false ⏐ true`` | `false` | If `true`, show the last-page button. |
| **siblingCount** | `number` | `1` | Number of always visible pages before and after the current page. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |

## CSS classes

CSS classes for different states and variations of the `Pagination` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaPagination-root | `root` | Class name applied to the root element. |
| .NovaPagination-ul | `ul` | Class name applied to the ul element. |

<br><br>

# PaginationItem

API reference docs for the React PaginationItem component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `PaginationItem` component, you can choose to import it directly or through the main entry point.

```jsx
import { PaginationItem } from '@hxnova/react-components/PaginationItem';
// or
import { PaginationItem } from '@hxnova/react-components';
```

## Props

The properties available for the `PaginationItem` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **page** | `ReactNode` | - | The current page number. |
| **selected** | ``false ⏐ true`` | `false` | If `true` the pagination item is selected. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | `ElementType` | `{}` | The props used for each slot inside. |
| **slots** | `PaginationItemSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **type** | ``"page" ⏐ "first" ⏐ "last" ⏐ "next" ⏐ "previous" ⏐ "start-ellipsis" ⏐ "end-ellipsis"`` | `'page'` | The type of pagination item. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `PaginationItem` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| first | .NovaPaginationItem-first | `'div'` |  |
| last | .NovaPaginationItem-last | `'div'` |  |
| next | .NovaPaginationItem-next | `'div'` |  |
| previous | .NovaPaginationItem-previous | `'div'` |  |

## CSS classes

CSS classes for different states and variations of the `PaginationItem` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaPaginationItem-root | `root` | Class name applied to the root element. |
| .NovaPaginationItem-page | `page` | Class name applied to the root element if `type="page"`. |
| .NovaPaginationItem-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaPaginationItem-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |
| .NovaPaginationItem-ellipsis | `ellipsis` | Class name applied to the root element if `type="start-ellipsis"` or `type="end-ellipsis"`. |
| .NovaPaginationItem-firstLast | `firstLast` | Class name applied to the root element if `type="first"` or type="last". |
| .NovaPaginationItem-previousNext | `previousNext` | Class name applied to the root element if `type="previous"` or type="next". |
| .Nova-focusVisible | `focusVisible` | State class applied to the root element if keyboard focused. |
| .Nova-disabled | `disabled` | State class applied to the root element if `disabled={true}`. |
| .Nova-selected | `selected` | State class applied to the root element if `selected={true}`. |
| .NovaPaginationItem-icon | `icon` | Class name applied to the icon to display. |

<br><br>

# TablePagination

API reference docs for the React TablePagination component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `TablePagination` component, you can choose to import it directly or through the main entry point.

```jsx
import { TablePagination } from '@hxnova/react-components/TablePagination';
// or
import { TablePagination } from '@hxnova/react-components';
```

## Props

The properties available for the `TablePagination` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **count*** | `number` | - | The total number of rows.<br>To enable server side pagination for an unknown number of items, provide -1. |
| **onPageChange*** | ``(event: MouseEvent<HTMLButtonElement, MouseEvent> ⏐ null, page: number) => void`` | - | Callback fired when the page is changed.<br>@param event The event source of the callback.<br>@param page The dropdown page. |
| **page*** | `number` | - | The zero-based index of the current page. |
| **rowsPerPage*** | `number` | - | The number of rows per page.<br>Set -1 to display all the rows. |
| **ActionsComponent** | `ElementType` | `TablePaginationActions` | The component used for displaying the actions.<br>Either a string to use a HTML element or a component. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **getItemAriaLabel** | ``(type: "next" ⏐ "previous" ⏐ "first" ⏐ "last") => string`` | `function defaultGetAriaLabel(type) { return \`Go to ${type} page\`; }` | Accepts a function which returns a string value that provides a user-friendly name for the current page.<br>This is important for screen reader users.<br>@param type The link or button type to format ('first' | 'last' | 'next' | 'previous').<br>@returns |
| **labelRowsPerPage** | `ReactNode` | `'Items per page:'` | Customize the rows per page label. |
| **onRowsPerPageChange** | ``(event: SyntheticEvent<Element, Event> ⏐ null, page: number) => void`` | - | Callback fired when the number of rows per page is changed.<br>@param event The event source of the callback. |
| **renderPageInfo** | ``((page: number, totalPages: number) => ReactNode)`` | `function renderPageInfo(page, totalPages) { return \`Page ${page + 1} of ${totalPages}\`; }` | Custom render function for page info content<br>@param page Current page number (0-based)<br>@param totalPages Total number of pages<br>@returns |
| **rowsPerPageOptions** | ``readonly (number ⏐ { value: number; label: string; })[]`` | `[10, 25, 50, 100]` | Customizes the options of the rows per page dropdown field. If less than two options are<br>available, no dropdown field will be displayed.<br>Use -1 for the value with a custom label to show all the rows. |
| **showFirstButton** | ``false ⏐ true`` | `false` | If `true`, show the first-page button. |
| **showLastButton** | ``false ⏐ true`` | `false` | If `true`, show the last-page button. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, TablePaginationOwnerState> ⏐ undefined; dropdownLabel?: SlotProps<"p", object, TablePaginationOwnerState> ⏐ undefined; dropdown?: DropdownProps<...> ⏐ undefined; dropdownOption?: OptionProps ⏐ undefined; actions?: { ...; } ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `TablePaginationSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `TablePagination` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaTablePagination-root | `'div'` | The component that renders the root slot. |
| dropdownLabel | .NovaTablePagination-dropdownLabel | `'p'` | The tag that renders the dropdownLabel slot. |
| dropdown | .NovaTablePagination-dropdown | `Dropdown` | The component that renders the dropdown slot. |
| dropdownOption | .NovaTablePagination-dropdownOption | `Option` | The component that renders the dropdown option slot. |
| actions | .NovaTablePagination-actions | `'div'` | The slots for actions component. |

## CSS classes

CSS classes for different states and variations of the `TablePagination` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaTablePagination-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaTablePagination-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaTablePagination-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled={true}`. |

<br><br>

# TablePaginationActions

API reference docs for the React TablePaginationActions component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `TablePaginationActions` component, you can choose to import it directly or through the main entry point.

```jsx
import { TablePaginationActions } from '@hxnova/react-components/TablePaginationActions';
// or
import { TablePaginationActions } from '@hxnova/react-components';
```

## Props

The properties available for the `TablePaginationActions` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **count*** | `number` | - | The total number of items. |
| **getItemAriaLabel*** | ``(type: "next" ⏐ "previous" ⏐ "first" ⏐ "last") => string`` | - | Accepts a function which returns a string value that provides a user-friendly name for the current page.<br>@param type The link or button type to format ('first' | 'last' | 'next' | 'previous').<br>@returns |
| **onPageChange*** | ``(event: MouseEvent<HTMLButtonElement, MouseEvent> ⏐ null, page: number) => void`` | - | Callback fired when the page is changed.<br>@param event The event source of the callback.<br>@param page The page selected. |
| **page*** | `number` | - | The current page. |
| **renderPageInfo*** | ``(page: number, totalPages: number) => ReactNode`` | - | Custom render function for page info content<br>@param page Current page number (0-based)<br>@param totalPages Total number of pages<br>@returns |
| **rowsPerPage*** | `number` | - | The number of rows per page. |
| **showFirstButton*** | ``false ⏐ true`` | - | If `true`, the first button is shown. |
| **showLastButton*** | ``false ⏐ true`` | - | If `true`, the last button is shown. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | ``{ firstButton?: Partial<IconButtonProps> ⏐ undefined; lastButton?: Partial<IconButtonProps> ⏐ undefined; nextButton?: Partial<...> ⏐ undefined; ... 4 more ...; previousButtonIcon?: Partial<...> ⏐ undefined; }`` | - | The props used for each slot. |
| **slots** | `TablePaginationActionsSlots` | - | The components used for each slot. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `TablePaginationActions` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| firstButton | .NovaTablePaginationActions-firstButton | `IconButton` | The component that renders the first button. |
| lastButton | .NovaTablePaginationActions-lastButton | `IconButton` | The component that renders the last button. |
| nextButton | .NovaTablePaginationActions-nextButton | `IconButton` | The component that renders the next button. |
| previousButton | .NovaTablePaginationActions-previousButton | `IconButton` | The component that renders the previous button. |
| firstButtonIcon | .NovaTablePaginationActions-firstButtonIcon | `FirstPageIcon` | The component that renders the first button icon. |
| lastButtonIcon | .NovaTablePaginationActions-lastButtonIcon | `LastPageIcon` | The component that renders the last button icon. |
| nextButtonIcon | .NovaTablePaginationActions-nextButtonIcon | `KeyboardArrowRight` | The component that renders the next button icon. |
| previousButtonIcon | .NovaTablePaginationActions-previousButtonIcon | `KeyboardArrowLeft` | The component that renders the previous button icon. |

