import * as React from 'react';
import { Pagination } from '@hxnova/react-components/Pagination';

export default function ControlledExample() {
  const [page, setPage] = React.useState(1);

  const handleChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <div>
        <h4>Controlled pagination (current page: {page})</h4>
        <Pagination count={10} page={page} onChange={handleChange} />
      </div>
    </div>
  );
}
