# API Documentation

- [Menu](#menu)
- [MenuItem](#menuitem)
- [MenuList](#menulist)

# Menu

API reference docs for the React Menu component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Menu` component, you can choose to import it directly or through the main entry point.

```jsx
import { Menu } from '@hxnova/react-components/Menu';
// or
import { Menu } from '@hxnova/react-components';
```

## Props

The properties available for the `Menu` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **anchorEl** | ``null ⏐ HTMLElement ⏐ VirtualElement ⏐ () => HTMLElement ⏐ () => VirtualElement`` | - | An HTML element, [virtualElement](https://popper.js.org/docs/v2/virtual-elements/),<br>or a function that returns either.<br>It's used to set the position of the popper.<br>The return value will passed as the reference object of the Popper instance. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **container** | ``null ⏐ Element ⏐ () => Element ⏐ null`` | - | An HTML element or function that returns one.<br>The `container` will have the portal children appended to it.<br>You can also provide a callback, which is called in a React layout effect.<br>This lets you set the container from a ref, and also makes server-side rendering possible.<br>By default, it uses the body of the top-level document object,<br>so it's simply `document.body` most of the time. |
| **density** | ``"compact" ⏐ "standard" ⏐ "comfortable"`` | `'standard'` | The `density` attribute for the MenuItem |
| **direction** | ``"ltr" ⏐ "rtl"`` | `'ltr'` | Direction of the text. |
| **disablePortal** | ``false ⏐ true`` | `false` | The `children` will be under the DOM hierarchy of the parent component. |
| **keepMounted** | ``false ⏐ true`` | `false` | Always keep the children in the DOM.<br>This prop can be useful in SEO situation or<br>when you want to maximize the responsiveness of the Popper. |
| **modifiers** | ``Partial<Modifier<any, any>>[]`` | - | Popper.js is based on a "plugin-like" architecture,<br>most of its features are fully encapsulated "modifiers".<br>A modifier is a function that is called each time Popper.js needs to<br>compute the position of the popper.<br>For this reason, modifiers should be very performant to avoid bottlenecks.<br>To learn how to create a modifier, [read the modifiers documentation](https://popper.js.org/docs/v2/modifiers/). |
| **onClose** | ``() => void`` | - | Triggered when focus leaves the menu and the menu should close. |
| **onItemsChange** | ``(items: string[]) => void`` | - | Function called when the items displayed in the menu change. |
| **open** | ``false ⏐ true`` | - | Whether the menu is currently open. |
| **placement** | ``"bottom" ⏐ "left" ⏐ "right" ⏐ "top" ⏐ "auto" ⏐ "auto-start" ⏐ "auto-end" ⏐ "top-start" ⏐ "top-end" ⏐ "bottom-start" ⏐ "bottom-end" ⏐ "right-start" ⏐ "right-end" ⏐ "left-start" ⏐ "left-end"`` | `'bottom'` | Popper placement. |
| **popperOptions** | ``Partial<OptionsGeneric<any>>`` | `object` | Options provided to the [`Popper.js`](https://popper.js.org/docs/v2/constructors/#options) instance. |
| **popperRef** | ``Ref<Instance>`` | - | A ref that points to the used popper instance. |
| **slotProps** | ``{ root?: SlotProps<"div", object, MenuOwnerState> ⏐ undefined; listbox?: SlotProps<"ul", object, MenuOwnerState> ⏐ undefined; } & { ...; }`` | `{} object` | The props used for each slot inside.<br>The props used for each slot inside the Popper. |
| **slots** | `MenuSlots & PopperSlots` | `{} object` | The components used for each slot inside.<br>The components used for each slot inside the Popper.<br>Either a string to use a HTML element or a component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **transition** | ``false ⏐ true`` | `false` | Help supporting a react-transition-group/Transition component. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Menu` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaMenu-root | `'div'` | The component that renders the root. |
| listbox | .NovaMenu-listbox | `'ul'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `Menu` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaMenu-densityStandard | `densityStandard` | Class name applied to the root element if `density="standard"`. |
| .NovaMenu-densityCompact | `densityCompact` | Class name applied to the root element if `density="compact"`. |
| .NovaMenu-densityComfortable | `densityComfortable` | Class name applied to the root element if `density="comfortable"`. |

<br><br>

# MenuItem

API reference docs for the React MenuItem component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `MenuItem` component, you can choose to import it directly or through the main entry point.

```jsx
import { MenuItem } from '@hxnova/react-components/MenuItem';
// or
import { MenuItem } from '@hxnova/react-components';
```

## Props

The properties available for the `MenuItem` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``Ref<{ focusVisible(): void; }>`` | - | A ref for imperative actions. It currently only supports `focusVisible()` action. |
| **autoFocus** | ``false ⏐ true`` | `false` | If `true`, the list item is focused during the first mount.<br>Focus will also be triggered if the value changes from false to true. |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **focusVisibleClassName** | `string` | - | This prop can help identify which element has keyboard focus.<br>The class name will be applied when the element gains the focus through keyboard interaction. |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'horizontal'` | The content direction flow. |
| **selected** | ``false ⏐ true`` | `false` | If `true`, the component is selected. |
| **slotProps** | ``{ root?: SlotProps<"div", object, MenuItemOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `MenuItemSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **tabIndex** | `number` | `0` |  |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `MenuItem` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaMenuItem-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `MenuItem` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-focusVisible | `focusVisible` | Class name applied to the root element if the link is keyboard focused. |
| .Nova-disabled | `disabled` | Class name applied to the inner `component` element if `disabled={true}`. |
| .Nova-selected | `selected` | Class name applied to the root element if `selected={true}`. |

<br><br>

# MenuList

API reference docs for the React MenuList component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `MenuList` component, you can choose to import it directly or through the main entry point.

```jsx
import { MenuList } from '@hxnova/react-components/MenuList';
// or
import { MenuList } from '@hxnova/react-components';
```

## Props

The properties available for the `MenuList` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **density** | ``"standard" ⏐ "compact" ⏐ "comfortable"`` | `'standard'` | The `density` attribute for the MenuListItem |
| **onItemsChange** | ``(items: string[]) => void`` | - | Function called when the items displayed in the menu change. |
| **slotProps** | ``{ root?: SlotProps<"ul", object, MenuListOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `MenuListSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `MenuList` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaMenuList-root | `'ul'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `MenuList` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaMenuList-densityStandard | `densityStandard` | Class name applied to the root element if `density="standard"`. |
| .NovaMenuList-densityCompact | `densityCompact` | Class name applied to the root element if `density="compact"`. |
| .NovaMenuList-densityComfortable | `densityComfortable` | Class name applied to the root element if `density="comfortable"`. |

