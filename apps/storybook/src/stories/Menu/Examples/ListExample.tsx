import React from 'react';
import { MenuList } from '@hxnova/react-components/MenuList';
import { MenuItem } from '@hxnova/react-components/MenuItem';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Typography } from '@hxnova/react-components/Typography';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { Divider } from '@hxnova/react-components/Divider';
import Icon from '@hxnova/icons/Icon';

export default function IconMenu() {
  return (
    <div sx={{ width: '200px' }}>
      <MenuList>
        <MenuItem>
          <ListItemDecorator>
            <Icon family="material" name="content_cut" size={24} />
          </ListItemDecorator>
          <ListItemContent>Cut</ListItemContent>
          <Typography variant="bodySmall">⌘X</Typography>
        </MenuItem>
        <MenuItem>
          <ListItemDecorator>
            <Icon family="material" name="content_copy" size={24} />
          </ListItemDecorator>
          <ListItemContent>Copy</ListItemContent>
          <Typography variant="bodySmall">⌘C</Typography>
        </MenuItem>
        <MenuItem>
          <ListItemDecorator>
            <Icon family="material" name="content_paste" size={24} />
          </ListItemDecorator>
          <ListItemContent>Paste</ListItemContent>
          <Typography variant="bodySmall">⌘V</Typography>
        </MenuItem>
        <Divider />
        <MenuItem>
          <ListItemDecorator>
            <Icon family="material" name="cloud" size={24} />
          </ListItemDecorator>
          <ListItemContent>Web Clipboard</ListItemContent>
        </MenuItem>
      </MenuList>
    </div>
  );
}
