import { useState } from 'react';
import { FloatingActionBar } from '@hxnova/react-components/FloatingActionBar';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  const [selectedItems, setSelectedItems] = useState<string[]>(['bookmark']);

  const toggleItem = (item: string) => {
    setSelectedItems((prev) => {
      if (prev.includes(item)) {
        return prev.filter((i) => i !== item);
      } else {
        return [...prev, item];
      }
    });
  };

  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <FloatingActionBar.Root>
        <FloatingActionBar.Item selected={selectedItems.includes('bookmark')} onClick={() => toggleItem('bookmark')}>
          <Icon family="material" name="bookmark_border" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item
          showDivider
          selected={selectedItems.includes('palette')}
          onClick={() => toggleItem('palette')}
        >
          <Icon family="material" name="palette" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item
          selected={selectedItems.includes('border-color')}
          onClick={() => toggleItem('border-color')}
        >
          <Icon family="material" name="border_color" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item
          selected={selectedItems.includes('person-add')}
          onClick={() => toggleItem('person-add')}
        >
          <Icon family="material" name="person_add" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item disabled>
          <Icon family="material" name="delete" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item selected={selectedItems.includes('apps')} onClick={() => toggleItem('apps')}>
          <Icon family="material" name="apps" />
        </FloatingActionBar.Item>
      </FloatingActionBar.Root>
    </div>
  );
}
