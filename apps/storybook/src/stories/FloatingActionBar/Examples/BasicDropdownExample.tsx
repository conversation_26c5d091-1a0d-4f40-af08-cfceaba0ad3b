import * as React from 'react';
import { FloatingActionBar } from '@hxnova/react-components/FloatingActionBar';
import { Popper } from '@hxnova/react-components/Popper';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [selectedItem, setSelectedItem] = React.useState<string>('palette');
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(!anchorEl ? event.currentTarget : null);
  };
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <FloatingActionBar.Root orientation="vertical">
        <FloatingActionBar.Item>
          <Icon family="material" name="bookmark_border" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item showDivider selected={open} showDropdownIcon onClick={handleClick}>
          <Icon family="material" name={selectedItem} />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item>
          <Icon family="material" name="border_color" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item showDivider>
          <Icon family="material" name="person_add" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item disabled>
          <Icon family="material" name="delete" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item>
          <Icon family="material" name="apps" />
        </FloatingActionBar.Item>
      </FloatingActionBar.Root>
      <Popper
        open={open}
        anchorEl={anchorEl}
        placement={'right-start'}
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: { offset: [-4, 10] },
            },
          ],
        }}
      >
        <FloatingActionBar.Root orientation="vertical">
          <FloatingActionBar.Item selected={selectedItem === 'palette'} onClick={() => setSelectedItem('palette')}>
            <Icon family="material" name="palette" />
          </FloatingActionBar.Item>
          <FloatingActionBar.Item
            selected={selectedItem === 'deployed_code'}
            onClick={() => setSelectedItem('deployed_code')}
          >
            <Icon family="material" name="deployed_code" />
          </FloatingActionBar.Item>
        </FloatingActionBar.Root>
      </Popper>
    </div>
  );
}
