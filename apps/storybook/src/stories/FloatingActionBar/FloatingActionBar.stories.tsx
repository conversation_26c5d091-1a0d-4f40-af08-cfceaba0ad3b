/* eslint-disable react/prop-types */
import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import { FloatingActionBar, FloatingActionBarProps } from '@hxnova/react-components/FloatingActionBar';
import { Popper } from '@hxnova/react-components/Popper';
import { Icon } from '@hxnova/icons';

export default {
  title: '@hxnova/react-components/Floating Action Bar',
  component: FloatingActionBar.Root,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=3580-4791&p=f&t=48L4VrkDd4OGwulG-0',
    },
  },
  tags: ['!autodocs'],
} as Meta<FloatingActionBarProps>;

const FloatingActionBarTemplate: StoryFn<(props: FloatingActionBarProps) => JSX.Element> = ({ ...args }) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(!anchorEl ? event.currentTarget : null);
  };
  return (
    <div style={{ display: 'flex', flexDirection: 'row' }}>
      <FloatingActionBar.Root {...args}>
        <FloatingActionBar.Item>
          <Icon family="material" name="bookmark_border" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item showDivider showDropdownIcon selected={open} onClick={handleClick}>
          <Icon family="material" name="palette" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item>
          <Icon family="material" name="border_color" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item>
          <Icon family="material" name="person_add" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item disabled>
          <Icon family="material" name="delete" />
        </FloatingActionBar.Item>
        <FloatingActionBar.Item>
          <Icon family="material" name="apps" />
        </FloatingActionBar.Item>
      </FloatingActionBar.Root>
      <Popper
        open={open}
        anchorEl={anchorEl}
        placement={args.orientation === 'horizontal' ? 'top-start' : 'right-start'}
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: { offset: [-4, 10] },
            },
          ],
        }}
      >
        <FloatingActionBar.Root {...args}>
          <FloatingActionBar.Item selected>
            <Icon family="material" name="palette" />
          </FloatingActionBar.Item>
          <FloatingActionBar.Item>
            <Icon family="material" name="deployed_code" />
          </FloatingActionBar.Item>
        </FloatingActionBar.Root>
      </Popper>
    </div>
  );
};

export const Basic = {
  render: FloatingActionBarTemplate,
  args: {
    orientation: 'horizontal',
    size: 'medium',
  },
  argTypes: {
    size: {
      control: { type: 'radio' },
      options: ['small', 'medium', 'large'],
    },
    orientation: {
      control: { type: 'radio' },
      options: ['horizontal', 'vertical'],
    },
  },
  parameters: {
    controls: {
      include: ['size', 'orientation'],
    },
  },
};
