# API Documentation

- [FloatingActionBar.Item](#floatingactionbaritem)
- [FloatingActionBar.Root](#floatingactionbarroot)

# FloatingActionBar.Item

API reference docs for the React FloatingActionBar.Item component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `FloatingActionBar.Item` component, you can choose to import it directly or through the main entry point.

```jsx
import { FloatingActionBar } from '@hxnova/react-components/FloatingActionBar';
// or
import { FloatingActionBar } from '@hxnova/react-components';

// usage
<FloatingActionBar.Item>{children}</FloatingActionBar.Item>
```

## Props

The properties available for the `FloatingActionBar.Item` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``Ref<ButtonActions>`` | - | A ref for imperative actions. It currently only supports `focusVisible()` action. |
| **className** | `string` | - |  |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **focusableWhenDisabled** | ``false ⏐ true`` | `false` | If `true`, allows a disabled button to receive focus. |
| **href** | `string` | - |  |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **rootElementName** | `keyof HTMLElementTagNameMap` | `'button'` | The HTML element that is ultimately rendered, for example 'button' or 'a' |
| **selected** | ``false ⏐ true`` | `false` | Whether the component is selected. |
| **showDivider** | ``false ⏐ true`` | `false` | Display a divider after the component.<br>- When FloatingActionBar is horizontal, the divider will be vertical (on the right)<br>- When FloatingActionBar is vertical, the divider will be horizontal (on the bottom) |
| **showDropdownIcon** | ``false ⏐ true`` | `false` | Whether a dropdown icon should appear on the corner. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | How large the FloatingAction should be. |
| **slotProps** | ``{ root?: SlotComponentProps<"button", ButtonRootSlotPropsOverrides, { action?: Ref<ButtonActions>; children?: ReactNode; ... 13 more ...; focusVisible: boolean; }> ⏐ undefined; } ⏐ undefined`` | `{}` | The props used for each slot inside the Button. |
| **slots** | `ButtonSlots` | `{}` | The components used for each slot inside the Button.<br>Either a string to use a HTML element or a component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **tabIndex** | `number` | - |  |
| **to** | `string` | - |  |
| **type** | ``"button" ⏐ "submit" ⏐ "reset"`` | `'button'` | Type attribute applied when the `component` is `button`. |

## CSS classes

CSS classes for different states and variations of the `FloatingActionBar.Item` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaFloatingAction-root | `root` | Class name applied to the root element. |
| .NovaFloatingAction-dropdown | `dropdown` | Class name applied to the dropdown element. |

<br><br>

# FloatingActionBar.Root

API reference docs for the React FloatingActionBar.Root component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `FloatingActionBar.Root` component, you can choose to import it directly or through the main entry point.

```jsx
import { FloatingActionBar } from '@hxnova/react-components/FloatingActionBar';
// or
import { FloatingActionBar } from '@hxnova/react-components';

// usage
<FloatingActionBar.Root>{children}</FloatingActionBar.Root>
```

## Props

The properties available for the `FloatingActionBar.Root` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'horizontal'` | The component orientation. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | How large the FloatingActionBar contents should be. |
| **slotProps** | ``{ root?: SlotProps<"div", FloatingActionBarSlotPropsOverrides, FloatingActionBarOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `FloatingActionBarSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `FloatingActionBar.Root` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaFloatingActionBar-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `FloatingActionBar.Root` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaFloatingActionBar-horizontal | `horizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaFloatingActionBar-vertical | `vertical` | Class name applied to the root element if `orientation="vertical"`. |
| .NovaFloatingActionBar-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaFloatingActionBar-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaFloatingActionBar-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |

