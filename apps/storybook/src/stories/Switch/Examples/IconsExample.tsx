import { Switch } from '@hxnova/react-components/Switch';
import Icon from '@hxnova/icons/Icon';

export default function IconsDemo() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <Switch
        onIcon={<Icon family="material" name="check" size={24} />}
        offIcon={<Icon family="material" name="close" size={24} />}
        size="medium"
        defaultChecked
      />
      <Switch
        onIcon={<Icon family="material" name="check" size={24} />}
        offIcon={<Icon family="material" name="close" size={24} />}
        size="medium"
      />
    </div>
  );
}
