import { Switch } from '@hxnova/react-components/Switch';

export default function SizeDemo() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '5rem' }}>
      <Switch size={'small'} endDecorator="Value Text" defaultChecked />
      <Switch size={'medium'} endDecorator="Value Text" defaultChecked />
      <Switch size={'large'} endDecorator="Value Text" defaultChecked />
    </div>
  );
}
