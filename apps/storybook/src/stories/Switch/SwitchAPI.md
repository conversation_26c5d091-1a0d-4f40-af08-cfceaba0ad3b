# API Documentation

- [Switch](#switch)

# Switch

API reference docs for the React Switch component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Switch` component, you can choose to import it directly or through the main entry point.

```jsx
import { Switch } from '@hxnova/react-components/Switch';
// or
import { Switch } from '@hxnova/react-components';
```

## Props

The properties available for the `Switch` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **checked** | ``false ⏐ true`` | - | If `true`, the component is checked. |
| **className** | `string` | - | Class name applied to the root element. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **defaultChecked** | ``false ⏐ true`` | - | The default checked state. Use when the component is not controlled. |
| **disabled** | ``false ⏐ true`` | - | If `true`, the component is disabled. |
| **endDecorator** | ``ReactNode ⏐ ((ownerState: SwitchOwnerState) => ReactNode)`` | - | The element that appears at the end of the switch. |
| **id** | `string` | - | The id of the `input` element. |
| **name** | `string` | - | Name attribute of the `input` element. |
| **offIcon** | `ReactNode` | - | The icon to display when the component is off. |
| **onBlur** | ``FocusEventHandler<Element>`` | - |  |
| **onChange** | ``ChangeEventHandler<HTMLInputElement>`` | - | Callback fired when the state is changed.<br>@param event The event source of the callback.<br>You can pull out the new value by accessing `event.target.value` (string).<br>You can pull out the new checked state by accessing `event.target.checked` (boolean). |
| **onFocus** | ``FocusEventHandler<Element>`` | - |  |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **onIcon** | `ReactNode` | - | The icon to display when the component is on. |
| **readOnly** | ``false ⏐ true`` | - | If `true`, the component is read only. |
| **required** | ``false ⏐ true`` | - | If `true`, the `input` element is required. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the switch. |
| **slotProps** | ``{ root?: SlotProps<"div", object, SwitchOwnerState> ⏐ undefined; thumb?: SlotProps<"span", object, SwitchOwnerState> ⏐ undefined; ... 4 more ...; endDecorator?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `SwitchSlots` | `{}` | The components used for each slot inside. |
| **startDecorator** | ``ReactNode ⏐ ((ownerState: SwitchOwnerState) => ReactNode)`` | - | The element that appears at the start of the switch. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | `unknown` | - | The value of the component. The DOM API casts this to a string. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Switch` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaSwitch-root | `'div'` | The component that renders the root. |
| thumb | .NovaSwitch-thumb | `'span'` | The component that renders the thumb. |
| action | .NovaSwitch-action | `'div'` | The component that renders the action. |
| input | .NovaSwitch-input | `'input'` | The component that renders the input. |
| track | .NovaSwitch-track | `'span'` | The component that renders the track. |
| startDecorator | .NovaSwitch-startDecorator | `'span'` | The component that renders the start decorator. |
| endDecorator | .NovaSwitch-endDecorator | `'span'` | The component that renders the end decorator. |

## CSS classes

CSS classes for different states and variations of the `Switch` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-checked | `checked` | State class applied to the root `checked` class. |
| .Nova-disabled | `disabled` | State class applied to the root disabled class. |
| .Nova-focusVisible | `focusVisible` | State class applied to the root element if the switch has visible focus |
| .NovaSwitch-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaSwitch-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaSwitch-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |
| .NovaSwitch-withIcon | `withIcon` | Class name applied to the thumb `withIcon` class. |

