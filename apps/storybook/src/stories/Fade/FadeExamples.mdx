import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import { Fade } from '@hxnova/react-components/Fade';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import InteractiveExample from './Examples/InteractiveExample';
import InteractiveExampleSource from './Examples/InteractiveExample.tsx?raw';

<Meta title="@hxnova/react-components/Transitions/Fade/Examples" />

## Fade Transition

The Fade component provides smooth opacity transitions for showing and hiding content. It's built on top of `react-transition-group` and uses the **`in` prop** to control fade-in and fade-out animations.

### Basic Usage

The Fade component animates the opacity of its child element from 0 to 1 (fade in) or from 1 to 0 (fade out) based on the **`in` prop**. Use the **`timeout` prop** to control the animation duration in milliseconds (e.g., `timeout={300}` for a 300ms fade animation).

<div className="doc-story sb-story sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

### Interactive Example

Here's a more complex example showing multiple Fade components working together:

<div className="doc-story sb-story sb-unstyled">
  <InteractiveExample />
</div>
<CodeExpand code={InteractiveExampleSource} showBorderTop style={{marginTop: 16}}/>
