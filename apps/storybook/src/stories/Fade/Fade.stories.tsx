import { useState } from 'react';
import type { Meta, StoryFn, StoryObj } from '@storybook/react';
import { Fade, FadeProps } from '@hxnova/react-components/Fade';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

const meta = {
  title: '@hxnova/react-components/Transitions/Fade',
  component: Fade,
  parameters: {
    layout: 'centered',
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof Fade>;

export default meta;
type Story = StoryObj<typeof meta>;

const Template: StoryFn<(props: FadeProps) => JSX.Element> = ({ ...args }) => {
  const [checked, setChecked] = useState(false);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
      <Switch checked={checked} onChange={() => setChecked((prev: boolean) => !prev)} endDecorator="Toggle Fade" />
      <Fade {...args} in={checked}>
        <Box
          sx={{
            padding: '8px',
            border: '1px solid var(--palette-outlineVariant)',
            borderRadius: 'var(--radius-2xs)',
            backgroundColor: 'var(--palette-surfaceContainer)',
            width: '300px',
            textAlign: 'center',
          }}
        >
          <Typography variant="bodyMedium">
            This content fades in and out smoothly using the Fade transition component.
          </Typography>
        </Box>
      </Fade>
    </div>
  );
};

export const Default: Story = {
  render: Template,
  args: {
    appear: true,
    timeout: 300,
    easing: 'ease-in-out',
    children: <div />,
  },
  argTypes: {
    timeout: {
      control: 'select',
      options: [100, 300, 1000],
    },
  },
  parameters: {
    controls: {
      include: ['timeout'],
    },
  },
};
