import * as React from 'react';
import { SegmentedButtonGroup } from '@hxnova/react-components/SegmentedButtonGroup';
import { SegmentedButton } from '@hxnova/react-components/SegmentedButton';

export default function Demo() {
  const [values, setValues] = React.useState(['1', '2']);
  const handleChanges = (_event: React.ChangeEvent<unknown>, newValues: string[]) => {
    setValues(newValues);
  };

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', flexWrap: 'wrap', gap: '1rem' }}>
      <SegmentedButtonGroup value={values} onChange={handleChanges}>
        <SegmentedButton value="1">Button 1</SegmentedButton>
        <SegmentedButton value="2">Button 2</SegmentedButton>
        <SegmentedButton value="3">Button 3</SegmentedButton>
      </SegmentedButtonGroup>
    </div>
  );
}
