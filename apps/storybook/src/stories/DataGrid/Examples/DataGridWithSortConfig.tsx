import React from 'react';
import { DataGrid, ColumnType, SortDirection } from '@hxnova/react-components/DataGrid';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Tag } from '@hxnova/react-components/Tag';
import { Link } from '@hxnova/react-components/Link';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
  status: 'active' | 'pending' | 'deactivated';
};

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  {
    field: 'avatar',
    header: 'User',
    width: 300,
    cell: (paras: DataType) => (
      <div sx={{ display: 'flex', alignItems: 'center', gap: '8px', height: '100%' }}>
        <Avatar>
          {paras.firstName ? paras.firstName.charAt(0).toUpperCase() : ''}
          {paras.lastName ? paras.lastName.charAt(0).toUpperCase() : ''}
        </Avatar>
        <Link
          href="https://www.google.com"
          target="_blank"
          variant="bodySmall"
          sx={{ display: 'inline', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}
        >
          {`${paras.firstName} ${paras.lastName}`}
        </Link>
      </div>
    ),
    sortFn: (d1: DataType, d2: DataType, sort: SortDirection) => {
      const d1Name = `${d1.firstName} ${d1.lastName}`;
      const d2Name = `${d2.firstName} ${d2.lastName}`;
      return sort === 'asc' ? d1Name.localeCompare(d2Name) : d2Name.localeCompare(d1Name);
    },
  },
  { field: 'firstName', header: 'First Name', width: 200, sortable: false },
  { field: 'lastName', header: 'Last Name', width: 200, sortable: false },
  {
    field: 'status',
    header: 'Status',
    width: 200,
    cell: (paras: DataType) => (
      <Tag
        intensity="subtle"
        variant={paras.status === 'active' ? 'success' : paras.status === 'pending' ? 'warning' : 'error'}
        label={`${paras.status.charAt(0).toUpperCase()}${paras.status.substring(1, paras.status.length)}`}
      />
    ),
    sortFn: (d1: DataType, d2: DataType, sort: SortDirection) => {
      const sortOrder = ['active', 'pending', 'deactivated'];
      const statusA = sortOrder.indexOf(d1.status);
      const statusB = sortOrder.indexOf(d2.status);

      if (statusA === -1) {
        return 1;
      }
      if (statusB === -1) {
        return -1;
      }
      if (sort === 'asc') {
        return statusA - statusB;
      } else {
        return statusB - statusA;
      }
    },
  },
];
const data: DataType[] = [
  { id: 1, firstName: 'Tony', lastName: 'Smith', status: 'pending' },
  { id: 2, firstName: 'Isla', lastName: 'Fletcher', status: 'active' },
  { id: 3, firstName: 'Evie', lastName: 'Easton', status: 'deactivated' },
  { id: 4, firstName: 'Liam', lastName: 'Johnson', status: 'active' },
  { id: 5, firstName: 'Ava', lastName: 'Brown', status: 'active' },
  { id: 6, firstName: 'Noah', lastName: 'Williams', status: 'deactivated' },
  { id: 7, firstName: 'Olivia', lastName: 'Jones', status: 'active' },
  { id: 8, firstName: 'Mason', lastName: 'Garcia', status: 'pending' },
  { id: 9, firstName: 'Sophia', lastName: 'Martinez', status: 'active' },
  { id: 10, firstName: 'Jacob', lastName: 'Davis', status: 'active' },
  { id: 11, firstName: 'Charlotte', lastName: 'Rodriguez', status: 'deactivated' },
];
export default function DataGridDemo() {
  return (
    <DataGrid columns={columns} data={data} initialState={{ sortedColumns: [{ field: 'status', sort: 'asc' }] }} />
  );
}
