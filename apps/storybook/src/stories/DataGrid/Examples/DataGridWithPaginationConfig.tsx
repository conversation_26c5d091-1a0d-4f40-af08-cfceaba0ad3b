import React from 'react';
import { DataGrid, ColumnType } from '@hxnova/react-components/DataGrid';
import { Tag } from '@hxnova/react-components/Tag';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
  status: 'active' | 'pending' | 'deactivated';
};

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
  {
    field: 'status',
    header: 'Status',
    width: 200,
    cell: (paras: DataType) => (
      <Tag
        intensity="subtle"
        variant={paras.status === 'active' ? 'success' : paras.status === 'pending' ? 'warning' : 'error'}
        label={`${paras.status.charAt(0).toUpperCase()}${paras.status.substring(1, paras.status.length)}`}
      />
    ),
  },
];
const data: DataType[] = [
  { id: 1, firstName: '<PERSON>', lastName: 'Smith', status: 'active' },
  { id: 2, firstName: 'Isla', lastName: '<PERSON>', status: 'active' },
  { id: 3, firstName: 'Evie', lastName: 'Easton', status: 'deactivated' },
  { id: 4, firstName: 'Liam', lastName: 'Johnson', status: 'active' },
  { id: 5, firstName: 'Ava', lastName: 'Brown', status: 'active' },
  { id: 6, firstName: 'Noah', lastName: 'Williams', status: 'deactivated' },
  { id: 7, firstName: 'Olivia', lastName: 'Jones', status: 'active' },
  { id: 8, firstName: 'Mason', lastName: 'Garcia', status: 'pending' },
  { id: 9, firstName: 'Sophia', lastName: 'Martinez', status: 'active' },
  { id: 10, firstName: 'Jacob', lastName: 'Davis', status: 'active' },
  { id: 11, firstName: 'Charlotte', lastName: 'Rodriguez', status: 'deactivated' },
  { id: 12, firstName: 'Ethan', lastName: 'Wilson', status: 'active' },
  { id: 13, firstName: 'Amelia', lastName: 'Anderson', status: 'active' },
  { id: 14, firstName: 'James', lastName: 'Taylor', status: 'active' },
  { id: 15, firstName: 'Avery', lastName: 'Thomas', status: 'deactivated' },
  { id: 16, firstName: 'Logan', lastName: 'Hernandez', status: 'active' },
  { id: 17, firstName: 'Ella', lastName: 'Moore', status: 'active' },
  { id: 18, firstName: 'Lucas', lastName: 'Martin', status: 'deactivated' },
  { id: 19, firstName: 'Mia', lastName: 'Lee', status: 'active' },
  { id: 20, firstName: 'Jackson', lastName: 'Lee', status: 'active' },
  { id: 21, firstName: 'Grace', lastName: 'Lewis', status: 'active' },
  { id: 22, firstName: 'Benjamin', lastName: 'Walker', status: 'deactivated' },
  { id: 23, firstName: 'Scarlett', lastName: 'Hall', status: 'active' },
  { id: 24, firstName: 'Oliver', lastName: 'Allen', status: 'pending' },
  { id: 25, firstName: 'Chloe', lastName: 'Young', status: 'deactivated' },
  { id: 26, firstName: 'Henry', lastName: 'Hernandez', status: 'active' },
  { id: 27, firstName: 'Sofia', lastName: 'King', status: 'active' },
  { id: 28, firstName: 'William', lastName: 'Wright', status: 'active' },
  { id: 29, firstName: 'Zoe', lastName: 'Scott', status: 'active' },
  { id: 30, firstName: 'Samuel', lastName: 'Green', status: 'active' },
  { id: 31, firstName: 'Lily', lastName: 'Adams', status: 'active' },
  { id: 32, firstName: 'David', lastName: 'Baker', status: 'deactivated' },
  { id: 33, firstName: 'Addison', lastName: 'Gonzalez', status: 'active' },
  { id: 34, firstName: 'Matthew', lastName: 'Nelson', status: 'active' },
  { id: 35, firstName: 'Natalie', lastName: 'Carter', status: 'active' },
  { id: 36, firstName: 'Daniel', lastName: 'Mitchell', status: 'deactivated' },
  { id: 37, firstName: 'Lucy', lastName: 'Perez', status: 'active' },
  { id: 38, firstName: 'Joseph', lastName: 'Roberts', status: 'active' },
  { id: 39, firstName: 'Victoria', lastName: 'Turner', status: 'active' },
  { id: 40, firstName: 'Isaac', lastName: 'Phillips', status: 'active' },
  { id: 41, firstName: 'Ella', lastName: 'Campbell', status: 'pending' },
  { id: 42, firstName: 'Leo', lastName: 'Parker', status: 'active' },
  { id: 43, firstName: 'Aria', lastName: 'Evans', status: 'active' },
  { id: 44, firstName: 'Caleb', lastName: 'Edwards', status: 'active' },
  { id: 45, firstName: 'Aubrey', lastName: 'Collins', status: 'deactivated' },
  { id: 46, firstName: 'Anthony', lastName: 'Stewart', status: 'active' },
  { id: 47, firstName: 'Hailey', lastName: 'Sanchez', status: 'active' },
  { id: 48, firstName: 'Dylan', lastName: 'Morris', status: 'active' },
  { id: 49, firstName: 'Stella', lastName: 'Rogers', status: 'deactivated' },
  { id: 50, firstName: 'Gabriel', lastName: 'Reed', status: 'active' },
  { id: 51, firstName: 'AA', lastName: 'BB', status: 'active' },
];
export default function DataGridDemo() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'column', maxHeight: '400px' }}>
      <DataGrid
        columns={columns}
        data={data}
        initialState={{
          pagination: {
            rowsPerPage: 25,
            page: 1, // zero-based index
          },
        }}
        paginationConfig={{
          labelRowsPerPage: 'Rows per page',
          renderPageInfo: (page: number, totalPages: number) => {
            return `Rows ${page + 1} of ${totalPages}`;
          },
          rowsPerPageOptions: [5, 10, 25, 50],
        }}
      />
    </div>
  );
}
