import React from 'react';
import { DataGrid, ColumnType } from '@hxnova/react-components/DataGrid';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Tag } from '@hxnova/react-components/Tag';
import { Link } from '@hxnova/react-components/Link';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
  status: 'active' | 'pending' | 'deactivated';
};

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150, visible: false },
  {
    field: 'avatar',
    header: 'User',
    width: 300,
    sortable: false,
    cell: (paras: DataType) => (
      <div sx={{ display: 'flex', alignItems: 'center', gap: '8px', height: '100%' }}>
        <Avatar>
          {paras.firstName ? paras.firstName.charAt(0).toUpperCase() : ''}
          {paras.lastName ? paras.lastName.charAt(0).toUpperCase() : ''}
        </Avatar>
        <Link
          href="https://www.google.com"
          target="_blank"
          variant="bodySmall"
          sx={{ display: 'inline', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}
        >
          {`${paras.firstName} ${paras.lastName}`}
        </Link>
      </div>
    ),
  },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
  {
    field: 'status',
    header: 'Status',
    width: 200,
    cell: (paras: DataType) => (
      <Tag
        intensity="subtle"
        variant={paras.status === 'active' ? 'success' : paras.status === 'pending' ? 'warning' : 'error'}
        label={`${paras.status.charAt(0).toUpperCase()}${paras.status.substring(1, paras.status.length)}`}
      />
    ),
  },
];
const data: DataType[] = [
  { id: 1, firstName: 'Tony', lastName: 'Smith', status: 'active' },
  { id: 2, firstName: 'Isla', lastName: 'Fletcher', status: 'active' },
  { id: 3, firstName: 'Evie', lastName: 'Easton', status: 'deactivated' },
  { id: 4, firstName: 'Liam', lastName: 'Johnson', status: 'active' },
  { id: 5, firstName: 'Ava', lastName: 'Brown', status: 'active' },
  { id: 6, firstName: 'Noah', lastName: 'Williams', status: 'deactivated' },
];
export default function DataGridDemo() {
  return <DataGrid columns={columns} data={data} />;
}
