import React, { useCallback, useState } from 'react';
import { CellType, ColumnType, DataGrid } from '@hxnova/react-components/DataGrid';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
};

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
  {
    field: 'fullName',
    header: 'Full Name',
    width: 250,
    cell: (row) => `${row.firstName} ${row.lastName}`,
    sortable: false,
  },
];
const data: DataType[] = [
  { id: 1, firstName: '<PERSON>', lastName: '<PERSON>' },
  { id: 2, firstName: 'Isla', lastName: '<PERSON>' },
  { id: 3, firstName: 'Evie', lastName: 'Easton' },
  { id: 4, firstName: '<PERSON>', lastName: '<PERSON>' },
  { id: 5, firstName: 'Ava', lastName: '<PERSON>' },
  { id: 6, firstName: '<PERSON>', lastName: '<PERSON>' },
];
export default function DataGridDemo() {
  const initialSelectedCells = [{ rowId: 2, columnField: 'firstName' }];
  const [selectedCell, setSelectedCell] = useState<CellType[]>(initialSelectedCells);
  const onSelectedCellsChange = useCallback((cells: CellType[]) => {
    setSelectedCell(cells);
  }, []);
  return (
    <div>
      {selectedCell && (
        <div
          sx={{
            backgroundColor: `var(--palette-primaryContainer)`,
            color: `var(--palette-onPrimaryContainer)`,
            marginBottom: '4px',
            padding: '4px',
          }}
        >
          <div>
            <b>Selected Row:</b> {selectedCell[0]?.rowId}
          </div>
          <div>
            <b>Selected Column:</b> {selectedCell[0]?.columnField}
          </div>
        </div>
      )}
      <DataGrid
        columns={columns}
        data={data}
        cellSelection
        selectedCells={selectedCell}
        onSelectedCellsChange={onSelectedCellsChange}
        initialState={{ selectedCells: initialSelectedCells }}
      />
    </div>
  );
}
