import { useState } from 'react';
import { CellEditParas, ColumnType, DataGrid } from '@hxnova/react-components/DataGrid';
import { Tag } from '@hxnova/react-components/Tag';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
  status: 'open' | 'active' | 'finished';
};

type ApiResponse = {
  status: number;
  data: {
    message: string;
    receivedData: DataType;
  };
};

const ChooseStatus = (props: CellEditParas) => {
  const { value, onValueChange, autoFocus } = props;
  const [open, setOpen] = useState(true);
  const options = [
    { label: 'Open', value: 'open' },
    { label: 'Active', value: 'active' },
    { label: 'Finished', value: 'finished' },
  ];
  return (
    <Dropdown
      listboxOpen={open}
      value={value}
      autoFocus={autoFocus}
      onClick={() => {
        setOpen(!open);
      }}
      onChange={(e, v) => {
        onValueChange(v);
      }}
      sx={{ display: 'inline-flex', background: 'var(--palette-surfaceContainer)' }}
    >
      {options.map((item) => (
        <Option
          key={item.value}
          value={item.value}
          onKeyDown={(event) => {
            if (event.key === 'Enter') {
              setOpen(false);
              event.stopPropagation();
            }
          }}
        >
          {item.label}
        </Option>
      ))}
    </Dropdown>
  );
};

const renderStatusEditCell = (props: CellEditParas) => {
  return <ChooseStatus {...props} />;
};

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  { field: 'firstName', header: 'First Name', width: 200, editable: true },
  { field: 'lastName', header: 'Last Name', width: 200, editable: true },
  {
    field: 'fullName',
    header: 'Full Name',
    width: 250,
    cell: (row) => `${row.firstName} ${row.lastName}`,
    sortable: false,
  },
  {
    field: 'status',
    header: 'Status',
    width: 200,
    cell: (paras: DataType) => (
      <Tag
        intensity="subtle"
        variant={paras.status === 'open' ? 'neutral' : paras.status === 'active' ? 'info' : 'success'}
        label={`${paras.status.charAt(0).toUpperCase()}${paras.status.substring(1, paras.status.length)}`}
      />
    ),
    renderEditCell: renderStatusEditCell,
    editable: true,
  },
];

const initialData: DataType[] = [
  { id: 1, firstName: 'Tony', lastName: 'Smith', status: 'open' },
  { id: 2, firstName: 'Isla', lastName: 'Fletcher', status: 'open' },
  { id: 3, firstName: 'Evie', lastName: 'Easton', status: 'active' },
  { id: 4, firstName: 'Liam', lastName: 'Johnson', status: 'open' },
  { id: 5, firstName: 'Ava', lastName: 'Brown', status: 'open' },
  { id: 6, firstName: 'Noah', lastName: 'Williams', status: 'finished' },
];

export default function DataGridDemo() {
  const [data, setData] = useState<DataType[]>(initialData);

  const simulatePutRequest = async (newRow: DataType): Promise<ApiResponse> => {
    // The possible API call
    // const response = await fetch(`/api/putData`, { method: 'PUT', body: JSON.stringify({ ...newRow }) });
    // const data = await response.json();
    return new Promise((resolve, reject) => {
      // Simulating network delay
      setTimeout(() => {
        // Simulate a successful response
        const isSuccess = Math.random() > 0.05; // 95% chance of success

        if (isSuccess) {
          resolve({
            status: 200,
            data: {
              message: 'Data received successfully',
              receivedData: { ...newRow },
            },
          });
        } else {
          reject(new Error('Failed to update data'));
        }
      }, 200); // Simulates a 200 ms delay
    });
  };

  const processRowUpdate = async (newRow: DataType, originalRow: DataType) => {
    const response = await simulatePutRequest(newRow);
    if (response.status === 200) {
      setData((prevData) => {
        return prevData.map((row) => (row.id === newRow.id ? response.data.receivedData : row));
      });
      return newRow;
    }
    return originalRow;
  };

  const onProcessRowUpdateError = (error: unknown) => {
    if (error instanceof Error) {
      alert(error.message);
    } else {
      alert('An unknown error occurred');
    }
  };

  return (
    <DataGrid
      columns={columns}
      data={data}
      editMode="row"
      processRowUpdate={processRowUpdate}
      onProcessRowUpdateError={onProcessRowUpdateError}
      onRowEditStart={(row) => {
        console.log('onRowEditStart', row);
      }}
      onRowEditStop={(row) => {
        console.log('onRowEditStop', row);
      }}
      onEditingRowsChange={(rowIds) => {
        console.log('onEditingRowsChange', rowIds);
      }}
    />
  );
}
