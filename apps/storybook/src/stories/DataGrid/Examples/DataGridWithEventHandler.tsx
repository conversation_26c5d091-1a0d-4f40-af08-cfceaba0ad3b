import { CellEventHandlers, ColumnType, DataGrid, RowEventHandlers } from '@hxnova/react-components/DataGrid';
import { useMemo, useState } from 'react';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
};

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
  {
    field: 'fullName',
    header: 'Full Name',
    width: 300,
    cell: (row) => `${row.firstName} ${row.lastName}`,
    sortable: false,
  },
];

const data: DataType[] = [
  { id: 1, firstName: '<PERSON>', lastName: '<PERSON>' },
  { id: 2, firstName: 'Isla', lastName: '<PERSON>' },
  { id: 3, firstName: 'Evie', lastName: 'Easton' },
  { id: 4, firstName: '<PERSON>', lastName: '<PERSON>' },
  { id: 5, firstName: 'Ava', lastName: '<PERSON>' },
  { id: 6, firstName: '<PERSON>', lastName: '<PERSON>' },
];

export default function DataGridDemo() {
  const [cellEvent, setCellEvent] = useState<string>('');
  const [rowEvent, setRowEvent] = useState<string>('');

  const cellEvents: CellEventHandlers<DataType> = useMemo(
    () => ({
      onClick: (paras, event) => {
        setCellEvent(`Cell [${paras.rowId}, ${paras.columnField}] is clicked`);
      },
      onDoubleClick: (paras, event) => {
        setCellEvent(`Cell [${paras.rowId}, ${paras.columnField}] is double clicked`);
      },
      onContextMenu: (paras, event) => {
        setCellEvent(`Cell [${paras.rowId}, ${paras.columnField}] is right clicked`);
      },
    }),
    [],
  );

  const rowEvents: RowEventHandlers<DataType> = useMemo(
    () => ({
      onClick: (paras, event) => {
        setRowEvent(`Row ${paras.rowId} is clicked`);
      },
      onDoubleClick: (paras, event) => {
        setRowEvent(`Row ${paras.rowId} is double clicked`);
      },
      onContextMenu: (paras, event) => {
        setRowEvent(`Row ${paras.rowId} is right clicked`);
      },
    }),
    [],
  );

  return (
    <div>
      <div
        sx={{
          backgroundColor: `var(--palette-primaryContainer)`,
          color: `var(--palette-onPrimaryContainer)`,
          marginBottom: '4px',
          padding: '4px',
        }}
      >
        <div>
          <b>Cell event:</b> {cellEvent}
        </div>
        <div>
          <b>Row event:</b> {rowEvent}
        </div>
      </div>
      <DataGrid columns={columns} data={data} cellEvents={cellEvents} rowEvents={rowEvents} />
    </div>
  );
}
