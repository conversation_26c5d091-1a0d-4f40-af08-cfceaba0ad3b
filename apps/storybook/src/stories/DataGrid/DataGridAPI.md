# API Documentation

- [DataGrid](#datagrid)

# DataGrid

API reference docs for the React DataGrid component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `DataGrid` component, you can choose to import it directly or through the main entry point.

```jsx
import { DataGrid } from '@hxnova/react-components/DataGrid';
// or
import { DataGrid } from '@hxnova/react-components';
```

## Props

The properties available for the `DataGrid` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **columns*** | ``readonly ColumnType<T>[]`` | - | Configuration for the columns of the DataGrid. |
| **data*** | `readonly T[]` | - | The data to be displayed in the DataGrid. |
| **cellEvents** | ``CellEventHandlers<T>`` | - | Cell-level event handlers (such as onClick, onKeyDown) |
| **cellSelection** | ``false ⏐ true`` | `false` | Indicate if the cell selection is enabled. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **density** | ``"compact" ⏐ "standard" ⏐ "comfortable"`` | `'standard'` | The visual density of the DataGrid. Options are 'compact', 'standard', or 'comfortable'. Default is 'standard'. |
| **disableRowSelectionOnClick** | ``false ⏐ true`` | `false` | If `true`, the selection on click on a row or cell is disabled. |
| **editingCells** | `readonly CellType[]` | - | Array of unique identifiers for cells under editing state . |
| **editingRows** | `readonly RowId[]` | - | Array of unique identifiers for rows under editing state . |
| **editMode** | ``"cell" ⏐ "row"`` | `'cell'` | indicate the edit mode. |
| **expandedRowPanelRender** | ``((row: T) => ReactNode)`` | - | Render expand row panel |
| **expandedRows** | `readonly RowId[]` | - | Array of unique identifiers for selected rows. |
| **getExpandedRowHeight** | ``(row: T) => number`` | - | Get the expanded row height. By default, it will be 200px for each row. |
| **headerHeight** | `number` | - | The header height of DataGrid in pixels, default is based on `density` prop.<br>compact: 40px<br>standard: 48px (default)<br>comfortable: 56px |
| **initialState** | `InitialState` | - | Initial state configuration for the DataGrid. |
| **isCellEditable** | ``(cell: CellType) => boolean`` | - | indicate if cell is editable |
| **isRowEditable** | ``(row: T) => boolean`` | - | indicate if row is editable |
| **isRowExpandable** | ``(row: T) => boolean`` | - | indicate if row is expandable |
| **isRowSelectable** | ``(row: T) => boolean`` | - | indicate if row is selectable |
| **onCellEditStart** | ``(cell: CellType) => void`` | - | Callback fired when the cell turns to edit mode. |
| **onCellEditStop** | ``(paras: CellStopEditParas<T>) => void`` | - | Callback fired when the cell turns to view mode. |
| **onEditingCellsChange** | ``(cells: CellType[]) => void`` | - | Callback once edit cells change |
| **onEditingRowsChange** | ``(rowIds: RowId[]) => void`` | - | Callback once edit rows change |
| **onExpandedRowsChange** | ``(rowIds: RowId[]) => void`` | - | Callback once expand rows change |
| **onPageChange** | ``(page: number) => void`` | - | Callback function triggered when page changes. |
| **onProcessRowUpdateError** | ``(error: any, paras: { newRow: T; originalRow: T; }) => void`` | - | Callback called when `processRowUpdate` throws an error or rejects. |
| **onRowEditStart** | ``(row: T) => void`` | - | Callback fired when the row turns to edit mode. |
| **onRowEditStop** | ``(paras: RowStopEditParas<T>) => void`` | - | Callback fired when the row turns to view mode. |
| **onRowsPerPageChange** | ``(rowsPerPage: number) => void`` | - | Callback function triggered when page size changes. |
| **onSelectedCellsChange** | ``(cells: CellType[]) => void`` | - | Callback function triggered when cell selection changes. |
| **onSelectedRowsChange** | ``(rowIds: RowId[]) => void`` | - | Callback function triggered when row selection changes. |
| **onSortedColumnsChange** | ``(sorts: SortItem[]) => void`` | - | Callback function triggered when the sort order changes. |
| **page** | `number` | - | The zero-based index of the current page. |
| **pagination** | ``false ⏐ true`` | `true` | Flag to display pagination in the DataGrid, it's enabled by default. You can hide it by setting "pagination = false". |
| **paginationConfig** | ``Omit<TablePaginationProps, "page" ⏐ "count" ⏐ "onPageChange" ⏐ "onRowsPerPageChange" ⏐ "rowsPerPage">`` | - | Additional pagination config |
| **paginationMode** | ``"client" ⏐ "server"`` | `'client'` | Determines pagination mode: 'client' for client-side or 'server' for server-side. Default is 'client'. |
| **processRowUpdate** | ``(newRow: T, originalRow: T) => T ⏐ Promise<T>`` | - | Callback called before updating a row with new values in the row and cell editing. |
| **ref** | ``((((instance: HTMLDivElement ⏐ null) => void) ⏐ RefObject<HTMLDivElement>) & (string ⏐ ((instance: HTMLDivElement ⏐ null) => void) ⏐ RefObject<...>)) ⏐ null`` | - | Allows getting a ref to the component instance.<br>Once the component unmounts, React will set `ref.current` to `null`<br>(or call the ref with `null` if you passed a callback ref).<br>@see {@link https://react.dev/learn/referencing-values-with-refs#refs-and-the-dom React Docs} |
| **rowEvents** | ``RowEventHandlers<T>`` | - | Row-level event handlers (such as onClick, onMouseLeave) |
| **rowHeight** | `number` | - | The row height of DataGrid in pixels, default is based on `density` prop.<br>compact: 40px<br>standard: 48px (default)<br>comfortable: 56px |
| **rowSelectionMode** | ``"checkboxSelection" ⏐ "radioSelection"`` | - | Selection mode for rows. Options are 'checkboxSelection' or 'radioSelection'. |
| **rowsPerPage** | `number` | - | Rows number of data items per page. |
| **scrollbarSize** | `number` | `15` | The size of the data grid scroll bar in pixels. |
| **selectedCells** | `readonly CellType[]` | - | Array of selected cells. |
| **selectedRows** | `readonly RowId[]` | - | Array of unique identifiers for selected rows. |
| **slotProps** | ``{ root?: SlotProps<"div", object, DataGridOwnerState<any>> ⏐ undefined; header?: SlotProps<"div", object, DataGridOwnerState<any>> ⏐ undefined; headerCell?: SlotProps<...> ⏐ undefined; body?: SlotProps<...> ⏐ undefined; row?: SlotProps<...> ⏐ undefined; rowCell?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `DataGridSlots` | `{}` | The components used for each slot inside. |
| **sortedColumns** | `readonly SortItem[]` | - | Array of sorted columns. |
| **sortMode** | ``"client" ⏐ "server"`` | `'client'` | Sort mode for the DataGrid: 'client' for client-side sorting or 'server' for server-side. Default is 'client'. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **total** | `number` | - | The total data of DataGrid. Default is length of data. You need pass it once you are using server side pagination. |
| **uniqueField** | `string` | `'id'` | The unique field for the DataGrid. Default is 'id'. |


## ColumnType
| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **field**         | `string`                                                | -              | The key that corresponds to the value in the data object.           |
| **header**        | `React.ReactNode ⏐ ((args: T) => React.ReactNode)`     | -              | The header title for the column, which can be a function.           |
| **cell**          | `(args: T) => React.ReactNode`                          | -              | A function for rendering the cell content.                          |
| **renderEditCell**| `(args: CellEditParas<T>) => React.ReactNode`           | -              | A function for rendering the editing cell.                          |
| **width**         | `number`                                                | `100`          | The width of the column in pixels.                                  |
| **minWidth**      | `number`                                                | `50`           | The minimum width of the column in pixels.                          |
| **maxWidth**      | `number`                                                | `Infinity`     | The maximum width of the column in pixels.                          |
| **flex**          | `number`                                                | -              | The flex grow factor for the column, determining how much it can grow relative to others. Must be a positive number. |
| **align**         | `GridAlignment`                                         | -              | Align cell content.                                                 |
| **headerAlign**   | `GridAlignment`                                         | -              | Align column header content.                                        |
| **visible**       | `boolean`                                               | `true`         | Indicate if current column is visible.                              |
| **sortable**      | `boolean`                                               | `true`         | Indicates whether the column is sortable.                           |
| **resizable**     | `boolean`                                               | `true`         | Indicates whether the column is resizable.                          |
| **editable**      | `boolean`                                               | `false`        | Indicates whether the column is editable.                           |
| **fixed**         | `left ⏐ right`                                          |  -             | Indicate if the column should fixed at `left` or `right`.           |
| **sortFn**        | `(d1: T, d2: T, sort: SortDirection) => number`        | -              | A custom sorting function for sorting the column data.             |
| **className**     | `string ⏐ ((args: T) => string)`                       | -              | Class name for the cell.                                           |
| **headerClassName**| `string`                                              | -              | Class name for the header cell.                                    |
| **style**         | `CSSProperties ⏐ ((args: T) => CSSProperties)`         | -              | Style for the column.                                              |
| **headerStyle**   | `CSSProperties`                                         | -              | Style for the header cell.                                        |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `DataGrid` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDataGrid-root | `'div'` | The component that renders the root of datagrid. |
| header | .NovaDataGrid-header | `'div'` | The component that renders the header of datagrid. |
| headerCell | .NovaDataGrid-headerCell | `'div'` | The component that renders the header cell of datagrid . |
| body | .NovaDataGrid-body | `'div'` | The component that renders the body of datagrid. |
| row | .NovaDataGrid-row | `'div'` | The component that renders the body row of datagrid. |
| rowCell | .NovaDataGrid-rowCell | `'div'` | The component that renders the body row cell of datagrid. |

## CSS classes

CSS classes for different states and variations of the `DataGrid` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaDataGrid-densityStandard | `densityStandard` | Class applied to the root element if `density="standard"`. |
| .NovaDataGrid-densityCompact | `densityCompact` | Class applied to the root element if `density="compact"`. |
| .NovaDataGrid-densityComfortable | `densityComfortable` | Class applied to the root element if `density="comfortable"`. |
| .NovaDataGrid-container | `container` | Class name applied to the container element |
| .NovaDataGrid-scrollArea | `scrollArea` | Class name applied to the scroll area element. |
| .NovaDataGrid-headerCellText | `headerCellText` | Class name applied to the header cell text element. |
| .NovaDataGrid-sortIcon | `sortIcon` | Class name applied to the header sort icon element. |
| .NovaDataGrid-sortAsc | `sortAsc` | Class name applied to the header sort icon element if the column is sorted in ascending order. |
| .NovaDataGrid-sortDesc | `sortDesc` | Class name applied to the header sort icon element if the column is sorted in descending order. |
| .NovaDataGrid-rowSelected | `rowSelected` | Class name applied to the body row element once row is selected. |
| .NovaDataGrid-rowEditing | `rowEditing` | Class name applied to the body row element once row is editing. |
| .NovaDataGrid-cell | `cell` | Class name applied to the cell elements. |
| .NovaDataGrid-cellSelected | `cellSelected` | Class name applied to the body cell element once cell is selected. |
| .NovaDataGrid-cellEditing | `cellEditing` | Class name applied to the body cell element once cell is editing. |
| .NovaDataGrid-cellFixed | `cellFixed` | Class name applied to the body cell element once cell is fixed. |
| .NovaDataGrid-cellFixedLeft | `cellFixedLeft` | Class name applied to the body cell element once cell is left fixed. |
| .NovaDataGrid-cellFixedRight | `cellFixedRight` | Class name applied to the body cell element once cell is right fixed. |
| .NovaDataGrid-emptyCell | `emptyCell` | Class name applied to the empty cell elements. |
| .NovaDataGrid-checkboxCell | `checkboxCell` | Class name applied to the checkbox cell elements. |
| .NovaDataGrid-radioCell | `radioCell` | Class name applied to the radio cell elements. |
| .NovaDataGrid-expandCell | `expandCell` | Class name applied to the expand cell elements. |
| .NovaDataGrid-rowExpandedPanel | `rowExpandedPanel` | Class name applied to the row expanded panel element. |
| .NovaDataGrid-scrollbar | `scrollbar` | Class name applied to the scrollbar element. |
| .NovaDataGrid-scrollHorizontal | `scrollHorizontal` | Class name applied to the horizontal scrollbar element. |
| .NovaDataGrid-scrollVertical | `scrollVertical` | Class name applied to the vertical scrollbar element. |
| .NovaDataGrid-pagination | `pagination` | Class name applied to the pagination container element. |
| .NovaDataGrid-columnResizeContainer | `columnResizeContainer` | Class name applied to the column resize container element. |
| .NovaDataGrid-columnResizeSlider | `columnResizeSlider` | Class name applied to the column resize slider element. |

