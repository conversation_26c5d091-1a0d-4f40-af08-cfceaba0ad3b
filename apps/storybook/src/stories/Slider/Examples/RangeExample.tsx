import { useState } from 'react';
import { Slider } from '@hxnova/react-components/Slider';

export default function RangeExample() {
  const [value, setValue] = useState<number[]>([-50, 0]);

  const handleChange = (event: Event, newValue: number | number[]) => {
    setValue(newValue as number[]);
  };

  return (
    <div sx={{ width: '100%' }}>
      <div sx={{ marginBottom: '16px' }}>
        <Slider
          value={value}
          onChange={handleChange}
          min={-100}
          max={100}
          step={10}
          marks={[
            { value: -100, label: '' },
            { value: 100, label: '' },
          ]}
          valueLabelDisplay="auto"
        />
      </div>
      <div sx={{ marginBottom: '16px' }}>
        <Slider
          value={value}
          onChange={handleChange}
          min={-100}
          max={100}
          disabled
          marks={[
            { value: -100, label: '' },
            { value: 100, label: '' },
          ]}
        />
      </div>
    </div>
  );
}
