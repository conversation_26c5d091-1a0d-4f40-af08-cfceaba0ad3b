import { Slider } from '@hxnova/react-components/Slider';

export default function DiscreteExample() {
  return (
    <div sx={{ width: '100%' }}>
      <div sx={{ marginBottom: '16px' }}>
        <Slider defaultValue={0} step={10} marks valueLabelDisplay="auto" />
      </div>
      <div sx={{ marginBottom: '16px' }}>
        <Slider aria-label="Small steps" defaultValue={40} step={10} marks valueLabelDisplay="auto" />
      </div>
      <div sx={{ marginBottom: '16px' }}>
        <Slider defaultValue={100} step={10} marks valueLabelDisplay="auto" />
      </div>
      <div sx={{ marginBottom: '16px' }}>
        <Slider defaultValue={50} disabled step={10} marks />
      </div>
    </div>
  );
}
