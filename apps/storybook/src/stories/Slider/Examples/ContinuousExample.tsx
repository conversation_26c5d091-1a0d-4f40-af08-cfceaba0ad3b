import { Slider } from '@hxnova/react-components/Slider';

export default function ContinuousExample() {
  return (
    <div sx={{ width: '100%' }}>
      <div sx={{ marginBottom: '16px' }}>
        <Slider
          defaultValue={0}
          onChange={(event, val) => console.log('val', val)}
          valueLabelDisplay="auto"
          marks={[{ value: 100, label: '' }]}
        />
      </div>
      <div sx={{ marginBottom: '16px' }}>
        <Slider
          defaultValue={50}
          onChange={(event, val) => console.log('val', val)}
          valueLabelDisplay="auto"
          marks={[{ value: 100, label: '' }]}
        />
      </div>
      <div sx={{ marginBottom: '16px' }}>
        <Slider defaultValue={100} onChange={(event, val) => console.log('val', val)} valueLabelDisplay="auto" />
      </div>
      <div sx={{ marginBottom: '16px' }}>
        <Slider
          defaultValue={30}
          onChange={(event, val) => console.log('val', val)}
          disabled
          marks={[{ value: 100, label: '' }]}
        />
      </div>
    </div>
  );
}
