import { Slider } from '@hxnova/react-components/Slider';

export default function CenteredExample() {
  return (
    <div sx={{ width: '100%' }}>
      <div sx={{ marginBottom: '16px' }}>
        <Slider
          track={false}
          defaultValue={0}
          min={-100}
          max={100}
          marks={[
            { value: -100, label: '' },
            { value: 0, label: '' },
            { value: 100, label: '' },
          ]}
          valueLabelDisplay="auto"
        />
      </div>
      <div sx={{ marginBottom: '16px' }}>
        <Slider
          track={false}
          defaultValue={50}
          min={-100}
          max={100}
          marks={[
            { value: -100, label: '' },
            { value: 0, label: '' },
            { value: 100, label: '' },
          ]}
          valueLabelDisplay="auto"
        />
      </div>
      <div sx={{ marginBottom: '16px' }}>
        <Slider
          defaultValue={0}
          min={-100}
          max={100}
          disabled
          track={false}
          marks={[
            { value: -100, label: '' },
            { value: 0, label: '' },
            { value: 100, label: '' },
          ]}
        />
      </div>
    </div>
  );
}
