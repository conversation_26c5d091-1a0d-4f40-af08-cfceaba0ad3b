# API Documentation

- [Slider](#slider)

# Slider

API reference docs for the React Slider component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Slider` component, you can choose to import it directly or through the main entry point.

```jsx
import { Slider } from '@hxnova/react-components/Slider';
// or
import { Slider } from '@hxnova/react-components';
```

## Props

The properties available for the `Slider` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **aria-label** | `string` | - | The label of the slider. |
| **aria-labelledby** | `string` | - | The id of the element containing a label for the slider. |
| **aria-valuetext** | `string` | - | A string value that provides a user-friendly name for the current value of the slider. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **defaultValue** | ``number ⏐ readonly number[]`` | - | The default value. Use when the component is not controlled. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **disableSwap** | ``false ⏐ true`` | `false` | If `true`, the active thumb doesn't swap when moving pointer over a thumb while dragging another thumb. |
| **getAriaLabel** | ``(index: number) => string`` | - | Accepts a function which returns a string value that provides a user-friendly name for the thumb labels of the slider.<br>This is important for screen reader users.<br>@param index The thumb label's index to format.<br>@returns |
| **getAriaValueText** | ``(value: number, index: number) => string`` | - | Accepts a function which returns a string value that provides a user-friendly name for the current value of the slider.<br>This is important for screen reader users.<br>@param value The thumb label's value to format.<br>@param index The thumb label's index to format.<br>@returns |
| **isRtl** | ``false ⏐ true`` | `false` | If `true` the Slider will be rendered right-to-left (with the lowest value on the right-hand side). |
| **marks** | ``false ⏐ true ⏐ readonly Mark[]`` | `false` | Marks indicate predetermined values to which the user can move the slider.<br>If `true` the marks are spaced according the value of the `step` prop.<br>If an array, it should contain objects with `value` and an optional `label` keys. |
| **max** | `number` | `100` | The maximum allowed value of the slider.<br>Should not be equal to min. |
| **min** | `number` | `0` | The minimum allowed value of the slider.<br>Should not be equal to max. |
| **name** | `string` | - | Name attribute of the hidden `input` element. |
| **onChange** | ``(event: Event, value: number ⏐ number[], activeThumb: number) => void`` | - | Callback function that is fired when the slider's value changed.<br>@param event The event source of the callback.<br>You can pull out the new value by accessing `event.target.value` (any).<br>**Warning**: This is a generic event not a change event.<br>@param value The new value.<br>@param activeThumb Index of the currently moved thumb. |
| **onChangeCommitted** | ``(event: Event ⏐ SyntheticEvent<Element, Event>, value: number ⏐ number[]) => void`` | - | Callback function that is fired when the `mouseup` is triggered.<br>@param event The event source of the callback. **Warning**: This is a generic event not a change event.<br>@param value The new value. |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'horizontal'` | The component orientation. |
| **rootRef** | ``Ref<Element>`` | - | The ref attached to the root of the Slider. |
| **scale** | ``(value: number) => number`` | `function Identity(x) { return x; }` | A transformation function, to change the scale of the slider.<br>@param x<br>@returns |
| **shiftStep** | `number` | `10` | The granularity with which the slider can step through values when using Page Up/Page Down or Shift + Arrow Up/Arrow Down. |
| **slotProps** | ``{ root?: SlotProps<"span", Record<string, never>, SliderOwnerState> ⏐ undefined; track?: SlotProps<"span", Record<string, never>, SliderOwnerState> ⏐ undefined; ... 5 more ...; input?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `SliderSlots` | `{}` | The components used for each slot inside. |
| **step** | ``null ⏐ number`` | `1` | The granularity with which the slider can step through values. (A "discrete" slider.)<br>The `min` prop serves as the origin for the valid values.<br>We recommend (max - min) to be evenly divisible by the step.<br>When step is `null`, the thumb can only be slid onto marks provided with the `marks` prop. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **tabIndex** | `number` | - | Tab index attribute of the hidden `input` element. |
| **track** | ``false ⏐ "normal" ⏐ "inverted"`` | `'normal'` | The track presentation:<br>- `normal` the track will render a bar representing the slider value.<br>- `inverted` the track will render a bar representing the remaining slider value.<br>- `false` the track will render without a bar. |
| **value** | ``number ⏐ readonly number[]`` | - | The value of the slider.<br>For ranged sliders, provide an array with two values. |
| **valueLabelDisplay** | ``"on" ⏐ "auto" ⏐ "off"`` | `'off'` | Controls when the value label is displayed:<br>- `auto` the value label will display when the thumb is hovered or focused.<br>- `on` will display persistently.<br>- `off` will never display. |
| **valueLabelFormat** | ``string ⏐ ((value: number, index: number) => ReactNode)`` | `function Identity(x) { return x; }` | The format function the value label's value.<br>When a function is provided, it should have the following signature:<br>- {number} value The value label's value to format<br>- {number} index The value label's index to format<br>@param x<br>@returns |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Slider` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaSlider-root | `'span'` | The component that renders the root. |
| track | .NovaSlider-track | `'span'` | The component that renders the track. |
| rail | .NovaSlider-rail | `'span'` | The component that renders the rail. |
| thumb | .NovaSlider-thumb | `'span'` | The component that renders the thumb. |
| mark | .NovaSlider-mark | `'span'` | The component that renders the mark. |
| markLabel | .NovaSlider-markLabel | `'span'` | The component that renders the mark label. |
| valueLabel | .NovaSlider-valueLabel | `'span'` | The component that renders the value label. |
| input | .NovaSlider-input | `'input'` | The component that renders the input. |

## CSS classes

CSS classes for different states and variations of the `Slider` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaSlider-marked | `marked` | Class name applied to the root element if `marks` is provided with at least one label. |
| .NovaSlider-vertical | `vertical` | Class name applied to the root element if `orientation="vertical"`. |
| .Nova-disabled | `disabled` | State class applied to the root and thumb element if `disabled={true}`. |
| .NovaSlider-dragging | `dragging` | State class applied to the root if a thumb is being dragged. |
| .NovaSlider-trackFalse | `trackFalse` | Class name applied to the root element if `track={false}`. |
| .NovaSlider-trackInverted | `trackInverted` | Class name applied to the root element if `track="inverted"`. |
| .Nova-active | `active` | State class applied to the thumb element if it's active. |
| .Nova-focusVisible | `focusVisible` | State class applied to the thumb element if keyboard focused. |
| .NovaSlider-markActive | `markActive` | Class name applied to the mark element if active (depending on the value). |
| .NovaSlider-markLabelActive | `markLabelActive` | Class name applied to the mark label element if active (depending on the value). |
| .NovaSlider-valueLabelOpen | `valueLabelOpen` | Class name applied to the thumb label element if it's open. |

