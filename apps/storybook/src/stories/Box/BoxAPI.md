# API Documentation

- [Box](#box)

# Box

API reference docs for the React Box component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Box` component, you can choose to import it directly or through the main entry point.

```jsx
import { Box } from '@hxnova/react-components/Box';
// or
import { Box } from '@hxnova/react-components';
```

## Props

The properties available for the `Box` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

CSS classes for different states and variations of the `Box` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaBox-root | `root` | Class name applied to the root element. |

