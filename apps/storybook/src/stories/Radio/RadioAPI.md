# API Documentation

- [Radio](#radio)
- [RadioGroup](#radiogroup)

# Radio

API reference docs for the React Radio component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Radio` component, you can choose to import it directly or through the main entry point.

```jsx
import { Radio } from '@hxnova/react-components/Radio';
// or
import { Radio } from '@hxnova/react-components';
```

## Props

The properties available for the `Radio` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **checked** | ``false ⏐ true`` | - | If `true`, the component is checked. |
| **checkedIcon** | `ReactNode` | - | The icon to display when the component is checked. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **defaultChecked** | ``false ⏐ true`` | - | The default checked state. Use when the component is not controlled. |
| **disabled** | ``false ⏐ true`` | - | If `true`, the component is disabled. |
| **disableIcon** | ``false ⏐ true`` | `false` | If `true`, the checked icon is removed and the selected variant is applied on the `action` element instead. |
| **id** | `string` | - | The id of the `input` element. |
| **label** | `ReactNode` | - | The label element at the end the radio. |
| **name** | `string` | - | The `name` attribute of the input. |
| **onBlur** | ``FocusEventHandler<Element>`` | - |  |
| **onChange** | ``ChangeEventHandler<HTMLInputElement>`` | - | Callback fired when the state is changed.<br>@param event The event source of the callback.<br>You can pull out the new value by accessing `event.target.value` (string).<br>You can pull out the new checked state by accessing `event.target.checked` (boolean). |
| **onFocus** | ``FocusEventHandler<Element>`` | - |  |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **overlay** | ``false ⏐ true`` | `false` | If `true`, the root element's position is set to initial which allows the action area to fill the nearest positioned parent.<br>This prop is useful for composing Radio with ListItem component. |
| **readOnly** | ``false ⏐ true`` | - | If `true`, the component is read only. |
| **required** | ``false ⏐ true`` | - | If `true`, the `input` element is required. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | ``{ root?: SlotProps<"span", object, RadioOwnerState> ⏐ undefined; container?: SlotProps<"span", object, RadioOwnerState> ⏐ undefined; ... 4 more ...; label?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `RadioSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **uncheckedIcon** | `ReactNode` | - | The icon to display when the component is not checked. |
| **value** | `unknown` | - | The value of the component. The DOM API casts this to a string. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Radio` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaRadio-root | `'span'` | The component that renders the root. |
| container | .NovaRadio-container | `'span'` | The component that renders the container. |
| radio | .NovaRadio-radio | `'span'` | The component that renders the radio. |
| icon | .NovaRadio-icon | `'span'` | The component that renders the icon. |
| action | .NovaRadio-action | `'span'` | The component that renders the action. |
| input | .NovaRadio-input | `'input'` | The component that renders the input. |
| label | .NovaRadio-label | `'label'` | The component that renders the label. |

## CSS classes

CSS classes for different states and variations of the `Radio` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-checked | `checked` | State class applied to the root, action slots if `checked`. |
| .Nova-disabled | `disabled` | State class applied to the root, action slots if `disabled`. |
| .Nova-focusVisible | `focusVisible` | Class name applied to the root element if the switch has visible focus |
| .NovaRadio-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaRadio-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaRadio-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |

<br><br>

# RadioGroup

API reference docs for the React RadioGroup component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `RadioGroup` component, you can choose to import it directly or through the main entry point.

```jsx
import { RadioGroup } from '@hxnova/react-components/RadioGroup';
// or
import { RadioGroup } from '@hxnova/react-components';
```

## Props

The properties available for the `RadioGroup` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **defaultValue** | `any` | - | The default value. Use when the component is not controlled. |
| **disableIcon** | ``false ⏐ true`` | `false` | The radio's `disabledIcon` prop. If specified, the value is passed down to every radios under this element. |
| **name** | `string` | - | The name used to reference the value of the control.<br>If you don't provide this prop, it falls back to a randomly generated name. |
| **onChange** | ``(event: ChangeEvent<HTMLInputElement>) => void`` | - | Callback fired when a radio button is selected.<br>@param event The event source of the callback.<br>You can pull out the new value by accessing `event.target.value` (string). |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'vertical'` | The component orientation. |
| **overlay** | ``false ⏐ true`` | `false` | The radio's `overlay` prop. If specified, the value is passed down to every radios under this element. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, RadioGroupOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `RadioGroupSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | `any` | - | Value of the selected radio button. The DOM API casts this to a string. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `RadioGroup` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaRadioGroup-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `RadioGroup` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaRadioGroup-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaRadioGroup-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaRadioGroup-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |
| .NovaRadioGroup-horizontal | `horizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaRadioGroup-vertical | `vertical` | Class name applied to the root element if `orientation="vertical"`. |

