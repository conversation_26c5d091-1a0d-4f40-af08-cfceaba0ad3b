import * as React from 'react';
import { Button } from '@hxnova/react-components/Button';
import { Alert } from '@hxnova/react-components/Alert';
import { Snackbar, SnackbarOrigin } from '@hxnova/react-components/Snackbar';

interface State extends SnackbarOrigin {
  open: boolean;
}

export default function Demo() {
  const [state, setState] = React.useState<State>({
    open: false,
    vertical: 'top',
    horizontal: 'center',
  });
  const { vertical, horizontal, open } = state;

  const handleClick = (newState: SnackbarOrigin) => () => {
    setState({ ...newState, open: true });
  };

  const handleClose = () => {
    setState({ ...state, open: false });
  };

  return (
    <div>
      <div sx={{ display: 'flex', flexDirection: 'row', gap: '0.5rem' }}>
        <Button onClick={handleClick({ vertical: 'top', horizontal: 'left' })}>Top-Left</Button>
        <Button onClick={handleClick({ vertical: 'top', horizontal: 'center' })}>Top-Center</Button>
        <Button onClick={handleClick({ vertical: 'top', horizontal: 'right' })}>Top-Right</Button>
        <Button onClick={handleClick({ vertical: 'bottom', horizontal: 'left' })}>Bottom-Left</Button>
        <Button onClick={handleClick({ vertical: 'bottom', horizontal: 'center' })}>Bottom-Center</Button>
        <Button onClick={handleClick({ vertical: 'bottom', horizontal: 'right' })}>Bottom-Right</Button>
      </div>
      <Snackbar open={open} anchorOrigin={{ vertical, horizontal }} onClose={handleClose} key={vertical + horizontal}>
        <Alert onClose={handleClose}>This is a message</Alert>
      </Snackbar>
    </div>
  );
}
