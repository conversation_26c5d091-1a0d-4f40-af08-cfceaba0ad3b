import { Alert } from '@hxnova/react-components/Alert';

export default function Demo() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <div sx={{ display: 'flex', width: '30%', flexDirection: 'column', gap: '1rem' }}>
        <Alert intensity="bold" color="primary">
          This is a message
        </Alert>
        <Alert intensity="bold" color="error">
          This is a message
        </Alert>
        <Alert intensity="bold" color="warning">
          This is a message
        </Alert>
        <Alert intensity="bold" color="info">
          This is a message
        </Alert>
        <Alert intensity="bold" color="success">
          This is a message
        </Alert>
      </div>
      <div sx={{ display: 'flex', width: '30%', flexDirection: 'column', gap: '1rem' }}>
        <Alert intensity="subtle" color="primary">
          This is a message
        </Alert>
        <Alert intensity="subtle" color="error">
          This is a message
        </Alert>
        <Alert intensity="subtle" color="warning">
          This is a message
        </Alert>
        <Alert intensity="subtle" color="info">
          This is a message
        </Alert>
        <Alert intensity="subtle" color="success">
          This is a message
        </Alert>
      </div>
    </div>
  );
}
