# API Documentation

- [Alert](#alert)
- [Snackbar](#snackbar)

# Alert

API reference docs for the React Alert component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Alert` component, you can choose to import it directly or through the main entry point.

```jsx
import { Alert } from '@hxnova/react-components/Alert';
// or
import { Alert } from '@hxnova/react-components';
```

## Props

The properties available for the `Alert` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``{ onClick: () => void; label: string; }`` | - | The action of alert. It will be presented as a button. |
| **color** | ``"primary" ⏐ "error" ⏐ "info" ⏐ "warning" ⏐ "success"`` | `'primary'` | The system color of Alert |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **intensity** | ``"bold" ⏐ "subtle"`` | `'bold'` | The intensity of Alert |
| **onClose** | ``(event: SyntheticEvent<Element, Event>) => void`` | - | Callback fired when the component requests to be closed.<br>When provided, a close icon button is displayed that triggers the callback when clicked.<br>@param event The event source of the callback. |
| **orientation** | ``"vertical" ⏐ "horizontal"`` | `'horizontal'` | The content direction flow, if `vertical`, the endDecorator will be placed below the text. |
| **slotProps** | ``{ root?: SlotProps<"div", object, AlertOwnerState> ⏐ undefined; startDecorator?: SlotProps<"span", object, AlertOwnerState> ⏐ undefined; action?: SlotProps<...> ⏐ undefined; closeButton?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `AlertSlots` | `{}` | The components used for each slot inside. |
| **startDecorator** | `ReactNode` | - | Element placed before the children. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Alert` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAlert-root | `'div'` | The component that renders the root. |
| startDecorator | .NovaAlert-startDecorator | `'span'` | The component that renders the start decorator. |
| action | .NovaAlert-action | `'span'` | The component that renders the action. |
| closeButton | .NovaAlert-closeButton | `'button'` | The component that renders the close button. |

## CSS classes

CSS classes for different states and variations of the `Alert` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaAlert-colorPrimary | `colorPrimary` | Class name applied to the root element if `color="primary"`. |
| .NovaAlert-colorError | `colorError` | Class name applied to the root element if `color="error"`. |
| .NovaAlert-colorInfo | `colorInfo` | Class name applied to the root element if `color="info"`. |
| .NovaAlert-colorWarning | `colorWarning` | Class name applied to the root element if `color="warning"`. |
| .NovaAlert-colorSuccess | `colorSuccess` | Class name applied to the root element if `color="success"`. |
| .NovaAlert-intensityBold | `intensityBold` | Class name applied to the root element if `intensity="bold"`. |
| .NovaAlert-intensitySubtle | `intensitySubtle` | Class name applied to the root element if `intensity="subtle"`. |
| .NovaAlert-horizontal | `horizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaAlert-vertical | `vertical` | Class name applied to the root element if `orientation="vertical"`. |
| .NovaAlert-endDecorator | `endDecorator` | Class name applied to the endDecorator element |
| .NovaAlert-content | `content` | Class name applied to the content element |
| .NovaAlert-message | `message` | Class name applied to the message element |
| .NovaAlert-closeIcon | `closeIcon` | Class name applied to the close icon element |

<br><br>

# Snackbar

API reference docs for the React Snackbar component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Snackbar` component, you can choose to import it directly or through the main entry point.

```jsx
import { Snackbar } from '@hxnova/react-components/Snackbar';
// or
import { Snackbar } from '@hxnova/react-components';
```

## Props

The properties available for the `Snackbar` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **anchorOrigin** | `SnackbarOrigin` | `{ vertical: 'bottom', horizontal: 'center' }` | The anchor of the `Snackbar`.<br>On smaller screens, the component grows to occupy all the available width,<br>the horizontal alignment is ignored. |
| **animationDuration** | `number` | `300` | The duration of the animation in milliseconds. This value is used to control<br>the length of time it takes for an animation to complete one cycle. It is also<br>utilized for delaying the unmount of the component.<br>Provide this value if you have your own animation so that we can precisely<br>time the component's unmount to match your custom animation. |
| **autoHideDuration** | ``null ⏐ number`` | `null` | The number of milliseconds to wait before automatically calling the<br>`onClose` function. `onClose` should then set the state of the `open`<br>prop to hide the Snackbar. This behavior is disabled by default with<br>the `null` value. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disableWindowBlurListener** | ``false ⏐ true`` | `false` | If `true`, the `autoHideDuration` timer will expire even if the window is not focused. |
| **onClose** | ``(event: Event ⏐ SyntheticEvent<any, Event>, reason: SnackbarCloseReason) => void`` | - | Callback fired when the component requests to be closed.<br>Typically `onClose` is used to set state in the parent component,<br>which is used to control the `Snackbar` `open` prop.<br>The `reason` parameter can optionally be used to control the response to `onClose`,<br>for example ignoring `clickaway`.<br>@param event The event source of the callback.<br>@param reason Can be: `"timeout"` (`autoHideDuration` expired), `"clickaway"`, or `"escapeKeyDown"`. |
| **onUnmount** | ``() => void`` | - | A callback fired when the component is about to be unmounted. |
| **open** | ``false ⏐ true`` | - | If `true`, the component is shown. |
| **resumeHideDuration** | `number` | - | The number of milliseconds to wait before dismissing after user interaction.<br>If `autoHideDuration` prop isn't specified, it does nothing.<br>If `autoHideDuration` prop is specified but `resumeHideDuration` isn't,<br>we default to `autoHideDuration / 2` ms. |
| **slotProps** | ``{ root?: SlotProps<"div", object, SnackbarOwnerState> ⏐ undefined; clickAway?: ClickAwayListenerProps ⏐ ((ownerState: SnackbarOwnerState) => ClickAwayListenerProps) ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `SnackbarSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Snackbar` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaSnackbar-root | `'div'` | The component that renders the root. |
| clickAway | .NovaSnackbar-clickAway | `ClickAwayListener` | The component that renders the click away. |

## CSS classes

CSS classes for different states and variations of the `Snackbar` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaSnackbar-anchorOriginTopLeft | `anchorOriginTopLeft` | Class name applied to the root element if `anchorOrigin={{ 'top', 'left' }}`. |
| .NovaSnackbar-anchorOriginTopCenter | `anchorOriginTopCenter` | Class name applied to the root element if `anchorOrigin={{ 'top', 'center' }}`. |
| .NovaSnackbar-anchorOriginTopRight | `anchorOriginTopRight` | Class name applied to the root element if `anchorOrigin={{ 'top', 'right' }}`. |
| .NovaSnackbar-anchorOriginBottomLeft | `anchorOriginBottomLeft` | Class name applied to the root element if `anchorOrigin={{ 'bottom', 'left' }}`. |
| .NovaSnackbar-anchorOriginBottomCenter | `anchorOriginBottomCenter` | Class name applied to the root element if `anchorOrigin={{ 'bottom', 'center' }}`. |
| .NovaSnackbar-anchorOriginBottomRight | `anchorOriginBottomRight` | Class name applied to the root element if `anchorOrigin={{ 'bottom', 'right' }}`. |

