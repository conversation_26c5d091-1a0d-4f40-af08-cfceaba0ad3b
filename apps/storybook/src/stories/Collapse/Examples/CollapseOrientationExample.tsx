import React from 'react';
import { Collapse } from '@hxnova/react-components/Collapse';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function CollapseOrientationExample() {
  const [checkedVertical, setCheckedVertical] = React.useState(false);
  const [checkedHorizontal, setCheckedHorizontal] = React.useState(false);

  const handleVerticalChange = () => {
    setCheckedVertical((prev) => !prev);
  };

  const handleHorizontalChange = () => {
    setCheckedHorizontal((prev) => !prev);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Vertical Collapse */}
      <Box>
        <Typography variant="titleMedium" sx={{ marginBottom: '16px' }}>
          Vertical Collapse (Default)
        </Typography>
        <Switch checked={checkedVertical} onChange={handleVerticalChange} endDecorator="Show Vertical Content" />
        <Collapse in={checkedVertical} orientation="vertical">
          <Box
            sx={{
              margin: '8px',
              padding: '8px',
              border: '1px solid var(--palette-outlineVariant)',
              borderRadius: 'var(--radius-2xs)',
              backgroundColor: 'var(--palette-primaryContainer)',
              width: '300px',
            }}
          >
            <Typography sx={{ color: 'var(--palette-onPrimaryContainer)' }}>
              This content collapses vertically (height animation). The default orientation is vertical.
            </Typography>
          </Box>
        </Collapse>
      </Box>

      {/* Horizontal Collapse */}
      <Box>
        <Typography variant="titleMedium" sx={{ marginBottom: '16px' }}>
          Horizontal Collapse
        </Typography>
        <Switch checked={checkedHorizontal} onChange={handleHorizontalChange} endDecorator="Show Horizontal Content" />
        <Collapse in={checkedHorizontal} orientation="horizontal">
          <Box
            sx={{
              margin: '8px',
              padding: '8px',
              border: '1px solid var(--palette-outlineVariant)',
              borderRadius: 'var(--radius-2xs)',
              backgroundColor: 'var(--palette-secondaryContainer)',
              height: '100px',
              whiteSpace: 'nowrap',
            }}
          >
            <Typography>This content collapses horizontally (width animation).</Typography>
          </Box>
        </Collapse>
      </Box>
    </Box>
  );
}
