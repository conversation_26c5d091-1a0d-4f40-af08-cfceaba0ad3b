import React from 'react';
import { Collapse } from '@hxnova/react-components/Collapse';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function CollapseCollapsedSizeExample() {
  const [checked, setChecked] = React.useState(false);

  const handleChange = () => {
    setChecked((prev) => !prev);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <Typography variant="titleMedium" sx={{ marginBottom: '16px' }}>
        Collapse with Different Collapsed Sizes
      </Typography>

      <Switch checked={checked} onChange={handleChange} endDecorator="Toggle Collapse" />

      <Box sx={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
        {/* Default collapsedSize (0px) */}
        <Box sx={{ flex: 1, minWidth: '200px' }}>
          <Typography variant="labelMedium" sx={{ marginBottom: '4px' }}>
            Default (0px)
          </Typography>
          <Collapse in={checked}>
            <Box
              sx={{
                padding: '8px',
                border: '1px solid var(--palette-outlineVariant)',
                borderRadius: 'var(--radius-2xs)',
                backgroundColor: 'var(--palette-surfaceContainer)',
                height: '120px',
              }}
            >
              <Typography>This content completely collapses to 0px height when closed.</Typography>
            </Box>
          </Collapse>
        </Box>

        {/* collapsedSize 40px */}
        <Box sx={{ flex: 1, minWidth: '200px' }}>
          <Typography variant="labelMedium" sx={{ marginBottom: '4px' }}>
            Collapsed Size: 40px
          </Typography>
          <Collapse in={checked} collapsedSize="40px">
            <Box
              sx={{
                padding: '8px',
                border: '1px solid var(--palette-outlineVariant)',
                borderRadius: 'var(--radius-2xs)',
                backgroundColor: 'var(--palette-secondaryContainer)',
                height: '120px',
              }}
            >
              <Typography>This content maintains a minimum height of 40px when collapsed.</Typography>
            </Box>
          </Collapse>
        </Box>

        {/* collapsedSize 80px */}
        <Box sx={{ flex: 1, minWidth: '200px' }}>
          <Typography variant="labelMedium" sx={{ marginBottom: '4px' }}>
            Collapsed Size: 80px
          </Typography>
          <Collapse in={checked} collapsedSize="80px">
            <Box
              sx={{
                padding: '8px',
                border: '1px solid var(--palette-outlineVariant)',
                borderRadius: 'var(--radius-2xs)',
                backgroundColor: 'var(--palette-primaryContainer)',
                height: '120px',
              }}
            >
              <Typography sx={{ color: 'var(--palette-onPrimaryContainer)' }}>
                This content maintains a minimum height of 80px when collapsed.
              </Typography>
            </Box>
          </Collapse>
        </Box>
      </Box>
    </Box>
  );
}
