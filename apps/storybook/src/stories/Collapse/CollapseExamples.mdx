import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import CollapseBasicExample from './Examples/CollapseBasicExample';
import CollapseBasicExampleSource from './Examples/CollapseBasicExample.tsx?raw';
import CollapseOrientationExample from './Examples/CollapseOrientationExample';
import CollapseOrientationExampleSource from './Examples/CollapseOrientationExample.tsx?raw';
import CollapseCollapsedSizeExample from './Examples/CollapseCollapsedSizeExample';
import CollapseCollapsedSizeExampleSource from './Examples/CollapseCollapsedSizeExample.tsx?raw';

<Meta title="@hxnova/react-components/Transitions/Collapse/Examples" />

## Basic Collapse

The Collapse component lets you transition the height (or width) of its child components. The **`in` prop** controls the collapsed state, and the transition animates smoothly between visible and hidden states. Use the **`timeout` prop** to control animation duration - set it to `"auto"` for automatic duration calculation based on content size, or specify a number in milliseconds (e.g., `timeout={300}`).

<div className="sb-unstyled">
  <CollapseBasicExample />
</div>
<CodeExpand code={CollapseBasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Collapse Orientation

The Collapse component supports both vertical (default) and horizontal orientations using the **`orientation` prop**. Vertical collapse animates the height, while horizontal collapse animates the width.

<div className="sb-unstyled">
  <CollapseOrientationExample />
</div>
<CodeExpand code={CollapseOrientationExampleSource} showBorderTop style={{marginTop: 16}}/>

## Collapsed Size

You can control the minimum size when collapsed using the **`collapsedSize` prop**. This is useful when you want to show a preview or partial content even when collapsed.

<div className="sb-unstyled">
  <CollapseCollapsedSizeExample />
</div>
<CodeExpand code={CollapseCollapsedSizeExampleSource} showBorderTop style={{marginTop: 16}}/>
