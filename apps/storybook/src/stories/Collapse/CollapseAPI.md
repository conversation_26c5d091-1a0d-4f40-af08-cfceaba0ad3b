# API Documentation

- [Collapse](#collapse)

# Collapse

API reference docs for the React Collapse component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Collapse` component, you can choose to import it directly or through the main entry point.

```jsx
import { Collapse } from '@hxnova/react-components/Collapse';
// or
import { Collapse } from '@hxnova/react-components';
```

## Props

The properties available for the `Collapse` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content node to be collapsed. |
| **classes** | ``Partial<CollapseClasses> & Partial<ClassNameMap<never>>`` | - | Override or extend the styles applied to the component. |
| **className** | `string` | - |  |
| **collapsedSize** | ``string ⏐ number`` | `'0px'` | The width (horizontal) or height (vertical) of the container when collapsed. |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **easing** | ``string ⏐ { enter?: string ⏐ undefined; exit?: string ⏐ undefined; }`` | - | The transition timing function.<br>You may specify a single easing or a object containing enter and exit values. |
| **in** | ``false ⏐ true`` | - | If `true`, the component will transition in.<br>Show the component; triggers the enter or exit states |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'vertical'` | The transition orientation. |
| **ref** | ``Ref<unknown>`` | - |  |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **timeout** | ``number ⏐ { appear?: number ⏐ undefined; enter?: number ⏐ undefined; exit?: number ⏐ undefined; } ⏐ { appear?: number ⏐ undefined; enter?: number ⏐ undefined; exit?: number ⏐ undefined; } ⏐ "auto"`` | `duration.standard` | The duration for the transition, in milliseconds.<br>You may specify a single timeout for all transitions, or individually with an object.<br>Set to 'auto' to automatically calculate transition time based on height. |
| **TransitionComponent** | ``ComponentClass<any, any> ⏐ FunctionComponent<any>`` | `Transition` | The component used for the transition. |

## CSS classes

CSS classes for different states and variations of the `Collapse` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCollapse-root | `root` | Styles applied to the root element. |
| .NovaCollapse-horizontal | `horizontal` | State class applied to the root element if `orientation="horizontal"`. |
| .NovaCollapse-entered | `entered` | Styles applied to the root element when the transition has entered. |
| .NovaCollapse-hidden | `hidden` | Styles applied to the root element when the transition has exited and `collapsedSize` = 0px. |
| .NovaCollapse-wrapper | `wrapper` | Styles applied to the outer wrapper element. |
| .NovaCollapse-wrapperInner | `wrapperInner` | Styles applied to the inner wrapper element. |

