# API Documentation

- [ToggleIconButton](#toggleiconbutton)

# ToggleIconButton

API reference docs for the React ToggleIconButton component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `ToggleIconButton` component, you can choose to import it directly or through the main entry point.

```jsx
import { ToggleIconButton } from '@hxnova/react-components/ToggleIconButton';
// or
import { ToggleIconButton } from '@hxnova/react-components';
```

## Props

The properties available for the `ToggleIconButton` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``Ref<ButtonActions>`` | - | A ref for imperative actions. It currently only supports `focusVisible()` action. |
| **className** | `string` | - |  |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **endIcon** | `ReactNode` | - | An optional icon to show at the end of the button |
| **focusableWhenDisabled** | ``false ⏐ true`` | `false` | If `true`, allows a disabled button to receive focus. |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the button will take up the full width of its container. |
| **href** | `string` | - |  |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **ref** | ``((instance: HTMLButtonElement ⏐ null) => void) ⏐ RefObject<HTMLButtonElement> ⏐ null`` | - |  |
| **rootElementName** | `keyof HTMLElementTagNameMap` | `'button'` | The HTML element that is ultimately rendered, for example 'button' or 'a' |
| **selected** | ``false ⏐ true`` | `false` | Whether the component is selected |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The overall size of the button. |
| **slotProps** | ``{ root?: SlotComponentProps<"button", ButtonRootSlotPropsOverrides, { action?: Ref<ButtonActions>; children?: ReactNode; ... 13 more ...; focusVisible: boolean; }> ⏐ undefined; } ⏐ undefined`` | `{}` | The props used for each slot inside the Button. |
| **slots** | `ButtonSlots` | `{}` | The components used for each slot inside the Button.<br>Either a string to use a HTML element or a component. |
| **startIcon** | `ReactNode` | - | An optional icon to show at the start of the button |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **tabIndex** | `number` | - |  |
| **to** | `string` | - |  |
| **type** | ``"button" ⏐ "submit" ⏐ "reset"`` | `'button'` | Type attribute applied when the `component` is `button`. |
| **variant** | ``"filled" ⏐ "outlined" ⏐ "standard" ⏐ "neutral"`` | `'filled'` | The style of toggle icon button to use |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `ToggleIconButton` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaToggleIconButton-root | ``props.href ⏐⏐ props.to ? 'a' : 'button'`` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `ToggleIconButton` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaToggleIconButton-filled | `filled` | Class name applied to the ToggleIconButton if `variant="filled"`. |
| .NovaToggleIconButton-outlined | `outlined` | Class name applied to the ToggleIconButton if `variant="outlined"`. |
| .NovaToggleIconButton-standard | `standard` | Class name applied to the ToggleIconButton if `variant="standard"`. |
| .NovaToggleIconButton-neutral | `neutral` | Class name applied to the ToggleIconButton if `variant="neutral"`. |
| .Nova-selected | `selected` | Class name applied to the ToggleIconButton if `selected={true}`. |

