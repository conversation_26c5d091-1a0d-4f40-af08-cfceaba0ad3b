import * as React from 'react';
import { ToggleIconButton } from '@hxnova/react-components/ToggleIconButton';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  const [selected, setSelected] = React.useState(false);
  const toggleSelected = () => setSelected(!selected);
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <ToggleIconButton onClick={toggleSelected} selected={selected}>
        <Icon family="material" name="settings" />
      </ToggleIconButton>
    </div>
  );
}
