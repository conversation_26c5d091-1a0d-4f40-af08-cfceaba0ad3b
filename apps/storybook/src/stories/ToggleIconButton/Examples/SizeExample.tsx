import { ToggleIconButton } from '@hxnova/react-components/ToggleIconButton';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <ToggleIconButton size={'small'}>
        <Icon family="material" name="settings" />
      </ToggleIconButton>
      <ToggleIconButton size={'medium'}>
        <Icon family="material" name="settings" />
      </ToggleIconButton>
      <ToggleIconButton size={'large'}>
        <Icon family="material" name="settings" />
      </ToggleIconButton>
    </div>
  );
}
