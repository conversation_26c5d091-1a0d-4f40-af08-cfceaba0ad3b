import { ToggleIconButton } from '@hxnova/react-components/ToggleIconButton';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <ToggleIconButton variant={'filled'}>
        <Icon family="material" name="settings" />
      </ToggleIconButton>
      <ToggleIconButton variant={'outlined'}>
        <Icon family="material" name="settings" />
      </ToggleIconButton>
      <ToggleIconButton variant={'standard'}>
        <Icon family="material" name="settings" />
      </ToggleIconButton>
      <ToggleIconButton variant={'neutral'}>
        <Icon family="material" name="settings" />
      </ToggleIconButton>
    </div>
  );
}
