# API Documentation

- [Popover](#popover)

# Popover

API reference docs for the React Popover component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Popover` component, you can choose to import it directly or through the main entry point.

```jsx
import { Popover } from '@hxnova/react-components/Popover';
// or
import { Popover } from '@hxnova/react-components';
```

## Props

The properties available for the `Popover` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children*** | `ReactNode` | - | The trigger element that will open the popover when clicked. |
| **className** | `string` | - | Class name applied to the root element. |
| **content** | `ReactNode` | - | Custom content to be displayed in the popover.<br>If provided, it will replace the default title and description layout. |
| **description** | `ReactNode` | - | The description content to be displayed in the popover. |
| **modal** | ``false ⏐ true`` | `false` | Whether the popover should be modal (trap focus). |
| **placement** | ``"top" ⏐ "right" ⏐ "bottom" ⏐ "left"`` | `'bottom'` | The placement of the popover relative to the trigger element. |
| **showArrow** | ``false ⏐ true`` | `true` | Whether to show the arrow. |
| **showBackdrop** | ``false ⏐ true`` | `false` | Whether to show the backdrop. |
| **showClose** | ``false ⏐ true`` | `false` | Whether to show the close button. |
| **slotProps** | ``{ root?: Omit<Props, "children">; trigger?: Omit<Props & RefAttributes<any>, "children"> ⏐ undefined; portal?: Omit<Props, "children"> ⏐ undefined; ... 6 more ...; close?: Omit<...> ⏐ undefined; } ⏐ undefined`` | - | Props passed to the sub-components. |
| **title** | `ReactNode` | - | The title to be displayed in the popover. |

## CSS classes

CSS classes for different states and variations of the `Popover` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaPopover-root | `root` | Class name applied to the root element. |
| .NovaPopover-trigger | `trigger` | Class name applied to the trigger element. |
| .NovaPopover-portal | `portal` | Class name applied to the portal element. |
| .NovaPopover-positioner | `positioner` | Class name applied to the positioner element. |
| .NovaPopover-popup | `popup` | Class name applied to the popup element. |
| .NovaPopover-arrow | `arrow` | Class name applied to the arrow element. |
| .NovaPopover-backdrop | `backdrop` | Class name applied to the backdrop element. |
| .NovaPopover-title | `title` | Class name applied to the title element. |
| .NovaPopover-description | `description` | Class name applied to the description element. |
| .NovaPopover-close | `close` | Class name applied to the close button element. |
| .Nova-open | `open` | Class name applied to the root element when the popover is open. |
| .NovaPopover-closed | `closed` | Class name applied to the root element when the popover is closed. |

