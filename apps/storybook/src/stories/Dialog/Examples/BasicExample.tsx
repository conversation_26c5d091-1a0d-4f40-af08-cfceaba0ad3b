import { useState } from 'react';
import { But<PERSON> } from '@hxnova/react-components/Button';
import { Dialog } from '@hxnova/react-components/Dialog';

export default function BasicExample() {
  const [open, setOpen] = useState(false);
  const [type, setType] = useState<'default' | 'alert'>('default');
  const handleClose = () => {
    setOpen(false);
  };
  return (
    <>
      <div sx={{ display: 'flex', gap: 8 }}>
        <Button
          variant="outlined"
          onClick={() => {
            setOpen(true);
            setType('default');
          }}
        >
          Open default dialog
        </Button>
        <Button
          variant="outlined"
          onClick={() => {
            setOpen(true);
            setType('alert');
          }}
        >
          Open alert dialog (error button)
        </Button>
      </div>
      <Dialog.Root open={open} onClose={handleClose}>
        <Dialog.Header
          supportingText={
            'A dialog is a type of modal window that appears in front of app content to provide critical information, or ask for a decision.'
          }
        >
          Basic dialog title
        </Dialog.Header>
        <Dialog.Actions>
          <Button variant="text" onClick={handleClose}>
            Button
          </Button>
          <Button variant="filled" color={type === 'alert' ? 'error' : 'primary'} onClick={handleClose}>
            Button
          </Button>
        </Dialog.Actions>
      </Dialog.Root>
    </>
  );
}
