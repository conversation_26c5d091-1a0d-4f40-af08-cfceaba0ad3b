import React, { useState } from 'react';
import { Button } from '@hxnova/react-components/Button';
import { Dialog, DialogRootProps } from '@hxnova/react-components/Dialog';
import { Radio } from '@hxnova/react-components/Radio';
import { RadioGroup } from '@hxnova/react-components/RadioGroup';
import { Switch } from '@hxnova/react-components/Switch';
import { Typography } from '@hxnova/react-components/Typography';
import { FormControl } from '@hxnova/react-components/FormControl';
import { FormLabel } from '@hxnova/react-components/FormLabel';

export default function MaxWidthExample() {
  const [open, setOpen] = useState(false);
  const [fullWidth, setFullWidth] = useState(false);
  const [maxWidth, setMaxWidth] = useState('xs');
  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Button
        variant="outlined"
        onClick={() => {
          setOpen(true);
        }}
      >
        Open max-width dialog
      </Button>

      <Dialog.Root
        open={open}
        onClose={handleClose}
        maxWidth={maxWidth === 'false' ? false : (maxWidth as DialogRootProps['maxWidth'])}
        fullWidth={fullWidth}
      >
        <Dialog.Header supportingText={'You can set the maxWidth and fullWidth to change dialog size'}>
          Open max-width dialog
        </Dialog.Header>
        <Dialog.Content
          topDivider
          bottomDivider
          sx={{
            gap: 16,
            color: 'var(--palette-onSurfaceVariant)',
          }}
        >
          <div sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%', gap: 40 }}>
            <FormControl>
              <FormLabel>Max Width</FormLabel>
              <RadioGroup
                value={maxWidth}
                onChange={(e) => {
                  setMaxWidth((e.target as HTMLInputElement).value);
                }}
              >
                <Radio label="false" value={'false'} />
                <Radio label="xs" value={'xs'} />
                <Radio label="sm" value={'sm'} />
                <Radio label="md" value={'md'} />
                <Radio label="lg" value={'lg'} />
                <Radio label="xl" value={'xl'} />
              </RadioGroup>
            </FormControl>
            <div sx={{ display: 'flex', alignItems: 'center' }}>
              <Switch checked={fullWidth} onChange={(e) => setFullWidth(e.target.checked)} />
              <Typography>Full width</Typography>
            </div>
          </div>
        </Dialog.Content>
        <Dialog.Actions>
          <Button variant="text" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="filled" onClick={handleClose}>
            Accept
          </Button>
        </Dialog.Actions>
      </Dialog.Root>
    </>
  );
}
