import { useState } from 'react';
import { Button } from '@hxnova/react-components/Button';
import { Dialog } from '@hxnova/react-components/Dialog';

export default function CustomActionsExample() {
  const [open, setOpen] = useState(false);
  const handleClose = () => {
    setOpen(false);
  };
  return (
    <>
      <Button
        variant="outlined"
        onClick={() => {
          setOpen(true);
        }}
      >
        Open a dialog With three actions
      </Button>

      <Dialog.Root open={open} onClose={handleClose}>
        <Dialog.Header
          supportingText={
            'A dialog is a type of modal window that appears in front of app content to provide critical information, or ask for a decision.'
          }
        >
          Basic dialog title
        </Dialog.Header>
        <Dialog.Actions sx={{ justifyContent: 'space-between' }}>
          <Button variant="text" onClick={handleClose}>
            Button
          </Button>
          <div sx={{ display: 'flex', gap: '8px' }}>
            <Button variant="text" onClick={handleClose}>
              Button
            </Button>
            <Button variant="filled" onClick={handleClose}>
              Button
            </Button>
          </div>
        </Dialog.Actions>
      </Dialog.Root>
    </>
  );
}
