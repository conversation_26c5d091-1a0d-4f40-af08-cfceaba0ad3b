import { Accordion } from '@hxnova/react-components/Accordion';

export default function DensityExample() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <Accordion.Group density="compact">
        <Accordion.Item>
          <Accordion.Summary>Compact density</Accordion.Summary>
          <Accordion.Details>This accordion uses compact density with tighter spacing.</Accordion.Details>
        </Accordion.Item>
      </Accordion.Group>

      <Accordion.Group density="standard">
        <Accordion.Item>
          <Accordion.Summary>Standard density</Accordion.Summary>
          <Accordion.Details>This accordion uses the default density with standard spacing.</Accordion.Details>
        </Accordion.Item>
      </Accordion.Group>

      <Accordion.Group density="comfortable">
        <Accordion.Item>
          <Accordion.Summary>Comfortable density</Accordion.Summary>
          <Accordion.Details>This accordion uses comfortable density with more relaxed spacing.</Accordion.Details>
        </Accordion.Item>
      </Accordion.Group>
    </div>
  );
}
