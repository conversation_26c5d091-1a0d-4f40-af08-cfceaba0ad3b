# API Documentation

- [Accordion.Details](#accordiondetails)
- [Accordion.Group](#accordiongroup)
- [Accordion.Item](#accordionitem)
- [Accordion.Summary](#accordionsummary)

# Accordion.Details

API reference docs for the React Accordion.Details component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Accordion.Details` component, you can choose to import it directly or through the main entry point.

```jsx
import { Accordion } from '@hxnova/react-components/Accordion';
// or
import { Accordion } from '@hxnova/react-components';

// usage
<Accordion.Details>{children}</Accordion.Details>
```

## Props

The properties available for the `Accordion.Details` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, AccordionDetailsOwnerState> ⏐ undefined; content?: SlotProps<"div", object, AccordionDetailsOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `AccordionDetailsSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Accordion.Details` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAccordionDetails-root | `'div'` | The component that renders the root. |
| content | .NovaAccordionDetails-content | `'div'` | The component that renders the content. |

## CSS classes

CSS classes for different states and variations of the `Accordion.Details` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Class name applied when the accordion is disabled. |
| .Nova-expanded | `expanded` | Class name applied to the root element when expanded. |

<br><br>

# Accordion.Group

API reference docs for the React Accordion.Group component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Accordion.Group` component, you can choose to import it directly or through the main entry point.

```jsx
import { Accordion } from '@hxnova/react-components/Accordion';
// or
import { Accordion } from '@hxnova/react-components';

// usage
<Accordion.Group>{children}</Accordion.Group>
```

## Props

The properties available for the `Accordion.Group` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **density** | ``"standard" ⏐ "compact" ⏐ "comfortable"`` | `'standard'` | The size of the component. |
| **disableDivider** | ``false ⏐ true`` | `false` | If `true`, the divider between accordions will be hidden. |
| **slotProps** | ``{ root?: SlotProps<"div", object, AccordionGroupOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `AccordionGroupSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **transition** | ``string ⏐ { initial: string; expanded: string; }`` | `'0.2s ease'` | The CSS transition for the Accordion details. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Accordion.Group` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAccordionGroup-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `Accordion.Group` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaAccordionGroup-densityStandard | `densityStandard` | Class name applied to the root element if `density="standard"`. |
| .NovaAccordionGroup-densityCompact | `densityCompact` | Class name applied to the root element if `density="compact"`. |
| .NovaAccordionGroup-densityComfortable | `densityComfortable` | Class name applied to the root element if `density="comfortable"`. |

<br><br>

# Accordion.Item

API reference docs for the React Accordion.Item component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Accordion.Item` component, you can choose to import it directly or through the main entry point.

```jsx
import { Accordion } from '@hxnova/react-components/Accordion';
// or
import { Accordion } from '@hxnova/react-components';

// usage
<Accordion.Item>{children}</Accordion.Item>
```

## Props

The properties available for the `Accordion.Item` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **accordionId** | `string` | - | The id to be used in the AccordionDetails which is controlled by the AccordionSummary.<br>If not provided, the id is autogenerated. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **defaultExpanded** | ``false ⏐ true`` | `false` | If `true`, expands the accordion by default. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **expanded** | ``false ⏐ true`` | - | If `true`, expands the accordion, otherwise collapse it.<br>Setting this prop enables control over the accordion. |
| **onChange** | ``(event: SyntheticEvent<Element, Event>, expanded: boolean) => void`` | - | Callback fired when the expand/collapse state is changed.<br>@param event The event source of the callback. **Warning**: This is a generic event not a change event.<br>@param expanded The `expanded` state of the accordion. |
| **slotProps** | ``{ root?: SlotProps<"div", object, AccordionItemOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `AccordionItemSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Accordion.Item` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAccordionItem-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `Accordion.Item` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-expanded | `expanded` | Class name applied to the root element if `expanded` is true. |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled` is true. |

<br><br>

# Accordion.Summary

API reference docs for the React Accordion.Summary component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Accordion.Summary` component, you can choose to import it directly or through the main entry point.

```jsx
import { Accordion } from '@hxnova/react-components/Accordion';
// or
import { Accordion } from '@hxnova/react-components';

// usage
<Accordion.Summary>{children}</Accordion.Summary>
```

## Props

The properties available for the `Accordion.Summary` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **indicator** | `ReactNode` | `<KeyboardArrowDown />` | The indicator element to display. |
| **slotProps** | ``{ root?: SlotProps<"div", object, AccordionSummaryOwnerState> ⏐ undefined; button?: SlotProps<"button", object, AccordionSummaryOwnerState> ⏐ undefined; indicator?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `AccordionSummarySlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Accordion.Summary` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAccordionSummary-root | `'div'` | The component that renders the root. |
| button | .NovaAccordionSummary-button | `'button'` | The component that renders the button. |
| indicator | .NovaAccordionSummary-indicator | `'span'` | The component that renders the indicator. |

## CSS classes

CSS classes for different states and variations of the `Accordion.Summary` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Class name applied when the accordion is disabled. |
| .Nova-expanded | `expanded` | Class name applied when the accordion is expanded. |

