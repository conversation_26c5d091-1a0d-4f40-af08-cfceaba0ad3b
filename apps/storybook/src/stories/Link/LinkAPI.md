# API Documentation

- [Link](#link)

# Link

API reference docs for the React Link component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Link` component, you can choose to import it directly or through the main entry point.

```jsx
import { Link } from '@hxnova/react-components/Link';
// or
import { Link } from '@hxnova/react-components';
```

## Props

The properties available for the `Link` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **endDecorator** | `ReactNode` | - | Element placed after the children. |
| **slotProps** | ``{ root?: SlotProps<"a", object, LinkOwnerState> ⏐ undefined; startDecorator?: SlotProps<"span", object, LinkOwnerState> ⏐ undefined; endDecorator?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `LinkSlots` | `{}` | The components used for each slot inside. |
| **startDecorator** | `ReactNode` | - | Element placed before the children. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **underline** | ``"none" ⏐ "hover" ⏐ "always"`` | `'always'` | Controls when the link should have an underline. |
| **variant** | ``"displayLarge" ⏐ "displayMedium" ⏐ "displaySmall" ⏐ "headlineLarge" ⏐ "headlineMedium" ⏐ "headlineSmall" ⏐ "titleLarge" ⏐ "titleMedium" ⏐ "titleSmall" ⏐ "bodyLarge" ⏐ "bodyMedium" ⏐ "bodySmall" ⏐ "labelLarge" ⏐ "labelMedium" ⏐ "labelSmall" ⏐ "inherit"`` | `'labelMedium'` | Applies the theme typography styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Link` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaLink-root | `'a'` | The component that renders the root. |
| startDecorator | .NovaLink-startDecorator | `'span'` | The component that renders the start decorator. |
| endDecorator | .NovaLink-endDecorator | `'span'` | The component that renders the end decorator. |

## CSS classes

CSS classes for different states and variations of the `Link` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaLink-underlineNone | `underlineNone` | Class name applied to the root element if `underline="none"`. |
| .NovaLink-underlineHover | `underlineHover` | Class name applied to the root element if `underline="hover"`. |
| .NovaLink-underlineAlways | `underlineAlways` | Class name applied to the root element if `underline="always"`. |
| .NovaLink-button | `button` | Class name applied to the root element if `component="button"`. |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled={true}`. |
| .Nova-focusVisible | `focusVisible` | Class name applied to the root element if the link is keyboard focused. |

