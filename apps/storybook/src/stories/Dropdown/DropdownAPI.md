# API Documentation

- [Dropdown](#dropdown)
- [Option](#option)

# Dropdown

API reference docs for the React Dropdown component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Dropdown` component, you can choose to import it directly or through the main entry point.

```jsx
import { Dropdown } from '@hxnova/react-components/Dropdown';
// or
import { Dropdown } from '@hxnova/react-components';
```

## Props

The properties available for the `Dropdown` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``Ref<{ focusVisible(): void; }>`` | - | A ref for imperative actions. It currently only supports `focusVisible()` action. |
| **autoFocus** | ``false ⏐ true`` | `false` | If `true`, the dropdown is focused during the first mount |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **defaultListboxOpen** | ``false ⏐ true`` | `false` | If `true`, the dropdown will be initially open. |
| **defaultValue** | ``SelectValue<Value, Multiple>`` | - | The default selected value. Use when the component is not controlled. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the dropdown is disabled. |
| **endDecorator** | `ReactNode` | - | Trailing adornment for the dropdown select. |
| **error** | ``false ⏐ true`` | `false` | If `true`, the dropdown is under error state. |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the dropdown will take up the full width of its container. |
| **getSerializedValue** | ``(option: SelectValue<SelectOption<Value>, Multiple>) => string ⏐ number ⏐ readonly string[] ⏐ undefined`` | - | A function to convert the currently selected value to a string.<br>Used to set a value of a hidden input associated with the select,<br>so that the selected value can be posted with a form. |
| **indicator** | ``ReactNode ⏐ ((open: boolean) => ReactNode)`` | - | The end indicator for the dropdown select. |
| **listboxId** | `string` | - | `id` attribute of the listbox element.<br>Also used to derive the `id` attributes of options. |
| **listboxOpen** | ``false ⏐ true`` | `undefined` | Controls the open state of the select's listbox. |
| **multiple** | ``false ⏐ true`` | `false` | If `true`, `value` must be an array and the menu will support multiple selections. |
| **name** | `string` | - | Name of the dropdown. |
| **onChange** | ``(event: MouseEvent<Element, MouseEvent> ⏐ KeyboardEvent<Element> ⏐ FocusEvent<Element, Element> ⏐ null, value: SelectValue<...>) => void`` | - | Callback fired when an option is selected. |
| **onClose** | ``() => void`` | - | Triggered when focus leaves the menu and the menu should close. |
| **onListboxOpenChange** | ``(isOpen: boolean) => void`` | - | Callback fired when the component requests to be opened.<br>Use in controlled mode (see listboxOpen). |
| **placeholder** | `ReactNode` | - | Text to show when there is no selected value. |
| **readOnly** | ``false ⏐ true`` | `false` | If `true`, the dropdown is under readOnly state. |
| **ref** | ``((((instance: HTMLDivElement ⏐ null) => void) ⏐ RefObject<HTMLDivElement>) & (string ⏐ ((instance: HTMLDivElement ⏐ null) => void) ⏐ RefObject<...>)) ⏐ null`` | - | Allows getting a ref to the component instance.<br>Once the component unmounts, React will set `ref.current` to `null`<br>(or call the ref with `null` if you passed a callback ref).<br>@see {@link https://react.dev/learn/referencing-values-with-refs#refs-and-the-dom React Docs} |
| **renderValue** | ``((option: SelectValue<SelectOption<Value>, Multiple>) => ReactNode)`` | - | Function that renders the selected value. |
| **required** | ``false ⏐ true`` | `false` | If `true`, the select value cannot be empty when submitting form. |
| **showErrorIcon** | ``false ⏐ true`` | `true` | Determines whether to display an error state icon at the end of `Dropdown` component.<br>This option only takes effect if the `error` property is set to `true`. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the dropdown. |
| **slotProps** | ``{ root?: SlotProps<"div", object, DropdownOwnerState<any, Multiple>> ⏐ undefined; button?: SlotProps<"button", object, DropdownOwnerState<any, Multiple>> ⏐ undefined; ... 4 more ...; errorStateIcon?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `DropdownSlots` | `{}` | The components used for each slot inside. |
| **startDecorator** | `ReactNode` | - | Leading adornment for the dropdown select. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | ``SelectValue<Value, Multiple>`` | - | The selected value. Use when the component is controlled. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Dropdown` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDropdown-root | `'div'` | The component that renders the root. |
| button | .NovaDropdown-button | `'button'` | The component that renders the button. |
| startDecorator | .NovaDropdown-startDecorator | `'span'` | The component that renders the start decorator. |
| endDecorator | .NovaDropdown-endDecorator | `'span'` | The component that renders the end decorator. |
| indicator | .NovaDropdown-indicator | `'span'` | The component that renders the indicator. |
| listbox | .NovaDropdown-listbox | `'ul'` | The component that renders the listbox. |
| errorStateIcon | .NovaDropdown-errorStateIcon | `'span'` | The component that renders the error state. |

## CSS classes

CSS classes for different states and variations of the `Dropdown` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled={true}`. |
| .Nova-focusVisible | `focusVisible` | Class name applied to the root element if they are keyboard focused. |
| .Nova-expanded | `expanded` | Class name applied to the root element if the listbox is expanded. |
| .NovaDropdown-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaDropdown-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaDropdown-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |
| .NovaDropdown-multiple | `multiple` | Class name applied to the root element if `multiple={true}`. |
| .Nova-error | `error` | Class name applied to the root element if `error={true}`. |

<br><br>

# Option

API reference docs for the React Option component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Option` component, you can choose to import it directly or through the main entry point.

```jsx
import { Option } from '@hxnova/react-components/Option';
// or
import { Option } from '@hxnova/react-components';
```

## Props

The properties available for the `Option` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **value*** | `any` | - | The option value. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **label** | ``string ⏐ ReactElement<any, string ⏐ JSXElementConstructor<any>>`` | - | A text representation of the option's content.<br>Used for keyboard text navigation matching. |
| **slotProps** | ``{ root?: SlotProps<"li", object, OptionOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `OptionSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Option` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaOption-root | `'div'` |  |

## CSS classes

CSS classes for different states and variations of the `Option` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled={true}`. |
| .NovaOption-highlighted | `highlighted` | Class name applied to the root element if `highlighted={true}`. |
| .Nova-selected | `selected` | Class name applied to the root element if `selected={true}`. |

