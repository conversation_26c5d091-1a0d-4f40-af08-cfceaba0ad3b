import React from 'react';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';
import { FormControl } from '@hxnova/react-components/FormControl';
import { FormLabel } from '@hxnova/react-components/FormLabel';

export default function Example() {
  return (
    <div sx={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
      <FormControl size="small">
        <FormLabel>Small</FormLabel>
        <Dropdown placeholder="Select" sx={{ width: '200px' }} slotProps={{ listbox: { density: 'compact' } }}>
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Dropdown>
      </FormControl>
      <FormControl size="medium">
        <FormLabel>Medium</FormLabel>
        <Dropdown placeholder="Select" sx={{ width: '200px' }} slotProps={{ listbox: { density: 'standard' } }}>
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Dropdown>
      </FormControl>
      <FormControl size="large">
        <FormLabel>large</FormLabel>
        <Dropdown placeholder="Select" sx={{ width: '200px' }} slotProps={{ listbox: { density: 'comfortable' } }}>
          <Option value="option1">Option 1</Option>
          <Option value="option2">Option 2</Option>
          <Option value="option3">Option 3</Option>
        </Dropdown>
      </FormControl>
    </div>
  );
}
