import React, { useState } from 'react';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';
import Icon from '@hxnova/icons/Icon';
import { Checkbox } from '@hxnova/react-components/Checkbox';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Button } from '@hxnova/react-components/Button';
import { Divider } from '@hxnova/react-components/Divider';

const generateOptions = (count: number) => {
  const result = [];
  for (let i = 0; i < count; i++) {
    result.push({
      label: `Option ${i + 1}`,
      value: `option${i + 1}`,
    });
  }
  return result;
};
const allOptions = generateOptions(20);
export default function Example() {
  const [open, setOpen] = useState<boolean>(false);
  const [options, setOptions] = useState<string[]>([]);
  const handleOptionChange = (event: React.SyntheticEvent | null, values: string[]) => {
    setOptions(values);
  };
  const onApply = () => {
    setOpen(false);
  };
  const onClear = () => {
    setOptions([]);
    setOpen(false);
  };
  return (
    <Dropdown
      multiple
      value={options}
      onChange={handleOptionChange}
      placeholder="Select"
      sx={{ width: '400px' }}
      startDecorator={<Icon family="material" name="calendar_today" />}
      renderValue={(items) => (items.length > 1 ? `${items.length} options selected` : items[0]?.label)}
      listboxOpen={open}
      onListboxOpenChange={(isOpen) => {
        setOpen(isOpen);
      }}
      onClose={onApply}
    >
      <div sx={{ maxHeight: '300px', overflow: 'auto' }}>
        {allOptions.map((i) => (
          <Option key={i.value} value={i.value}>
            <ListItemDecorator>
              <Checkbox checked={options.includes(i.value)} />
            </ListItemDecorator>
            <ListItemContent primary={i.label} />
          </Option>
        ))}
      </div>
      <Divider />
      <div sx={{ padding: '8px 16px', display: 'flex', justifyContent: 'end', gap: '8px' }}>
        <Button variant="text" onClick={onClear}>
          Clear
        </Button>
        <Button onClick={onApply}>Apply</Button>
      </div>
    </Dropdown>
  );
}
