import React from 'react';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';

export default function Example() {
  return (
    <div sx={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
      <Dropdown placeholder="Select" sx={{ width: '200px' }}>
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>
      <Dropdown placeholder="Select" sx={{ width: '200px' }} defaultValue={'option2'}>
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>
    </div>
  );
}
