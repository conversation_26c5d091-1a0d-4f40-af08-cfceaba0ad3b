import React from 'react';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';

export default function Example() {
  return (
    <div sx={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
      <Dropdown placeholder="Disabled" sx={{ width: '200px' }} disabled>
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>
      <Dropdown placeholder="Error" sx={{ width: '200px' }} error>
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>
      <Dropdown placeholder="Readonly" sx={{ width: '200px' }} readOnly>
        <Option value="option1">Option 1</Option>
        <Option value="option2">Option 2</Option>
        <Option value="option3">Option 3</Option>
      </Dropdown>
    </div>
  );
}
