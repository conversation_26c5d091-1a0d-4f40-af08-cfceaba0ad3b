import React, { useState } from 'react';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';

export default function Example() {
  const [options, setOptions] = useState<string[]>(['option1', 'option2']);
  const handleOptionChange = (event: React.SyntheticEvent | null, values: string[]) => {
    setOptions(values);
  };
  return (
    <Dropdown multiple value={options} onChange={handleOptionChange} placeholder="Select" sx={{ width: '400px' }}>
      <Option value="option1">Option 1</Option>
      <Option value="option2">Option 2</Option>
      <Option value="option3">Option 3</Option>
      <Option value="option4">Option 4</Option>
      <Option value="option5">Option 5</Option>
    </Dropdown>
  );
}
