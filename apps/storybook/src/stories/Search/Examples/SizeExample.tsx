import { Search } from '@hxnova/react-components/Search';
import { IconButton } from '@hxnova/react-components/IconButton';
import Icon from '@hxnova/icons/Icon';
export default function SizeExample() {
  return (
    <div sx={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
      <Search
        size={'small'}
        placeholder="Hinted search text"
        startDecorator={<Icon family="material" name="search" size={24} />}
        endDecorator={
          <IconButton variant="neutral" size="small">
            <Icon family="material" name="mic_none" size={24} />
          </IconButton>
        }
      />
      <Search
        size={'medium'}
        placeholder="Hinted search text"
        startDecorator={<Icon family="material" name="search" size={24} />}
        endDecorator={
          <IconButton variant="neutral" size="medium">
            <Icon family="material" name="mic_none" size={24} />
          </IconButton>
        }
      />
      <Search
        size={'large'}
        placeholder="Hinted search text"
        startDecorator={<Icon family="material" name="search" size={24} />}
        endDecorator={
          <IconButton variant="neutral" size="large">
            <Icon family="material" name="mic_none" size={24} />
          </IconButton>
        }
      />
    </div>
  );
}
