import { Search } from '@hxnova/react-components/Search';
import { IconButton } from '@hxnova/react-components/IconButton';
import Icon from '@hxnova/icons/Icon';

export default function FullWidthExample() {
  return (
    <div sx={{ display: 'flex' }}>
      <Search
        fullWidth
        placeholder="Hinted search text"
        startDecorator={<Icon family="material" name="search" size={24} />}
        endDecorator={
          <IconButton variant="neutral">
            <Icon family="material" name="mic_none" size={24} />
          </IconButton>
        }
      />
    </div>
  );
}
