# API Documentation

- [Search](#search)

# Search

API reference docs for the React Search component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Search` component, you can choose to import it directly or through the main entry point.

```jsx
import { Search } from '@hxnova/react-components/Search';
// or
import { Search } from '@hxnova/react-components';
```

## Props

The properties available for the `Search` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **endDecorator** | `ReactNode` | - | Trailing adornment for this input. |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the input will take up the full width of its container. |
| **id** | `string` | - | The id of the `input` element. |
| **name** | `string` | - | Name attribute of the `input` element. |
| **onBlur** | ``FocusEventHandler<HTMLInputElement ⏐ HTMLTextAreaElement>`` | - | Callback fired when the `input` is blurred.<br>Notice that the first argument (event) might be undefined. |
| **onChange** | ``ChangeEventHandler<HTMLInputElement ⏐ HTMLTextAreaElement>`` | - | Callback fired when the value is changed.<br>@param event The event source of the callback.<br>You can pull out the new value by accessing `event.target.value` (string). |
| **onFocus** | ``FocusEventHandler<HTMLInputElement ⏐ HTMLTextAreaElement>`` | - |  |
| **onKeyDown** | ``KeyboardEventHandler<HTMLInputElement ⏐ HTMLTextAreaElement>`` | - |  |
| **onKeyUp** | ``KeyboardEventHandler<HTMLInputElement ⏐ HTMLTextAreaElement>`` | - |  |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | ``{ root?: FormControlProps ⏐ undefined; input?: Omit<InputProps, InputRootKeys> ⏐ undefined; htmlInput?: SlotProps<"input", object, InputOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `SearchSlots` | `{}` | The components used for each slot inside. |
| **startDecorator** | `ReactNode` | - | Leading adornment for this input. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Search` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaSearch-root | `'div'` | The component that renders the root. |
| input | .NovaSearch-input | `'div'` | The component that renders the input. |
| htmlInput | .NovaSearch-htmlInput | `'div'` | The component that renders the inner html input. |

