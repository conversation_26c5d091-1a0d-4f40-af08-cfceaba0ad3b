# API Documentation

- [Checkbox](#checkbox)

# Checkbox

API reference docs for the React Checkbox component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Checkbox` component, you can choose to import it directly or through the main entry point.

```jsx
import { Checkbox } from '@hxnova/react-components/Checkbox';
// or
import { Checkbox } from '@hxnova/react-components';
```

## Props

The properties available for the `Checkbox` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **checked** | ``false ⏐ true`` | - | If `true`, the component is checked. |
| **color** | ``"primary" ⏐ "error"`` | - | The color of the Checkbox |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **defaultChecked** | ``false ⏐ true`` | - | The default checked state. Use when the component is not controlled. |
| **disabled** | ``false ⏐ true`` | - | If `true`, the component is disabled. |
| **id** | `string` | - | The id of the `input` element. |
| **indeterminate** | ``false ⏐ true`` | `false` | If `true`, the component appears indeterminate.<br>This does not set the native input element to indeterminate due<br>to inconsistent behavior across browsers.<br>However, we set a `data-indeterminate` attribute on the `input`. |
| **label** | `ReactNode` | - | The label element next to the checkbox. |
| **name** | `string` | - | The `name` attribute of the input. |
| **onBlur** | ``FocusEventHandler<Element>`` | - |  |
| **onChange** | ``ChangeEventHandler<HTMLInputElement>`` | - | Callback fired when the state is changed.<br>@param event The event source of the callback.<br>You can pull out the new value by accessing `event.target.value` (string).<br>You can pull out the new checked state by accessing `event.target.checked` (boolean). |
| **onFocus** | ``FocusEventHandler<Element>`` | - |  |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **readOnly** | ``false ⏐ true`` | - | If `true`, the component is read only. |
| **required** | ``false ⏐ true`` | - | If `true`, the `input` element is required. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the Checkbox. |
| **slotProps** | ``{ root?: SlotProps<"span", CheckBoxRootSlotPropsOverrides, CheckboxOwnerState> ⏐ undefined; checkbox?: SlotProps<...> ⏐ undefined; action?: SlotProps<...> ⏐ undefined; input?: SlotProps<...> ⏐ undefined; label?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `CheckboxSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | ``string ⏐ number ⏐ readonly string[]`` | - | The value of the component. The DOM API casts this to a string.<br>The browser uses "on" as the default value. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Checkbox` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCheckbox-root | `'span'` | The component that renders the root. |
| checkbox | .NovaCheckbox-checkbox | `'span'` | The component that renders the checkbox. |
| action | .NovaCheckbox-action | `'span'` | The component that renders the action. |
| input | .NovaCheckbox-input | `'input'` | The component that renders the input. |
| label | .NovaCheckbox-label | `'label'` | The component that renders the label. |

## CSS classes

CSS classes for different states and variations of the `Checkbox` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-checked | `checked` | State class applied to the root element if `checked={true}`. |
| .Nova-disabled | `disabled` | State class applied to the root element if `disabled={true}`. |
| .NovaCheckbox-indeterminate | `indeterminate` | State class applied to the root element if `indeterminate={true}`. |
| .NovaCheckbox-colorPrimary | `colorPrimary` | State class applied to the root element if `color="primary"`. |
| .NovaCheckbox-colorError | `colorError` | State class applied to the root element if `color="secondary"`. |
| .NovaCheckbox-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaCheckbox-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |
| .NovaCheckbox-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .Nova-focusVisible | `focusVisible` | Class name applied to the root element if the switch has visible focus |

