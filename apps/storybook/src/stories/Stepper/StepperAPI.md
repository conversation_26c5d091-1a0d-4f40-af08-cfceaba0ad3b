# API Documentation

- [Step](#step)
- [StepButton](#stepbutton)
- [Stepper](#stepper)

# Step

API reference docs for the React Step component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Step` component, you can choose to import it directly or through the main entry point.

```jsx
import { Step } from '@hxnova/react-components/Step';
// or
import { Step } from '@hxnova/react-components';
```

## Props

The properties available for the `Step` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **active** | ``false ⏐ true`` | `false` | If `true`, the active className is appended.<br>You can customize the active state from the Stepper's `sx` prop. |
| **children** | `ReactNode` | - | Used to render icon or text elements inside the Step if `src` is not set.<br>This can be an element, or just a string. |
| **completed** | ``false ⏐ true`` | `false` | If `true`, the completed className is appended.<br>You can customize the active state from the Stepper's `sx` prop. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the active className is appended.<br>You can customize the active state from the Stepper's `sx` prop. |
| **icon** | `ReactNode` | - | Icon to display in the step indicator.<br>Can be a number, string, or React element. |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'horizontal'` | The component orientation. |
| **slotProps** | ``{ root?: SlotProps<"li", object, StepOwnerState> ⏐ undefined; indicator?: SlotProps<"div", object, StepOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `StepSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Step` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaStep-root | `'li'` | The component that renders the root. |
| indicator | .NovaStep-indicator | `'div'` | The component that renders the indicator. |

## CSS classes

CSS classes for different states and variations of the `Step` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaStep-horizontal | `horizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaStep-vertical | `vertical` | Class name applied to the root element if `orientation="vertical"`. |
| .Nova-active | `active` | Class name applied to the root element if `active` is true. |
| .Nova-completed | `completed` | Class name applied to the root element if `completed` is true. |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled` is true. |

<br><br>

# StepButton

API reference docs for the React StepButton component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `StepButton` component, you can choose to import it directly or through the main entry point.

```jsx
import { StepButton } from '@hxnova/react-components/StepButton';
// or
import { StepButton } from '@hxnova/react-components';
```

## Props

The properties available for the `StepButton` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **slotProps** | ``{ root?: SlotProps<"button", object, StepButtonOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `StepButtonSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `StepButton` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaStepButton-root | `'button'` | The component that renders the root. |

<br><br>

# Stepper

API reference docs for the React Stepper component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Stepper` component, you can choose to import it directly or through the main entry point.

```jsx
import { Stepper } from '@hxnova/react-components/Stepper';
// or
import { Stepper } from '@hxnova/react-components';
```

## Props

The properties available for the `Stepper` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'horizontal'` | The component orientation. |
| **slotProps** | ``{ root?: SlotProps<"ol", object, StepperOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `StepperSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Stepper` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaStepper-root | `'ol'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `Stepper` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaStepper-horizontal | `horizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaStepper-vertical | `vertical` | Class name applied to the root element if `orientation="vertical"`. |

