import { Stepper } from '@hxnova/react-components/Stepper';
import { Step } from '@hxnova/react-components/Step';
import Icon from '@hxnova/icons/Icon';

export default function CustomIconsExample() {
  // Common props for the Icon component
  const iconProps = {
    family: 'material' as const,
    size: 24,
  };

  return (
    <Stepper
      sx={{
        width: '100%',
        '--nova-stepIndicator-size': '3rem',
      }}
    >
      <Step completed icon={<Icon {...iconProps} name="shopping_cart" />} />
      <Step completed icon={<Icon {...iconProps} name="contacts" />} />
      <Step completed icon={<Icon {...iconProps} name="local_shipping" />} />
      <Step active icon={<Icon {...iconProps} name="credit_card" />} />
      <Step icon={<Icon {...iconProps} name="check_circle" />} />
    </Stepper>
  );
}
