import * as React from 'react';
import { Stepper } from '@hxnova/react-components/Stepper';
import { Step } from '@hxnova/react-components/Step';
import { Typography } from '@hxnova/react-components/Typography';
import { Button } from '@hxnova/react-components/Button';
import Icon from '@hxnova/icons/Icon';

const steps = ['Select campaign settings', 'Create an ad group', 'Create an ad'];

export default function VerticalExample() {
  const [activeStep, setActiveStep] = React.useState(0);

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleFinish = () => {
    setActiveStep(steps.length);
  };

  const isFinished = activeStep === steps.length;

  return (
    <Stepper orientation="vertical">
      {steps.map((label, index) => (
        <Step
          key={label + index}
          active={!isFinished && index === activeStep}
          completed={isFinished || index < activeStep}
          icon={isFinished || index < activeStep ? <Icon family="material" name="check" size={20} /> : index + 1}
        >
          <Typography variant="bodyMedium">{label}</Typography>
          {!isFinished && activeStep === index && (
            <div>
              <Typography variant="bodyMedium" color="textSecondary" sx={{ maxWidth: '350px' }}>
                Step {index + 1} content goes here. This is the detailed information for this step.
              </Typography>
              <div sx={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
                {index === steps.length - 1 ? (
                  <Button size="small" variant="filled" onClick={handleFinish}>
                    Finish
                  </Button>
                ) : (
                  <Button size="small" variant="filled" onClick={handleNext}>
                    Continue
                  </Button>
                )}
                <Button size="small" variant="text" onClick={handleBack} disabled={index === 0}>
                  Back
                </Button>
              </div>
            </div>
          )}
        </Step>
      ))}
    </Stepper>
  );
}
