import { Tag } from '@hxnova/react-components/Tag';

export default function ClickableExample() {
  const handleClick = () => {
    console.log('Tag clicked!');
  };

  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <Tag label="Clickable Tag" onClick={handleClick} />
      <Tag label="Disabled Clickable Tag" onClick={handleClick} disabled />
    </div>
  );
}
