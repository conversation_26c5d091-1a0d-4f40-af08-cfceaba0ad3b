import { Tag } from '@hxnova/react-components/Tag';

export default function IntensityExample() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <Tag label="Bold" variant="neutral" intensity="bold" />
      <Tag label="Subtle" variant="neutral" intensity="subtle" />
      <Tag label="Bold" variant="error" intensity="bold" />
      <Tag label="Subtle" variant="error" intensity="subtle" />
    </div>
  );
}
