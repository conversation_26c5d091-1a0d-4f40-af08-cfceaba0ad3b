# API Documentation

- [Tag](#tag)

# Tag

API reference docs for the React Tag component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Tag` component, you can choose to import it directly or through the main entry point.

```jsx
import { Tag } from '@hxnova/react-components/Tag';
// or
import { Tag } from '@hxnova/react-components';
```

## Props

The properties available for the `Tag` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **label*** | `string` | - | The text label to show in the chip |
| **disabled** | ``false ⏐ true`` | `false` | Whether or not the chip is disabled |
| **endIcon** | ``ReactElement<any, string ⏐ JSXElementConstructor<any>>`` | - | Optional icon to show after the label |
| **intensity** | ``"bold" ⏐ "subtle"`` | `'bold'` | The intensity of the tag |
| **selected** | ``false ⏐ true`` | - | Whether or not the chip is selected/active |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the chip |
| **startIcon** | ``ReactElement<any, string ⏐ JSXElementConstructor<any>>`` | - | Optional icon to show before the label |
| **variant** | ``"neutral" ⏐ "error" ⏐ "warning" ⏐ "info" ⏐ "success"`` | `'neutral'` | The variant of the tag |

## CSS classes

CSS classes for different states and variations of the `Tag` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaTag-root | `root` | Class name applied to the root element. |
| .NovaTag-neutral | `neutral` | Class name applied to the root element if variant="neutral". |
| .Nova-error | `error` | Class name applied to the root element if variant="error". |
| .NovaTag-warning | `warning` | Class name applied to the root element if variant="warning". |
| .NovaTag-info | `info` | Class name applied to the root element if variant="info". |
| .NovaTag-success | `success` | Class name applied to the root element if variant="success". |
| .NovaTag-bold | `bold` | Class name applied to the root element if style="bold". |
| .NovaTag-subtle | `subtle` | Class name applied to the root element if style="subtle". |

