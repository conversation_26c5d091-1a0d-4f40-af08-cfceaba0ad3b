# API Documentation

- [FormControl](#formcontrol)
- [FormHelperText](#formhelpertext)
- [FormLabel](#formlabel)
- [Input](#input)
- [TextField](#textfield)
- [Textarea](#textarea)

# FormControl

API reference docs for the React FormControl component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `FormControl` component, you can choose to import it directly or through the main entry point.

```jsx
import { FormControl } from '@hxnova/react-components/FormControl';
// or
import { FormControl } from '@hxnova/react-components';
```

## Props

The properties available for the `FormControl` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **defaultValue** | `unknown` | - |  |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the children are in disabled state. |
| **error** | ``false ⏐ true`` | `false` | If `true`, the children will indicate an error. |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the component will take up the full width of its container. |
| **onChange** | ``ChangeEventHandler<NativeFormControlElement>`` | - | Callback fired when the form element's value is modified. |
| **required** | ``false ⏐ true`` | `false` | If `true`, the user must specify a value for the input before the owning form can be submitted.<br>If `true`, the asterisk appears on the FormLabel. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, FormControlOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `FormControlSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | `unknown` | - | The value of the form element. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `FormControl` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaFormControl-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `FormControl` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-error | `error` | State class applied to the root element if `error={true}`. |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled={true}`. |
| .NovaFormControl-fullWidth | `fullWidth` | Class name applied to the root element if `fullWidth={true}`. |
| .NovaFormControl-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaFormControl-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaFormControl-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |

<br><br>

# FormHelperText

API reference docs for the React FormHelperText component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `FormHelperText` component, you can choose to import it directly or through the main entry point.

```jsx
import { FormHelperText } from '@hxnova/react-components/FormHelperText';
// or
import { FormHelperText } from '@hxnova/react-components';
```

## Props

The properties available for the `FormHelperText` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | - | If `true`, the helper text should be displayed in a disabled state. |
| **error** | ``false ⏐ true`` | - | If `true`, the helper text is displayed in an error state. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | ``{ root?: SlotProps<"p", object, FormHelperTextOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `FormHelperTextSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `FormHelperText` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaFormHelperText-root | `'p'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `FormHelperText` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled={true}`. |
| .Nova-error | `error` | State class applied to the root element if `error={true}`. |
| .NovaFormHelperText-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaFormHelperText-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaFormHelperText-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |

<br><br>

# FormLabel

API reference docs for the React FormLabel component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `FormLabel` component, you can choose to import it directly or through the main entry point.

```jsx
import { FormLabel } from '@hxnova/react-components/FormLabel';
// or
import { FormLabel } from '@hxnova/react-components';
```

## Props

The properties available for the `FormLabel` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | - | If `true`, the label should be displayed in a disabled state. |
| **error** | ``false ⏐ true`` | - | If `true`, the label is displayed in an error state. |
| **required** | ``false ⏐ true`` | - | The asterisk is added if required=`true` |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | ``{ root?: SlotProps<"label", object, FormLabelOwnerState> ⏐ undefined; asterisk?: SlotProps<"span", object, FormLabelOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `FormLabelSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `FormLabel` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaFormLabel-root | `'label'` | The component that renders the root. |
| asterisk | .NovaFormLabel-asterisk | `'span'` | The component that renders the asterisk. |

## CSS classes

CSS classes for different states and variations of the `FormLabel` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled={true}`. |
| .Nova-error | `error` | State class applied to the root element if `error={true}`. |
| .NovaFormLabel-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaFormLabel-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaFormLabel-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |

<br><br>

# Input

API reference docs for the React Input component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Input` component, you can choose to import it directly or through the main entry point.

```jsx
import { Input } from '@hxnova/react-components/Input';
// or
import { Input } from '@hxnova/react-components';
```

## Props

The properties available for the `Input` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **endDecorator** | `ReactNode` | - | Trailing adornment for this input. |
| **error** | ``false ⏐ true`` | `false` | If `true`, the `input` will indicate an error.<br>The prop defaults to the value (`false`) inherited from the parent FormControl component. |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the input will take up the full width of its container. |
| **showErrorIcon** | ``false ⏐ true`` | `true` | Determines whether to display an error state icon at the end of `input` component.<br>This option only takes effect if the `error` property is set to `true`. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, InputOwnerState> ⏐ undefined; wrapper?: SlotProps<"div", object, InputOwnerState> ⏐ undefined; input?: SlotProps<...> ⏐ undefined; startDecorator?: SlotProps<...> ⏐ undefined; endDecorator?: SlotProps<...> ⏐ undefined; errorStateIcon?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `InputSlots` | `{}` | The components used for each slot inside. |
| **startDecorator** | `ReactNode` | - | Leading adornment for this input. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Input` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaInput-root | `'div'` | The component that renders the root. |
| wrapper | .NovaInput-wrapper | `'div'` | The component that renders the wrapper. |
| input | .NovaInput-input | `'input'` | The component that renders the input. |
| startDecorator | .NovaInput-startDecorator | `'span'` | The component that renders the start decorator. |
| endDecorator | .NovaInput-endDecorator | `'span'` | The component that renders the end decorator. |
| errorStateIcon | .NovaInput-errorStateIcon | `'span'` | The component that renders the end decorator. |

## CSS classes

CSS classes for different states and variations of the `Input` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-focused | `focused` | Class name applied to the root element if the component is focused. |
| .NovaInput-filled | `filled` | Class name applied to the root element if the input has value |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled={true}`. |
| .Nova-error | `error` | State class applied to the root element if `error={true}`. |
| .NovaInput-fullWidth | `fullWidth` | Class name applied to the root element if `fullWidth={true}`. |
| .NovaInput-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaInput-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaInput-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |

<br><br>

# TextField

API reference docs for the React TextField component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `TextField` component, you can choose to import it directly or through the main entry point.

```jsx
import { TextField } from '@hxnova/react-components/TextField';
// or
import { TextField } from '@hxnova/react-components';
```

## Props

The properties available for the `TextField` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **endDecorator** | `ReactNode` | - | Trailing adornment for this input.<br>Trailing adornment for this textarea. |
| **error** | ``false ⏐ true`` | `false false` | If `true`, the `input` will indicate an error.<br>The prop defaults to the value (`false`) inherited from the parent FormControl component.<br>If `true`, the `textarea` will indicate an error.<br>The prop defaults to the value (`false`) inherited from the parent FormControl component. |
| **fullWidth** | ``false ⏐ true`` | `false false` | If `true`, the input will take up the full width of its container.<br>If `true`, the textarea will take up the full width of its container. |
| **helperText** | `ReactNode` | - | The helper text content. |
| **id** | `string` | - | The id of the `input` element.<br>Use this prop to make `label` and `helperText` accessible for screen readers. |
| **label** | `ReactNode` | - | The label content. |
| **maxRows** | ``string ⏐ number`` | - | Maximum number of rows to display. |
| **minRows** | ``string ⏐ number`` | `1` | Minimum number of rows to display. |
| **multiline** | ``false ⏐ true`` | `false` | If `true`, a `textarea` element is rendered instead of an `input`. |
| **name** | `string` | - | Name attribute of the `input` element. |
| **onBlur** | ``FocusEventHandler<HTMLInputElement ⏐ HTMLTextAreaElement>`` | - | Callback fired when the `input` is blurred.<br>Notice that the first argument (event) might be undefined. |
| **onChange** | ``ChangeEventHandler<HTMLInputElement ⏐ HTMLTextAreaElement>`` | - | Callback fired when the value is changed.<br>@param event The event source of the callback.<br>You can pull out the new value by accessing `event.target.value` (string). |
| **onFocus** | ``FocusEventHandler<HTMLInputElement ⏐ HTMLTextAreaElement>`` | - |  |
| **onKeyDown** | ``KeyboardEventHandler<HTMLInputElement ⏐ HTMLTextAreaElement>`` | - |  |
| **onKeyUp** | ``KeyboardEventHandler<HTMLInputElement ⏐ HTMLTextAreaElement>`` | - |  |
| **showErrorIcon** | ``false ⏐ true`` | `true` | Determines whether to display an error state icon at the end of `input` component.<br>This option only takes effect if the `error` property is set to `true`. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium' 'medium'` | The size of the component. |
| **slotProps** | ``{ root?: FormControlProps ⏐ undefined; label?: FormLabelProps ⏐ undefined; input?: (SlotCommonProps & Pick<InputHTMLAttributes<HTMLInputElement>, "autoComplete" ⏐ ... 16 more ... ⏐ "maxLength"> & ... 6 more ... & Omit<...>) ⏐ undefined; htmlInput?: SlotProps<...> ⏐ undefined; helperText?: FormHelperTextProps ⏐ undef...`` | `{}` | The props used for each slot inside. |
| **slots** | `TextFieldSlots` | `{}` | The components used for each slot inside. |
| **startDecorator** | `ReactNode` | - | Leading adornment for this input.<br>Leading adornment for this textarea. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `TextField` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaTextField-root | `'div'` | The component that renders the root. |
| label | .NovaTextField-label | `'div'` | The component that renders the label. |
| input | .NovaTextField-input | `'div'` | The component that renders the input. |
| htmlInput | .NovaTextField-htmlInput | `'div'` | The component that renders the inner html input. |
| helperText | .NovaTextField-helperText | `'div'` | The component that renders the helper text. |
| counterText | .NovaTextField-counterText | `'span'` | The component that renders the counter text. |

## CSS classes

CSS classes for different states and variations of the `TextField` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaTextField-supportTextRoot | `supportTextRoot` | Class name applied to the supportTextRoot element. |

<br><br>

# Textarea

API reference docs for the React Textarea component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Textarea` component, you can choose to import it directly or through the main entry point.

```jsx
import { Textarea } from '@hxnova/react-components/Textarea';
// or
import { Textarea } from '@hxnova/react-components';
```

## Props

The properties available for the `Textarea` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **endDecorator** | `ReactNode` | - | Trailing adornment for this textarea. |
| **error** | ``false ⏐ true`` | `false` | If `true`, the `textarea` will indicate an error.<br>The prop defaults to the value (`false`) inherited from the parent FormControl component. |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the textarea will take up the full width of its container. |
| **maxRows** | ``string ⏐ number`` | - | Maximum number of rows to display. |
| **minRows** | ``string ⏐ number`` | `1` | Minimum number of rows to display. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, TextareaOwnerState> ⏐ undefined; textarea?: SlotProps<"textarea", object, TextareaOwnerState> ⏐ undefined; startDecorator?: SlotProps<...> ⏐ undefined; endDecorator?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `TextareaSlots` | `{}` | The components used for each slot inside. |
| **startDecorator** | `ReactNode` | - | Leading adornment for this textarea. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Textarea` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaTextarea-root | `'div'` | The component that renders the root. |
| textarea | .NovaTextarea-textarea | `'textarea'` | The component that renders the textarea. |
| startDecorator | .NovaTextarea-startDecorator | `'div'` | The component that renders the start decorator. |
| endDecorator | .NovaTextarea-endDecorator | `'div'` | The component that renders the end decorator. |

## CSS classes

CSS classes for different states and variations of the `Textarea` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-focused | `focused` | Class name applied to the root element if the component is focused. |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled={true}`. |
| .Nova-error | `error` | State class applied to the root element if `error={true}`. |
| .NovaTextarea-fullWidth | `fullWidth` | Class name applied to the root element if `fullWidth={true}`. |
| .NovaTextarea-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaTextarea-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaTextarea-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |

