import { TextField } from '@hxnova/react-components/TextField';

export default function FormAttrExample() {
  return (
    <div sx={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
      <TextField placeholder="Name" label="Name" defaultValue="Hello World" />
      <TextField placeholder="Required" label="Required" defaultValue="Hello World" required />
      <TextField placeholder="Disabled" label="Disabled" defaultValue="Hello World" disabled />
      <TextField placeholder="ReadOnly" label="ReadOnly" defaultValue="Hello World" readOnly />
    </div>
  );
}
