import { TextField } from '@hxnova/react-components/TextField';
import { Typography } from '@hxnova/react-components/Typography';
import Icon from '@hxnova/icons/Icon';
import { IconButton } from '@hxnova/react-components/IconButton';

export default function DecoratorsExample() {
  return (
    <TextField
      placeholder="Name"
      label="Label"
      helperText="Supported text"
      startDecorator={<Icon family="material" name="search" />}
      endDecorator={
        <div sx={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <Typography>Kg</Typography>
          <IconButton variant="standard" color="inherit">
            <Icon family="material" name="close" />
          </IconButton>
        </div>
      }
      maxLength={20}
    />
  );
}
