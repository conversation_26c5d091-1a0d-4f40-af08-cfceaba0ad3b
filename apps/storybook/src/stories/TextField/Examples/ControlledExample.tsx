import React from 'react';
import { TextField } from '@hxnova/react-components/TextField';

export default function ControlledExample() {
  const [value, setValue] = React.useState('Hello');

  return (
    <div sx={{ display: 'flex', gap: 8 }}>
      <TextField
        id="controlled"
        label="Controlled"
        value={value}
        onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
          setValue(event.target.value);
        }}
      />
      <TextField id="uncontrolled" label="Uncontrolled" defaultValue="Hello" />
    </div>
  );
}
