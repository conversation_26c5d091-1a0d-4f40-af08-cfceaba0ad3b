import { Skeleton } from '@hxnova/react-components/Skeleton';

export default function Demo() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: 8, width: 210 }}>
      {/* For variant="text", adjust the height via line-height */}
      <Skeleton variant="text" sx={{ lineHeight: '2rem' }} />

      {/* For other variants, adjust the size with `width` and `height` */}
      <Skeleton variant="circular" width={40} height={40} />
      <Skeleton variant="rectangular" height={60} />
      <Skeleton variant="rounded" height={60} />
    </div>
  );
}
