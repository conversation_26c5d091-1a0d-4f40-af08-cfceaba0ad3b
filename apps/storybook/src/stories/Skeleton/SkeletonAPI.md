# API Documentation

- [Skeleton](#skeleton)

# Skeleton

API reference docs for the React Skeleton component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Skeleton` component, you can choose to import it directly or through the main entry point.

```jsx
import { Skeleton } from '@hxnova/react-components/Skeleton';
// or
import { Skeleton } from '@hxnova/react-components';
```

## Props

The properties available for the `Skeleton` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **animation** | ``false ⏐ "pulse" ⏐ "wave"`` | `'pulse'` | The animation.<br>If `false` the animation effect is disabled. |
| **children** | `ReactNode` | - | Optional children to infer width and height from. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **height** | ``string ⏐ number`` | - | Height of the skeleton.<br>Useful when you don't want to adapt the skeleton to a text element but for instance a card. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **variant** | ``"text" ⏐ "rectangular" ⏐ "rounded" ⏐ "circular"`` | `'text'` | The type of content that will be rendered. |
| **width** | ``string ⏐ number`` | - | Width of the skeleton.<br>Useful when the skeleton is inside an inline element with no width of its own. |

## CSS classes

CSS classes for different states and variations of the `Skeleton` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaSkeleton-root | `root` | Class name applied to the root element. |
| .NovaSkeleton-text | `text` | Class name applied to the root element if `variant="text"`. |
| .NovaSkeleton-rectangular | `rectangular` | Class name applied to the root element if `variant="rectangular"`. |
| .NovaSkeleton-rounded | `rounded` | Class name applied to the root element if `variant="rounded"`. |
| .NovaSkeleton-circular | `circular` | Class name applied to the root element if `variant="circular"`. |
| .NovaSkeleton-pulse | `pulse` | Class name applied to the root element if `animation="pulse"`. |
| .NovaSkeleton-wave | `wave` | Class name applied to the root element if `animation="wave"`. |
| .NovaSkeleton-withChildren | `withChildren` | Class name applied when the component is passed children. |
| .NovaSkeleton-fitContent | `fitContent` | Class name applied when the component is passed children and no width. |
| .NovaSkeleton-heightAuto | `heightAuto` | Class name applied when the component is passed children and no height. |

