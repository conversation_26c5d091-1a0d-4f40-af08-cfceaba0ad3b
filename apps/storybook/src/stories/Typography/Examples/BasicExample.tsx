import { Typography, TypographyProps } from '@hxnova/react-components/Typography';

export default function BasicExample() {
  const variants: TypographyProps['variant'][] = [
    'displayLarge',
    'displayMedium',
    'displaySmall',
    'headlineLarge',
    'headlineMedium',
    'headlineSmall',
    'titleLarge',
    'titleMedium',
    'titleSmall',
    'bodyLarge',
    'bodyMedium',
    'bodySmall',
    'labelLarge',
    'labelMedium',
    'labelSmall',
  ];
  return (
    <div sx={{ display: 'flex', flexDirection: 'column' }}>
      {variants.map((variant) => (
        <Typography key={variant} variant={variant} sx={{ display: 'block' }}>
          {variant}
        </Typography>
      ))}
    </div>
  );
}
