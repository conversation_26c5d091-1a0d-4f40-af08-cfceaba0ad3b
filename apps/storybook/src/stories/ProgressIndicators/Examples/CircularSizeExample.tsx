import React from 'react';
import { CircularProgress } from '@hxnova/react-components/CircularProgress';

export default function CircularSizeExample() {
  return (
    <div>
      <div sx={{ marginBottom: '8px', fontWeight: 600 }}>Circular Progress with different sizes</div>
      <div sx={{ display: 'flex', flexDirection: 'row', gap: '24px', alignItems: 'center' }}>
        <CircularProgress size={24} />
        <CircularProgress size={40} />
        <CircularProgress size={56} />
        <CircularProgress size={72} />
      </div>

      <div sx={{ marginTop: '24px', marginBottom: '8px', fontWeight: 600 }}>Determinate with different sizes</div>
      <div sx={{ display: 'flex', flexDirection: 'row', gap: '24px', alignItems: 'center' }}>
        <CircularProgress variant="determinate" value={75} size={24} />
        <CircularProgress variant="determinate" value={75} size={40} />
        <CircularProgress variant="determinate" value={75} size={56} />
        <CircularProgress variant="determinate" value={75} size={72} />
      </div>
    </div>
  );
}
