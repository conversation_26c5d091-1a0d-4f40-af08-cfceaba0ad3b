import React from 'react';
import { LinearProgress } from '@hxnova/react-components/LinearProgress';

export default function LinearColorExample() {
  return (
    <div>
      <div sx={{ marginBottom: '8px', fontWeight: 600 }}>Linear Progress with different colors</div>
      <div sx={{ display: 'flex', flexDirection: 'column', gap: '16px', width: '100%', maxWidth: '400px' }}>
        <LinearProgress color="primary" />
        <LinearProgress color="error" />
        <LinearProgress color="success" />
        <LinearProgress color="info" />
        <LinearProgress color="warning" />
      </div>

      <div sx={{ marginTop: '24px', marginBottom: '8px', fontWeight: 600 }}>
        Determinate Linear Progress with different colors
      </div>
      <div sx={{ display: 'flex', flexDirection: 'column', gap: '16px', width: '100%', maxWidth: '400px' }}>
        <LinearProgress variant="determinate" value={75} color="primary" />
        <LinearProgress variant="determinate" value={75} color="error" />
        <LinearProgress variant="determinate" value={75} color="success" />
        <LinearProgress variant="determinate" value={75} color="info" />
        <LinearProgress variant="determinate" value={75} color="warning" />
      </div>
    </div>
  );
}
