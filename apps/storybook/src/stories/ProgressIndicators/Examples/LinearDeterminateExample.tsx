import React from 'react';
import { LinearProgress } from '@hxnova/react-components/LinearProgress';

export default function LinearDeterminateExample() {
  return (
    <div>
      <div sx={{ marginBottom: '8px', fontWeight: 600 }}>Linear Progress (Determinate)</div>
      <div sx={{ display: 'flex', flexDirection: 'column', gap: '16px', width: '100%', maxWidth: '400px' }}>
        <LinearProgress variant="determinate" value={25} />
        <LinearProgress variant="determinate" value={50} />
        <LinearProgress variant="determinate" value={75} />
        <LinearProgress variant="determinate" value={100} />
      </div>
    </div>
  );
}
