import React from 'react';
import { CircularProgress } from '@hxnova/react-components/CircularProgress';

export default function CircularDeterminateExample() {
  return (
    <div>
      <div sx={{ marginBottom: '8px', fontWeight: 600 }}>Circular Progress (Determinate)</div>
      <div sx={{ display: 'flex', flexDirection: 'row', gap: '24px' }}>
        <CircularProgress variant="determinate" value={25} />
        <CircularProgress variant="determinate" value={50} />
        <CircularProgress variant="determinate" value={75} />
        <CircularProgress variant="determinate" value={100} />
      </div>
    </div>
  );
}
