# API Documentation

- [CircularProgress](#circularprogress)
- [LinearProgress](#linearprogress)

# CircularProgress

API reference docs for the React CircularProgress component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `CircularProgress` component, you can choose to import it directly or through the main entry point.

```jsx
import { CircularProgress } from '@hxnova/react-components/CircularProgress';
// or
import { CircularProgress } from '@hxnova/react-components';
```

## Props

The properties available for the `CircularProgress` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **color** | ``"inherit" ⏐ "primary" ⏐ "error" ⏐ "info" ⏐ "success" ⏐ "warning"`` | `'primary'` | The color of the component. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **size** | `number` | `48` | The size of the component. |
| **slotProps** | ``{ root?: SlotProps<"span", object, CircularProgressOwnerState> ⏐ undefined; svg?: SlotProps<"svg", object, CircularProgressOwnerState> ⏐ undefined; track?: SlotProps<...> ⏐ undefined; progress?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `CircularProgressSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **thickness** | `number` | `3` | The thickness of the circle. |
| **value** | `number` | `0` | The value of the progress indicator for the determinate variant.<br>Value between 0 and 100. |
| **variant** | ``"determinate" ⏐ "indeterminate"`` | `'indeterminate'` | The variant to use.<br>Use indeterminate when there is no progress value. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `CircularProgress` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCircularProgress-root | `'span'` | The component that renders the root. |
| svg | .NovaCircularProgress-svg | `'svg'` | The component that renders the svg. |
| track | .NovaCircularProgress-track | `'circle'` | The component that renders the track. |
| progress | .NovaCircularProgress-progress | `'circle'` | The component that renders the progress. |

## CSS classes

CSS classes for different states and variations of the `CircularProgress` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCircularProgress-determinate | `determinate` | Class name applied to the root element if `variant="determinate"`. |
| .NovaCircularProgress-indeterminate | `indeterminate` | Class name applied to the root element if `variant="indeterminate"`. |
| .NovaCircularProgress-colorPrimary | `colorPrimary` | Class name applied to the root element if `color="primary"`. |
| .NovaCircularProgress-colorError | `colorError` | Class name applied to the root element if `color="error"`. |
| .NovaCircularProgress-colorInfo | `colorInfo` | Class name applied to the root element if `color="info"`. |
| .NovaCircularProgress-colorWarning | `colorWarning` | Class name applied to the root element if `color="warning"`. |
| .NovaCircularProgress-colorSuccess | `colorSuccess` | Class name applied to the root element if `color="success"`. |

<br><br>

# LinearProgress

API reference docs for the React LinearProgress component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `LinearProgress` component, you can choose to import it directly or through the main entry point.

```jsx
import { LinearProgress } from '@hxnova/react-components/LinearProgress';
// or
import { LinearProgress } from '@hxnova/react-components';
```

## Props

The properties available for the `LinearProgress` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **color** | ``"inherit" ⏐ "primary" ⏐ "error" ⏐ "info" ⏐ "success" ⏐ "warning"`` | `'primary'` | The color of the component. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, LinearProgressOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `LinearProgressSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **thickness** | `number` | `4` | The thickness of the bar. |
| **value** | `number` | `variant === 'determinate' ? 0 : 25` | The value of the progress indicator for the determinate and buffer variants.<br>Value between 0 and 100. |
| **variant** | ``"determinate" ⏐ "indeterminate"`` | `'indeterminate'` | The variant to use.<br>Use indeterminate or query when there is no progress value. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `LinearProgress` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaLinearProgress-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `LinearProgress` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaLinearProgress-colorPrimary | `colorPrimary` | Class name applied to the root element if `color="primary"`; |
| .NovaLinearProgress-determinate | `determinate` | Class name applied to the root element if `variant="determinate"`. |
| .NovaLinearProgress-indeterminate | `indeterminate` | Class name applied to the root element if `variant="indeterminate"`. |

