import { useState } from 'react';
import { Slide } from '@hxnova/react-components/Slide';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function BasicExample() {
  const [checked, setChecked] = useState(false);

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
          Basic Slide Transition
        </Typography>
        <div sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
          <Switch checked={checked} onChange={() => setChecked((prev) => !prev)} endDecorator="Toggle Content" />
          <div
            sx={{
              width: 300,
              height: 200,
              position: 'relative',
              overflow: 'hidden',
              border: '1px solid var(--palette-outline)',
              borderRadius: 'var(--radius-md)',
            }}
          >
            <Slide in={checked} direction="down" timeout={300}>
              <Box
                sx={{
                  padding: '16px',
                  backgroundColor: 'var(--palette-surfaceContainer)',
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  textAlign: 'center',
                }}
              >
                <Typography variant="bodyMedium">This content slides down smoothly when toggled.</Typography>
              </Box>
            </Slide>
          </div>
        </div>
      </div>

      <div>
        <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
          Different Directions
        </Typography>
        <div sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '16px' }}>
          {(['left', 'right', 'up', 'down'] as const).map((direction) => (
            <div key={direction} sx={{ textAlign: 'center' }}>
              <Typography variant="bodySmall" sx={{ marginBottom: '8px', textTransform: 'capitalize' }}>
                Slide {direction}
              </Typography>
              <div
                sx={{
                  width: 150,
                  height: 120,
                  position: 'relative',
                  overflow: 'hidden',
                  border: '1px solid var(--palette-outline)',
                  borderRadius: 'var(--radius-sm)',
                  margin: '0 auto',
                }}
              >
                <Slide in={checked} direction={direction} timeout={400}>
                  <Box
                    sx={{
                      padding: '8px',
                      backgroundColor:
                        direction === 'left'
                          ? 'var(--palette-primaryContainer)'
                          : direction === 'right'
                            ? 'var(--palette-secondaryContainer)'
                            : direction === 'up'
                              ? 'var(--palette-tertiaryContainer)'
                              : 'var(--palette-errorContainer)',
                      color:
                        direction === 'left'
                          ? 'var(--palette-onPrimaryContainer)'
                          : direction === 'right'
                            ? 'var(--palette-onSecondaryContainer)'
                            : direction === 'up'
                              ? 'var(--palette-onTertiaryContainer)'
                              : 'var(--palette-onSurface)',
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      textAlign: 'center',
                    }}
                  >
                    <Typography variant="bodySmall">{direction}</Typography>
                  </Box>
                </Slide>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
