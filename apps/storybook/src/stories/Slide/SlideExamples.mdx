import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand'; 
import { Slide } from '@hxnova/react-components/Slide';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';

<Meta title="@hxnova/react-components/Transitions/Slide/Examples" />

## Slide Transition

The Slide component provides smooth sliding transitions for showing and hiding content. It slides elements in from different directions using the **`direction` prop** (left, right, up, down) via CSS transforms. The component is built on top of `react-transition-group` and offers flexible timing and easing options.

### Basic Usage

The Slide component animates content by sliding it in from the specified direction. The **`direction` prop** controls where the content slides from (left, right, up, down). Use the **`timeout` prop** to control the animation duration in milliseconds (e.g., `timeout={300}` for a 300ms animation).

<div className="doc-story sb-story sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>
