# API Documentation

- [Slide](#slide)

# Slide

API reference docs for the React Slide component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Slide` component, you can choose to import it directly or through the main entry point.

```jsx
import { Slide } from '@hxnova/react-components/Slide';
// or
import { Slide } from '@hxnova/react-components';
```

## Props

The properties available for the `Slide` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component*** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **appear** | ``false ⏐ true`` | `true` | Perform the enter transition when it first mounts if `in` is also `true`.<br>Set this to `false` to disable this behavior.<br>Normally a component is not transitioned if it is shown when the<br>`<Transition>` component mounts. If you want to transition on the first<br>mount set  appear to true, and the component will transition in as soon<br>as the `<Transition>` mounts. Note: there are no specific "appear" states.<br>appear only adds an additional enter transition. |
| **children** | ``ReactElement<unknown, any> & ReactNode`` | - | A single child content element. |
| **container** | ``null ⏐ Element ⏐ (element: Element) => Element`` | - | An HTML element, or a function that returns one.<br>It's used to set the container the Slide is transitioning from. |
| **direction** | ``"left" ⏐ "right" ⏐ "up" ⏐ "down"`` | `'down'` | Direction the child node will enter from. |
| **easing** | ``string ⏐ { enter?: string ⏐ undefined; exit?: string ⏐ undefined; }`` | `{ enter: easing.easeOut, exit: easing.sharp, }` | The transition timing function.<br>You may specify a single easing or a object containing enter and exit values. |
| **in** | ``false ⏐ true`` | - | If `true`, the component will transition in.<br>Show the component; triggers the enter or exit states |
| **ref** | ``Ref<unknown>`` | - |  |
| **timeout** | ``number ⏐ { appear?: number ⏐ undefined; enter?: number ⏐ undefined; exit?: number ⏐ undefined; } ⏐ { appear?: number ⏐ undefined; enter?: number ⏐ undefined; exit?: number ⏐ undefined; }`` | `{ enter: duration.enteringScreen, exit: duration.leavingScreen, }` | The duration for the transition, in milliseconds.<br>You may specify a single timeout for all transitions, or individually with an object.<br>The duration of the transition, in milliseconds. Required unless addEndListener is provided.<br>You may specify a single timeout for all transitions:<br>```js<br>timeout={500}<br>```<br>or individually:<br>```js<br>timeout={{<br>appear: 500,<br>enter: 300,<br>exit: 500,<br>}}<br>```<br>- appear defaults to the value of `enter`<br>- enter defaults to `0`<br>- exit defaults to `0` |
| **TransitionComponent** | ``ComponentClass<any, any> ⏐ FunctionComponent<any>`` | `Transition` | The component used for the transition. |

