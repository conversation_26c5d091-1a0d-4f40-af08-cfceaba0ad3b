import { useState } from 'react';
import type { Meta, StoryFn, StoryObj } from '@storybook/react';
import { Slide, SlideProps } from '@hxnova/react-components/Slide';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

const meta = {
  title: '@hxnova/react-components/Transitions/Slide',
  component: Slide,
  parameters: {
    layout: 'centered',
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof Slide>;

export default meta;
type Story = StoryObj<typeof meta>;

const Template: StoryFn<(props: SlideProps) => JSX.Element> = ({ ...args }) => {
  const [checked, setChecked] = useState(false);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px', padding: '40px' }}>
      <Switch checked={checked} onChange={() => setChecked((prev: boolean) => !prev)} endDecorator="Toggle Slide" />
      <div
        style={{
          width: '400px',
          height: '300px',
          position: 'relative',
          overflow: 'hidden',
          border: '1px solid var(--palette-outline)',
          borderRadius: 'var(--radius-md)',
        }}
      >
        <Slide key={args.direction} {...args} in={checked}>
          <Box
            sx={{
              padding: '16px',
              backgroundColor: 'var(--palette-primaryContainer)',
              color: 'var(--palette-onPrimaryContainer)',
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              textAlign: 'center',
            }}
          >
            <Typography variant="bodyLarge">
              This content slides {args.direction} with smooth animation when toggled.
            </Typography>
          </Box>
        </Slide>
      </div>
    </div>
  );
};

export const Default: Story = {
  render: Template,
  args: {
    appear: true,
    direction: 'down',
    timeout: 300,
    children: <div />,
  },
  argTypes: {
    direction: {
      control: 'select',
      options: ['left', 'right', 'up', 'down'],
    },
    timeout: {
      control: 'select',
      options: [100, 300, 1000],
    },
  },
  parameters: {
    controls: {
      include: ['direction', 'timeout'],
    },
  },
};
