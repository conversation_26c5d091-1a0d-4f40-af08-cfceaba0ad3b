# API Documentation

- [Grid](#grid)

# Grid

API reference docs for the React Grid component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Grid` component, you can choose to import it directly or through the main entry point.

```jsx
import { Grid } from '@hxnova/react-components/Grid';
// or
import { Grid } from '@hxnova/react-components';
```

## Props

The properties available for the `Grid` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **columns** | ``number ⏐ (number ⏐ null)[] ⏐ { xs?: number ⏐ null ⏐ undefined; sm?: number ⏐ null ⏐ undefined; md?: number ⏐ null ⏐ undefined; lg?: number ⏐ null ⏐ undefined; xl?: number ⏐ null ⏐ undefined; }`` | `12` | The number of columns. |
| **columnSpacing** | ``string ⏐ number ⏐ (GridSpacing ⏐ null)[] ⏐ { xs?: GridSpacing ⏐ null ⏐ undefined; sm?: GridSpacing ⏐ null ⏐ undefined; md?: GridSpacing ⏐ null ⏐ undefined; lg?: GridSpacing ⏐ null ⏐ undefined; xl?: GridSpacing ⏐ ... 1 more ... ⏐ undefined; }`` | - | Defines the horizontal space between the type `item` components.<br>It overrides the value of the `spacing` prop. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **container** | ``false ⏐ true`` | `false` | If `true`, the component will have the flex *container* behavior.<br>You should be wrapping *items* with a *container*. |
| **direction** | ``"row" ⏐ "row-reverse" ⏐ "column" ⏐ "column-reverse" ⏐ (GridDirection ⏐ null)[] ⏐ { xs?: GridDirection ⏐ null ⏐ undefined; sm?: GridDirection ⏐ null ⏐ undefined; md?: GridDirection ⏐ null ⏐ undefined; lg?: GridDirection ⏐ null ⏐ undefined; xl?: GridDirection ⏐ ... 1 more ... ⏐ undefined; }`` | `'row'` | Defines the `flex-direction` style property.<br>It is applied for all screen sizes. |
| **offset** | ``number ⏐ (number ⏐ null)[] ⏐ { xs?: number ⏐ null ⏐ undefined; sm?: number ⏐ null ⏐ undefined; md?: number ⏐ null ⏐ undefined; lg?: number ⏐ null ⏐ undefined; xl?: number ⏐ null ⏐ undefined; }`` | - | Defines the offset of the grid. |
| **rowSpacing** | ``string ⏐ number ⏐ (GridSpacing ⏐ null)[] ⏐ { xs?: GridSpacing ⏐ null ⏐ undefined; sm?: GridSpacing ⏐ null ⏐ undefined; md?: GridSpacing ⏐ null ⏐ undefined; lg?: GridSpacing ⏐ null ⏐ undefined; xl?: GridSpacing ⏐ ... 1 more ... ⏐ undefined; }`` | - | Defines the vertical space between the type `item` components.<br>It overrides the value of the `spacing` prop. |
| **size** | ``number ⏐ "auto" ⏐ "grow" ⏐ (GridSize ⏐ null)[] ⏐ { xs?: GridSize ⏐ null ⏐ undefined; sm?: GridSize ⏐ null ⏐ undefined; md?: GridSize ⏐ null ⏐ undefined; lg?: GridSize ⏐ null ⏐ undefined; xl?: GridSize ⏐ ... 1 more ... ⏐ undefined; }`` | - | Defines the column size of the grid. |
| **spacing** | ``string ⏐ number ⏐ (GridSpacing ⏐ null)[] ⏐ { xs?: GridSpacing ⏐ null ⏐ undefined; sm?: GridSpacing ⏐ null ⏐ undefined; md?: GridSpacing ⏐ null ⏐ undefined; lg?: GridSpacing ⏐ null ⏐ undefined; xl?: GridSpacing ⏐ ... 1 more ... ⏐ undefined; }`` | `0` | Defines the space between the type `item` components.<br>It can only be used on a type `container` component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **wrap** | ``"nowrap" ⏐ "wrap" ⏐ "wrap-reverse"`` | `'wrap'` | Defines the `flex-wrap` style property.<br>It's applied for all screen sizes. |

## CSS classes

CSS classes for different states and variations of the `Grid` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaGrid-root | `root` | Class name applied to the root element. |
| .NovaGrid-container | `container` | Class name applied to the root element if 'container={true}'. |

