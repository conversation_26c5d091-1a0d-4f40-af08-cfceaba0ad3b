# API Documentation

- [Tooltip](#tooltip)

# Tooltip

API reference docs for the React Tooltip component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Tooltip` component, you can choose to import it directly or through the main entry point.

```jsx
import { Tooltip } from '@hxnova/react-components/Tooltip';
// or
import { Tooltip } from '@hxnova/react-components';
```

## Props

The properties available for the `Tooltip` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **title*** | `ReactNode` | - | The content to be displayed in the tooltip. |
| **className** | `string` | - | Class name applied to the root element. |
| **placement** | ``"top" ⏐ "right" ⏐ "bottom" ⏐ "left"`` | `'top'` | The placement of the trigger element to place the tooltip. |
| **showArrow** | ``false ⏐ true`` | `false` | Whether to show the arrow. |
| **slotProps** | ``{ provider?: Omit<Props, "children">; root?: Omit<Props, "children"> ⏐ undefined; trigger?: Omit<Props & RefAttributes<any>, "children"> ⏐ undefined; portal?: Omit<...> ⏐ undefined; positioner?: Omit<...> ⏐ undefined; popup?: Omit<...> ⏐ undefined; arrow?: Omit<...> ⏐ undefined; } ⏐ undefined`` | - | Props passed to the sub-components. |

## CSS classes

CSS classes for different states and variations of the `Tooltip` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaTooltip-root | `root` | Class name applied to the root element. |
| .NovaTooltip-trigger | `trigger` | Class name applied to the trigger element. |
| .NovaTooltip-portal | `portal` | Class name applied to the portal element. |
| .NovaTooltip-positioner | `positioner` | Class name applied to the positioner element. |
| .NovaTooltip-popup | `popup` | Class name applied to the popup element. |
| .NovaTooltip-arrow | `arrow` | Class name applied to the arrow element. |
| .Nova-open | `open` | Class name applied to the root element when the tooltip is open. |
| .NovaTooltip-closed | `closed` | Class name applied to the root element when the tooltip is closed. |

