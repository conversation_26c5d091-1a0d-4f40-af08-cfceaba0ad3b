import { Tooltip } from '@hxnova/react-components/Tooltip';
import { Button } from '@hxnova/react-components/Button';

export default function PlacementExample() {
  return (
    <div sx={{ display: 'flex', gap: '24px', flexWrap: 'wrap' }}>
      <Tooltip title="Top" placement="top">
        <Button variant="outlined">Top</Button>
      </Tooltip>
      <Tooltip title="Bottom" placement="bottom">
        <Button variant="outlined">Bottom</Button>
      </Tooltip>
      <Tooltip title="Left" placement="left">
        <Button variant="outlined">Left</Button>
      </Tooltip>
      <Tooltip title="Right" placement="right">
        <Button variant="outlined">Right</Button>
      </Tooltip>
    </div>
  );
}
