import { Tooltip } from '@hxnova/react-components/Tooltip';
import { But<PERSON> } from '@hxnova/react-components/Button';

export default function ControlledExample() {
  return (
    <div sx={{ padding: '32px 0', display: 'flex', gap: '24px', flexWrap: 'wrap' }}>
      <Tooltip title="Always visible" open>
        <Button variant="outlined">Controlled (Always Open)</Button>
      </Tooltip>
      <Tooltip title="Also always visible" placement="right" open>
        <Button variant="outlined">Another Example</Button>
      </Tooltip>
    </div>
  );
}
