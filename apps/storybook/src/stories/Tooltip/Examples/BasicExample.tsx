import { Tooltip } from '@hxnova/react-components/Tooltip';
import { Button } from '@hxnova/react-components/Button';

export default function BasicExample() {
  return (
    <div sx={{ display: 'flex', gap: '24px', alignItems: 'center' }}>
      <Tooltip title="Basic tooltip">
        <Button variant="outlined">Hover me</Button>
      </Tooltip>
      <Tooltip showArrow title="With arrow">
        <Button variant="outlined">With arrow</Button>
      </Tooltip>
    </div>
  );
}
