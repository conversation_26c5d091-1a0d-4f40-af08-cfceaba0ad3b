# API Documentation

- [Card.Actions](#cardactions)
- [Card.Content](#cardcontent)
- [Card.Header](#cardheader)
- [Card.Media](#cardmedia)
- [Card.Root](#cardroot)

# Card.Actions

API reference docs for the React Card.Actions component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Card.Actions` component, you can choose to import it directly or through the main entry point.

```jsx
import { Card } from '@hxnova/react-components/Card';
// or
import { Card } from '@hxnova/react-components';

// usage
<Card.Actions>{children}</Card.Actions>
```

## Props

The properties available for the `Card.Actions` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, CardActionsOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `CardActionsSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Card.Actions` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCardActions-root | `'div'` | The component that renders the root slot. |

<br><br>

# Card.Content

API reference docs for the React Card.Content component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Card.Content` component, you can choose to import it directly or through the main entry point.

```jsx
import { Card } from '@hxnova/react-components/Card';
// or
import { Card } from '@hxnova/react-components';

// usage
<Card.Content>{children}</Card.Content>
```

## Props

The properties available for the `Card.Content` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | `ReactNode` | - | The action to display in the card header. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **endDecorator** | `ReactNode` | - | The end decorator of the component. |
| **orientation** | ``"vertical" ⏐ "horizontal"`` | `'vertical'` | The orientation of the card content. |
| **slotProps** | ``{ root?: SlotProps<"div", object, CardContentOwnerState> ⏐ undefined; body?: SlotProps<"div", object, CardContentOwnerState> ⏐ undefined; title?: SlotProps<...> ⏐ undefined; subtitle?: SlotProps<...> ⏐ undefined; supportingText?: SlotProps<...> ⏐ undefined; action?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `CardContentSlots` | `{}` | The components used for each slot inside. |
| **startDecorator** | `ReactNode` | - | The start decorator of the component. |
| **subtitle** | `ReactNode` | - | The header of the component. |
| **supportingText** | `ReactNode` | - | The subheader of the component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **title** | `ReactNode` | - | The Avatar element to display. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Card.Content` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCardContent-root | `'div'` | The component that renders the root slot. |
| body | .NovaCardContent-body | `'div'` | The component that renders the body slot. |
| title | .NovaCardContent-title | `Typography` | The component that renders the title slot. |
| subtitle | .NovaCardContent-subtitle | `Typography` | The component that renders the subtitle slot. |
| supportingText | .NovaCardContent-supportingText | `Typography` | The component that renders the supportingText slot. |
| action | .NovaCardContent-action | `'div'` | The component that renders the action slot. |

## CSS classes

CSS classes for different states and variations of the `Card.Content` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCardContent-bodyMain | `bodyMain` | Class name applied to the bodyMain element. |
| .NovaCardContent-orientationVertical | `orientationVertical` | Class name applied to the root element if `orientation="vertical"`. |
| .NovaCardContent-orientationHorizontal | `orientationHorizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .Nova-disabled | `disabled` | Class name applied to the title, subtitle and supportingText elements if `disabled={true}`. |

<br><br>

# Card.Header

API reference docs for the React Card.Header component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Card.Header` component, you can choose to import it directly or through the main entry point.

```jsx
import { Card } from '@hxnova/react-components/Card';
// or
import { Card } from '@hxnova/react-components';

// usage
<Card.Header>{children}</Card.Header>
```

## Props

The properties available for the `Card.Header` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | `ReactNode` | - | The action to display in the card header. |
| **avatar** | `ReactNode` | - | The Avatar element to display. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **heading** | `ReactNode` | - | The heading of the component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, CardHeaderOwnerState> ⏐ undefined; avatar?: SlotProps<"div", object, CardHeaderOwnerState> ⏐ undefined; action?: SlotProps<...> ⏐ undefined; content?: SlotProps<...> ⏐ undefined; heading?: SlotProps<...> ⏐ undefined; subheading?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `CardHeaderSlots` | `{}` | The components used for each slot inside. |
| **subheading** | `ReactNode` | - | The subheading of the component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Card.Header` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCardHeader-root | `'div'` | The component that renders the root slot. |
| avatar | .NovaCardHeader-avatar | `'div'` | The component that renders the avatar slot. |
| action | .NovaCardHeader-action | `'div'` | The component that renders the action slot. |
| content | .NovaCardHeader-content | `'div'` | The component that renders the content slot. |
| heading | .NovaCardHeader-heading | `Typography` | The component that renders the heading slot. |
| subheading | .NovaCardHeader-subheading | `Typography` | The component that renders the subheading slot. |

## CSS classes

CSS classes for different states and variations of the `Card.Header` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Class name applied to the heading and subheading element if `disabled={true}`. |

<br><br>

# Card.Media

API reference docs for the React Card.Media component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Card.Media` component, you can choose to import it directly or through the main entry point.

```jsx
import { Card } from '@hxnova/react-components/Card';
// or
import { Card } from '@hxnova/react-components';

// usage
<Card.Media>{children}</Card.Media>
```

## Props

The properties available for the `Card.Media` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **image** | `string` | - | Image to be displayed as a background image.<br>Either `image` or `src` prop must be specified.<br>Note that caller must specify height otherwise the image will not be visible. |
| **slotProps** | ``{ root?: SlotProps<"div", object, CardMediaOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `CardMediaSlots` | `{}` | The components used for each slot inside. |
| **src** | `string` | - | An alias for `image` property.<br>Available only with media components.<br>Media components: `video`, `audio`, `picture`, `iframe`, `img`. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Card.Media` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCardMedia-root | `'div'` |  |

## CSS classes

CSS classes for different states and variations of the `Card.Media` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCardMedia-media | `media` | Class name applied to the root element if `component="video, audio, picture, iframe, or img"`. |
| .NovaCardMedia-img | `img` | Class name applied to the root element if `component="picture or img"`. |

<br><br>

# Card.Root

API reference docs for the React Card.Root component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Card.Root` component, you can choose to import it directly or through the main entry point.

```jsx
import { Card } from '@hxnova/react-components/Card';
// or
import { Card } from '@hxnova/react-components';

// usage
<Card.Root>{children}</Card.Root>
```

## Props

The properties available for the `Card.Root` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | `false` | Indicate the disabled state of the card. |
| **orientation** | ``"vertical" ⏐ "horizontal"`` | `'vertical'` | The orientation of the card. |
| **slotProps** | ``{ root?: SlotProps<"div", object, CardRootOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `CardRootSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Card.Root` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaCardRoot-root | `'div'` | The component that renders the root slot. |

## CSS classes

CSS classes for different states and variations of the `Card.Root` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaCardRoot-orientationVertical | `orientationVertical` | Class name applied to the root element if `orientation="vertical"`. |
| .NovaCardRoot-orientationHorizontal | `orientationHorizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .Nova-disabled | `disabled` | Class name applied to the root element if `disabled={true}`. |

