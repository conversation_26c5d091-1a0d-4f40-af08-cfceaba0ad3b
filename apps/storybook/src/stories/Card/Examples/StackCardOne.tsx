import { Card } from '@hxnova/react-components/Card';
import { Button } from '@hxnova/react-components/Button';

export default function Example() {
  return (
    <Card.Root sx={{ width: '360px' }}>
      <Card.Content title={'HxAuth Prod'} supportingText={'Yesterday at 11:41 AM'} />
      <Card.Actions>
        <Button variant="outlined" size="small">
          Cancel request
        </Button>
      </Card.Actions>
    </Card.Root>
  );
}
