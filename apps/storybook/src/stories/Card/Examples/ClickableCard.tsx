import { Card } from '@hxnova/react-components/Card';
import { Avatar } from '@hxnova/react-components/Avatar';
import avatarFirst from '../../Avatar/Examples/images/avatar_first.png';
import avatarSecond from '../../Avatar/Examples/images/avatar_second.png';
import avatarThird from '../../Avatar/Examples/images/avatar_third.png';
import { Checkbox } from '@hxnova/react-components/Checkbox';
export default function Example() {
  return (
    <Card.Root
      sx={{ width: '360px' }}
      onClick={() => {
        console.log('Card click');
      }}
    >
      <Card.Content
        title={'Title'}
        subtitle={'Subtitle'}
        supportingText={'Supporting text'}
        action={<Checkbox />}
        endDecorator={
          <div sx={{ display: 'flex', gap: '8px' }}>
            <Avatar src={avatarFirst} />
            <Avatar src={avatarSecond} />
            <Avatar src={avatarThird} />
          </div>
        }
      />
    </Card.Root>
  );
}
