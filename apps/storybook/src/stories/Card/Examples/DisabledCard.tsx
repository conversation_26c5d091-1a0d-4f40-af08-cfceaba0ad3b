import { Card } from '@hxnova/react-components/Card';
import { Avatar } from '@hxnova/react-components/Avatar';
import avatarFirst from '../../Avatar/Examples/images/avatar_first.png';
import avatarSecond from '../../Avatar/Examples/images/avatar_second.png';
import avatarThird from '../../Avatar/Examples/images/avatar_third.png';
import { Checkbox } from '@hxnova/react-components/Checkbox';
export default function Example() {
  return (
    <Card.Root sx={{ width: '360px' }} disabled>
      <Card.Content
        title={'Title'}
        subtitle={'Subtitle'}
        supportingText={'Supporting text'}
        action={<Checkbox disabled />}
        endDecorator={
          <div sx={{ display: 'flex', gap: '8px' }}>
            <Avatar disabled src={avatarFirst} />
            <Avatar disabled src={avatarSecond} />
            <Avatar disabled src={avatarThird} />
          </div>
        }
      />
    </Card.Root>
  );
}
