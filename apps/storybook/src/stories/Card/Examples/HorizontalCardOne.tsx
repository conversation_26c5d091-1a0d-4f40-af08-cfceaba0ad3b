import React from 'react';
import { Card } from '@hxnova/react-components/Card';
import { IconButton } from '@hxnova/react-components/IconButton';
import Icon from '@hxnova/icons/Icon';

export default function Example() {
  return (
    <Card.Root orientation="horizontal" sx={{ width: '400px' }}>
      <Card.Content title={'Project name'} supportingText={'10MB'} />
      <Card.Actions>
        <IconButton variant="neutral" aria-label="settings">
          <Icon family="material" name="more_vert" size={24} />
        </IconButton>
      </Card.Actions>
    </Card.Root>
  );
}
