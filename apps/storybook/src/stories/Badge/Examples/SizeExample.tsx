import { Badge } from '@hxnova/react-components/Badge';
import Icon from '@hxnova/icons/Icon';

export default function SizeExample() {
  return (
    <div sx={{ display: 'flex', gap: 24 }}>
      <Badge size={'small'}>
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={4} size={'large'}>
        <Icon family="material" name="mail" size={24} />
      </Badge>
    </div>
  );
}
