import { Badge } from '@hxnova/react-components/Badge';
import Icon from '@hxnova/icons/Icon';

export default function AlignmentExample() {
  return (
    <div sx={{ display: 'flex', gap: 24 }}>
      <Badge badgeContent={4} anchorOrigin={{ vertical: 'top', horizontal: 'left' }}>
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={4} anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}>
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={4} anchorOrigin={{ vertical: 'top', horizontal: 'right' }}>
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={4} anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
        <Icon family="material" name="mail" size={24} />
      </Badge>
    </div>
  );
}
