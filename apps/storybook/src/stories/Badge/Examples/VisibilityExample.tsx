import { Badge } from '@hxnova/react-components/Badge';
import Icon from '@hxnova/icons/Icon';

export default function VisibilityExample() {
  return (
    <div sx={{ display: 'flex', gap: 24 }}>
      <Badge badgeContent={4}>
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={4} invisible>
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={0} showZero>
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={0}>
        <Icon family="material" name="mail" size={24} />
      </Badge>
    </div>
  );
}
