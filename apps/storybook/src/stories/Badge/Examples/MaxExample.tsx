import { Badge } from '@hxnova/react-components/Badge';
import Icon from '@hxnova/icons/Icon';

export default function MaxExample() {
  return (
    <div sx={{ display: 'flex', gap: 32 }}>
      <Badge badgeContent={99}>
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={100}>
        <Icon family="material" name="mail" size={24} />
      </Badge>
      <Badge badgeContent={1000} max={999}>
        <Icon family="material" name="mail" size={24} />
      </Badge>
    </div>
  );
}
