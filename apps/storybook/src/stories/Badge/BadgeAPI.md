# API Documentation

- [Badge](#badge)

# Badge

API reference docs for the React Badge component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Badge` component, you can choose to import it directly or through the main entry point.

```jsx
import { Badge } from '@hxnova/react-components/Badge';
// or
import { Badge } from '@hxnova/react-components';
```

## Props

The properties available for the `Badge` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **anchorOrigin** | ``{ vertical: "top" ⏐ "bottom"; horizontal: "left" ⏐ "right"; }`` | `{ vertical: 'top', horizontal: 'right', }` | The anchor of the badge. |
| **badgeContent** | `ReactNode` | - | The content rendered within the badge. |
| **children** | `ReactNode` | - | The badge will be added relative to this node. |
| **color** | ``"primary" ⏐ "error" ⏐ "info" ⏐ "warning" ⏐ "success"`` | `'primary'` | The system color of badge |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | - | The disable state of badge |
| **invisible** | ``false ⏐ true`` | `false` | If `true`, the badge is invisible. |
| **max** | `number` | `99` | Max count to show. |
| **showZero** | ``false ⏐ true`` | `false` | Controls whether the badge is hidden when `badgeContent` is zero. |
| **size** | ``"small" ⏐ "large"`` | `'large'` | The badge size 'small' or 'large' |
| **slotProps** | ``{ root?: SlotProps<"span", BadgeRootSlotPropsOverrides, BadgeOwnerState> ⏐ undefined; badge?: SlotProps<"span", BadgeBadgeSlotPropsOverrides, BadgeOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `BadgeSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Badge` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaBadge-root | `'span'` | The component that renders the root. |
| badge | .NovaBadge-badge | `'span'` | The component that renders the badge. |

## CSS classes

CSS classes for different states and variations of the `Badge` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaBadge-sizeSmall | `sizeSmall` | Class name applied to the badge `span` element if `size="small"`. |
| .NovaBadge-sizeLarge | `sizeLarge` | Class name applied to the badge `span` element if `size="large"`. |
| .NovaBadge-anchorOriginTopRight | `anchorOriginTopRight` | Class name applied to the badge `span` element if `anchorOrigin={{ 'top', 'right' }}`. |
| .NovaBadge-anchorOriginBottomRight | `anchorOriginBottomRight` | Class name applied to the badge `span` element if `anchorOrigin={{ 'bottom', 'right' }}`. |
| .NovaBadge-anchorOriginTopLeft | `anchorOriginTopLeft` | Class name applied to the badge `span` element if `anchorOrigin={{ 'top', 'left' }}`. |
| .NovaBadge-anchorOriginBottomLeft | `anchorOriginBottomLeft` | Class name applied to the badge `span` element if `anchorOrigin={{ 'bottom', 'left' }}`. |
| .NovaBadge-invisible | `invisible` | State class applied to the badge `span` element if `invisible={true}`. |
| .NovaBadge-colorPrimary | `colorPrimary` | Class name applied to the badge `span` element if `color="primary"`. |
| .NovaBadge-colorError | `colorError` | Class name applied to the badge `span` element if `color="error"`. |
| .NovaBadge-colorInfo | `colorInfo` | Class name applied to the badge `span` element if `color="info"`. |
| .NovaBadge-colorWarning | `colorWarning` | Class name applied to the badge `span` element if `color="warning"`. |
| .NovaBadge-colorSuccess | `colorSuccess` | Class name applied to the badge `span` element if `color="success"`. |
| .Nova-disabled | `disabled` | Class name applied to the badge `span` element if `disabled` is true. |

