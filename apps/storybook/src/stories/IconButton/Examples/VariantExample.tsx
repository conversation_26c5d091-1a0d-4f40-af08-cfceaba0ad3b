import { IconButton } from '@hxnova/react-components/IconButton';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <IconButton variant={'filled'}>
        <Icon family="material" name="edit" />
      </IconButton>
      <IconButton variant={'outlined'}>
        <Icon family="material" name="edit" />
      </IconButton>
      <IconButton variant={'standard'}>
        <Icon family="material" name="edit" />
      </IconButton>
      <IconButton variant={'neutral'}>
        <Icon family="material" name="edit" />
      </IconButton>
    </div>
  );
}
