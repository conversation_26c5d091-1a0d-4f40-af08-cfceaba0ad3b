import { IconButton } from '@hxnova/react-components/IconButton';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <IconButton size={'small'}>
        <Icon family="material" name="edit" />
      </IconButton>
      <IconButton size={'medium'}>
        <Icon family="material" name="edit" />
      </IconButton>
      <IconButton size={'large'}>
        <Icon family="material" name="edit" />
      </IconButton>
    </div>
  );
}
