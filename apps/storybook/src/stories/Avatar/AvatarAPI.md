# API Documentation

- [Avatar](#avatar)
- [AvatarGroup](#avatargroup)

# Avatar

API reference docs for the React Avatar component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Avatar` component, you can choose to import it directly or through the main entry point.

```jsx
import { Avatar } from '@hxnova/react-components/Avatar';
// or
import { Avatar } from '@hxnova/react-components';
```

## Props

The properties available for the `Avatar` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **alt** | `string` | - | Used in combination with `src` or `srcSet` to<br>provide an alt attribute for the rendered `img` element. |
| **children** | `ReactNode` | - | Used to render icon or text elements inside the Avatar if `src` is not set.<br>This can be an element, or just a string. |
| **color** | ``"primary" ⏐ "error" ⏐ "info" ⏐ "warning" ⏐ "success"`` | - | The  status badge color of the component. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | - | The disable state of the avatar |
| **size** | ``"small" ⏐ "large" ⏐ "medium"`` | `'medium'` | The overall size of the button. |
| **slotProps** | ``{ root?: SlotProps<"div", object, AvatarOwnerState> ⏐ undefined; img?: SlotProps<"img", object, AvatarOwnerState> ⏐ undefined; fallback?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `AvatarSlots` | `{}` | The components used for each slot inside. |
| **src** | `string` | - | The `src` attribute for the `img` element. |
| **srcSet** | `string` | - | The `srcSet` attribute for the `img` element.<br>Use this attribute for responsive image display. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Avatar` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAvatar-root | `'div'` | The component that renders the root. |
| img | .NovaAvatar-img | `'img'` | The component that renders the img. |
| fallback | .NovaAvatar-fallback | `'svg'` | The component that renders the fallback. |

## CSS classes

CSS classes for different states and variations of the `Avatar` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaAvatar-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaAvatar-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaAvatar-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |
| .Nova-focusVisible | `focusVisible` | Class name applied to the root element if the link is keyboard focused. |

<br><br>

# AvatarGroup

API reference docs for the React AvatarGroup component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `AvatarGroup` component, you can choose to import it directly or through the main entry point.

```jsx
import { AvatarGroup } from '@hxnova/react-components/AvatarGroup';
// or
import { AvatarGroup } from '@hxnova/react-components';
```

## Props

The properties available for the `AvatarGroup` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **color** | ``"primary" ⏐ "error" ⏐ "info" ⏐ "warning" ⏐ "success"`` | - | The  status badge color of the component. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | - | The disable state of the avatar |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The overall size of the button. |
| **slotProps** | ``{ root?: SlotProps<"div", object, AvatarGroupOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `AvatarGroupSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `AvatarGroup` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaAvatarGroup-root | `'div'` | The component that renders the root. |

