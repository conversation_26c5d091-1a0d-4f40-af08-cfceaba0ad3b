import * as React from 'react';
import { Avatar } from '@hxnova/react-components/Avatar';
import avatarFirst from './images/avatar_first.png';

export default function AvatarStatus() {
  return (
    <div sx={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
      <Avatar alt="Remy Sharp" src={avatarFirst} color="primary" />
      <Avatar alt="Remy Sharp" src={avatarFirst} color="error" />
      <Avatar alt="Remy Sharp" src={avatarFirst} color="info" />
      <Avatar alt="Remy Sharp" src={avatarFirst} color="warning" />
      <Avatar alt="Remy Sharp" src={avatarFirst} color="success" />
    </div>
  );
}
