# API Documentation

- [SideSheet.Root](#sidesheetroot)

# SideSheet.Root

API reference docs for the React SideSheet.Root component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `SideSheet.Root` component, you can choose to import it directly or through the main entry point.

```jsx
import { SideSheet } from '@hxnova/react-components/SideSheet';
// or
import { SideSheet } from '@hxnova/react-components';

// usage
<SideSheet.Root>{children}</SideSheet.Root>
```

## Props

The properties available for the `SideSheet.Root` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **open*** | ``false ⏐ true`` | - | If `true`, the component is shown. |
| **anchor** | ``"left" ⏐ "right"`` | `'right'` | Side from which the side sheet will appear. |
| **children** | `ReactNode` | - | The content of the component. |
| **closeAfterTransition** | ``false ⏐ true`` | `false` | When set to true the Modal waits until a nested Transition is completed before closing. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **container** | ``null ⏐ Element ⏐ () => Element ⏐ null`` | - | An HTML element or function that returns one.<br>The `container` will have the portal children appended to it.<br>You can also provide a callback, which is called in a React layout effect.<br>This lets you set the container from a ref, and also makes server-side rendering possible.<br>By default, it uses the body of the top-level document object,<br>so it's simply `document.body` most of the time. |
| **disableAutoFocus** | ``false ⏐ true`` | `false` | If `true`, the modal will not automatically shift focus to itself when it opens, and<br>replace it to the last focused element when it closes.<br>This also works correctly with any modal children that have the `disableAutoFocus` prop.<br>Generally this should never be set to `true` as it makes the modal less<br>accessible to assistive technologies, like screen readers. |
| **disableEnforceFocus** | ``false ⏐ true`` | `false` | If `true`, the modal will not prevent focus from leaving the modal while open.<br>Generally this should never be set to `true` as it makes the modal less<br>accessible to assistive technologies, like screen readers. |
| **disableEscapeKeyDown** | ``false ⏐ true`` | `false` | If `true`, hitting escape will not fire the `onClose` callback. |
| **disablePortal** | ``false ⏐ true`` | `false` | The `children` will be under the DOM hierarchy of the parent component. |
| **disableRestoreFocus** | ``false ⏐ true`` | `false` | If `true`, the modal will not restore focus to previously focused element once<br>modal is hidden or unmounted. |
| **disableScrollLock** | ``false ⏐ true`` | `false` | Disable the scroll lock behavior. |
| **hideBackdrop** | ``false ⏐ true`` | `false` | If `true`, the backdrop is not rendered. |
| **onBackdropClick** | ``ReactEventHandler<object>`` | - | ⚠️ **Deprecated**: Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.<br><br>Callback fired when the backdrop is clicked. |
| **onClose** | ``(event: SyntheticEvent<Element, Event>) => void`` | - | Callback fired when the component requests to be closed.<br>The `reason` parameter can optionally be used to control the response to `onClose`.<br>@param event The event source of the callback. |
| **onTransitionEnter** | ``() => void`` | - | A function called when a transition enters. |
| **onTransitionExited** | ``() => void`` | - | A function called when a transition has exited. |
| **slotProps** | ``{ root?: SlotComponentProps<"div", ModalRootSlotPropsOverrides, { children: ReactElement<any, string ⏐ JSXElementConstructor<any>>; ... 17 more ...; exited: boolean; }> ⏐ undefined; backdrop?: SlotComponentProps<...> ⏐ undefined; } & { ...; }`` | `{} {}` | The props used for each slot inside the Modal.<br>The props used for each slot inside. |
| **slots** | `ModalSlots & SideSheetSlots` | `{} {}` | The components used for each slot inside the Modal.<br>Either a string to use a HTML element or a component.<br>The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **width** | ``string ⏐ number`` | `300` | Width of the side sheet. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `SideSheet.Root` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaSideSheet-root | `div` | The component that renders the root of the sideSheet. |
| container | .NovaSideSheet-container | `'div'` | The component that renders the content of the sideSheet. |

## CSS classes

CSS classes for different states and variations of the `SideSheet.Root` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaSideSheet-anchorLeft | `anchorLeft` | Class name applied to the root element when anchored to the left. |
| .NovaSideSheet-anchorRight | `anchorRight` | Class name applied to the root element when anchored to the right. |
| .Nova-open | `open` | Class name applied to the root element when open. |

