import React, { useState } from 'react';
import { SideSheet } from '@hxnova/react-components/SideSheet';
import { Button } from '@hxnova/react-components/Button';
import { Typography } from '@hxnova/react-components/Typography';
import Icon from '@hxnova/icons/Icon';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Divider } from '@hxnova/react-components/Divider';

export default function AnchorExample() {
  const [openLeft, setOpenLeft] = useState(false);
  const [openRight, setOpenRight] = useState(false);

  const handleCloseLeft = () => {
    setOpenLeft(false);
  };

  const handleCloseRight = () => {
    setOpenRight(false);
  };

  return (
    <div sx={{ display: 'flex', justifyContent: 'space-between', gap: '16px' }}>
      <Button onClick={() => setOpenLeft(true)}>Open Left Side Sheet</Button>
      <Button onClick={() => setOpenRight(true)}>Open Right Side Sheet</Button>

      {/* Left Anchored Side Sheet */}
      <SideSheet.Root open={openLeft} onClose={handleCloseLeft} anchor="left" width={360}>
        <SideSheet.Header sx={{ flexDirection: 'row-reverse' }}>
          <IconButton variant="neutral" onClick={handleCloseLeft} sx={{ width: 'fit-content' }}>
            <Icon family="material" name="arrow_back" size={24} sx={{ transform: 'rotate(180deg)' }} />
          </IconButton>
          <Typography variant="titleLarge" sx={{ fontWeight: 'normal' }}>
            Left Side Sheet
          </Typography>
          <IconButton variant="neutral" onClick={handleCloseLeft} sx={{ marginRight: 'auto', marginLeft: 'unset' }}>
            <Icon family="material" name="close" size={24} />
          </IconButton>
        </SideSheet.Header>
        <SideSheet.Content>
          <Typography>This side sheet appears from the left side of the screen.</Typography>
        </SideSheet.Content>
        <Divider variant="inset" />
        <SideSheet.Footer>
          <Button variant="outlined" onClick={handleCloseLeft}>
            Cancel
          </Button>
          <Button onClick={handleCloseLeft}>Confirm</Button>
        </SideSheet.Footer>
      </SideSheet.Root>
      {/* Right Anchored Side Sheet */}
      <SideSheet.Root open={openRight} onClose={handleCloseRight} anchor="right" width={360}>
        <SideSheet.Header>
          <IconButton variant="neutral" onClick={handleCloseRight} sx={{ width: 'fit-content' }}>
            <Icon family="material" name="arrow_back" size={24} />
          </IconButton>
          <Typography variant="titleLarge" sx={{ fontWeight: 'normal' }}>
            Right Side Sheet
          </Typography>
          <IconButton variant="neutral" onClick={handleCloseRight}>
            <Icon family="material" name="close" size={24} />
          </IconButton>
        </SideSheet.Header>
        <SideSheet.Content>
          <Typography>This side sheet appears from the right side of the screen.</Typography>
        </SideSheet.Content>
        <Divider variant="inset" />
        <SideSheet.Footer>
          <Button variant="outlined" onClick={handleCloseRight}>
            Cancel
          </Button>
          <Button onClick={handleCloseRight}>Confirm</Button>
        </SideSheet.Footer>
      </SideSheet.Root>
    </div>
  );
}
