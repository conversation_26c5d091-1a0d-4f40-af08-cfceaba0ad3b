import React from 'react';
import { Avatar } from '@hxnova/react-components/Avatar';
import { List } from '@hxnova/react-components/List';
import { ListDivider } from '@hxnova/react-components/ListDivider';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Typography } from '@hxnova/react-components/Typography';

export default function DividedList() {
  return (
    <div sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: '16px' }}>
      {([undefined, 'gutter', 'startDecorator', 'startContent'] as const).map((variant) => (
        <div key={variant || 'default'}>
          <Typography variant="bodySmall" sx={{ marginBottom: '16px' }}>
            <code>{variant ? `variant="${variant}"` : '(default)'}</code>
          </Typography>
          <List
            sx={{
              minWidth: 240,
              borderRadius: '8px',
              border: `1px solid var(--palette-outlineVariant)`,
            }}
          >
            <ListItem>
              <ListItemDecorator>
                <Avatar size="small" />
              </ListItemDecorator>
              Mabel Boyle
            </ListItem>
            <ListDivider variant={variant} />
            <ListItem>
              <ListItemDecorator>
                <Avatar size="small" />
              </ListItemDecorator>
              Boyd Burt
            </ListItem>
          </List>
        </div>
      ))}
    </div>
  );
}
