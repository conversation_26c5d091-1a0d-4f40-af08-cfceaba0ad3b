import React from 'react';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import Icon from '@hxnova/icons/Icon';

export default function ActionableList() {
  return (
    <List sx={{ maxWidth: 320 }}>
      <ListItem>
        <ListItemButton onClick={() => alert('You clicked')}>
          <ListItemDecorator>
            <Icon family="material" name="info" size={24} />
          </ListItemDecorator>
          Clickable item
        </ListItemButton>
      </ListItem>
      <ListItem>
        <ListItemButton component="a" href="#actionable">
          <ListItemDecorator>
            <Icon family="material" name="open_in_new" size={24} />
          </ListItemDecorator>
          Open a new tab
        </ListItemButton>
      </ListItem>
    </List>
  );
}
