import React from 'react';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import Icon from '@hxnova/icons/Icon';

export default function BasicList() {
  return (
    <List sx={{ width: '100%', maxWidth: 360 }}>
      <ListItem>
        <ListItemButton>
          <ListItemDecorator>
            <Icon family="material" name="home" size={24} />
          </ListItemDecorator>
          <ListItemContent primary="Home" />
          <Icon family="material" name="keyboard_arrow_right" size={24} />
        </ListItemButton>
      </ListItem>
    </List>
  );
}
