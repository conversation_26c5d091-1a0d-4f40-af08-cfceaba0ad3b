import React from 'react';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListSubheader } from '@hxnova/react-components/ListSubheader';

export default function NestedList() {
  return (
    <List sx={{ width: 200, borderRadius: '4px' }}>
      <ListItem nested>
        <ListSubheader>Category 1</ListSubheader>
        <List>
          <ListItem>
            <ListItemButton>Subitem 1</ListItemButton>
          </ListItem>
          <ListItem>
            <ListItemButton>Subitem 2</ListItemButton>
          </ListItem>
        </List>
      </ListItem>
      <ListItem nested>
        <ListSubheader>Category 2</ListSubheader>
        <List>
          <ListItem>
            <ListItemButton>Subitem 1</ListItemButton>
          </ListItem>
          <ListItem>
            <ListItemButton>Subitem 2</ListItemButton>
          </ListItem>
        </List>
      </ListItem>
    </List>
  );
}
