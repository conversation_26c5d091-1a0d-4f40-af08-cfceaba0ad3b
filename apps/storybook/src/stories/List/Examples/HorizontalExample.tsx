import React from 'react';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListDivider } from '@hxnova/react-components/ListDivider';
import Icon from '@hxnova/icons/Icon';

export default function HorizontalList() {
  return (
    <div aria-label="My site" sx={{ flexGrow: 1, maxWidth: 200 }}>
      <List role="menubar" orientation="horizontal">
        <ListItem>
          <ListItemButton role="menuitem" component="a" href="#horizontal-list" aria-label="Home">
            <Icon family="material" name="home" size={24} />
          </ListItemButton>
        </ListItem>
        <ListDivider />
        <ListItem>
          <ListItemButton role="menuitem" component="a" href="#horizontal-list">
            Products
          </ListItemButton>
        </ListItem>
        <ListDivider />
        <ListItem>
          <ListItemButton role="menuitem" component="a" href="#horizontal-list">
            Blog
          </ListItemButton>
        </ListItem>
        <ListDivider />
        <ListItem>
          <ListItemButton role="menuitem" component="a" href="#horizontal-list" aria-label="Profile">
            <Icon family="material" name="person" size={24} />
          </ListItemButton>
        </ListItem>
      </List>
    </div>
  );
}
