# API Documentation

- [Breadcrumbs](#breadcrumbs)

# Breadcrumbs

API reference docs for the React Breadcrumbs component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Breadcrumbs` component, you can choose to import it directly or through the main entry point.

```jsx
import { Breadcrumbs } from '@hxnova/react-components/Breadcrumbs';
// or
import { Breadcrumbs } from '@hxnova/react-components';
```

## Props

The properties available for the `Breadcrumbs` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **separator** | `ReactNode` | `'/'` | Custom separator node. |
| **slotProps** | ``{ root?: SlotProps<"nav", object, BreadcrumbsOwnerState> ⏐ undefined; ol?: SlotProps<"ol", object, BreadcrumbsOwnerState> ⏐ undefined; li?: SlotProps<...> ⏐ undefined; separator?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `BreadcrumbsSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Breadcrumbs` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaBreadcrumbs-root | `'nav'` | The component that renders the root. |
| ol | .NovaBreadcrumbs-ol | `'ol'` | The component that renders the ol. |
| li | .NovaBreadcrumbs-li | `'li'` | The component that renders the li. |
| separator | .NovaBreadcrumbs-separator | `'li'` | The component that renders the separator. |

