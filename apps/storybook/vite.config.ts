import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { pigment, extendTheme } from '@pigment-css/vite-plugin';
import { NovaTheme } from '@hxnova/themes';
import rawSourcePlugin from './rawSourcePlugin';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    rawSourcePlugin(),
    pigment({
      theme: extendTheme(NovaTheme),
      transformLibraries: ['@hxnova/react-components'],
      babelOptions: {
        plugins: ['@babel/plugin-transform-export-namespace-from'],
      },
      css: {
        defaultDirection: 'ltr',
        generateForBothDir: true,
      },
    }),
    react({
      babel: {
        plugins: ['@babel/plugin-transform-export-namespace-from'],
      },
    }),
  ],
  optimizeDeps: {
    include: ['react-is', '@mui/utils', '@base-ui-components/react'],
    exclude: ['@hxnova/themes', '@hxnova/react-components'],
  },
});
