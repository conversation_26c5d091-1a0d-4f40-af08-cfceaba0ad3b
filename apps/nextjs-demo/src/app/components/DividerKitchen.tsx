import { Divider } from '@hxnova/react-components/Divider';

export default function FabKitchen() {
  return (
    <div sx={{ margin: '20px', display: 'flex' }}>
      <div>
        <div
          sx={{
            fontSize: '14px',
            whiteSpace: 'pre-wrap',
          }}
        >
          {`vertical Divider`}
        </div>
        <div
          sx={{
            display: 'flex',
            width: '300px',
            height: '300px',
          }}
        >
          <Divider orientation="vertical" />
        </div>
      </div>

      <div>
        <div
          sx={{
            fontSize: '14px',
            whiteSpace: 'pre-wrap',
          }}
        >
          <div
            sx={{
              display: 'flex',
              width: '300px',
              height: '300px',
            }}
          >
            {`horizontal Divider`}
          </div>
          <Divider orientation="horizontal" />
        </div>
      </div>
    </div>
  );
}
