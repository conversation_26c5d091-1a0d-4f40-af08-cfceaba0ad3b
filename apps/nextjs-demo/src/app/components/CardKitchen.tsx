import React from 'react';
import { Typography } from '@hxnova/react-components/Typography';
import { Card } from '@hxnova/react-components/Card';
import { Avatar } from '@hxnova/react-components/Avatar';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Button } from '@hxnova/react-components/Button';
import { Tag } from '@hxnova/react-components/Tag';
import { Checkbox } from '@hxnova/react-components/Checkbox';
import Icon from '@hxnova/icons/Icon';

const avatarFirst = '/images/avatar_first.png';
const avatarSecond = '/images/avatar_second.png';
const avatarThird = '/images/avatar_third.png';
const CardCover = 'https://cdn.sanity.io/images/eqlh3dcx/dev/c2ab4812a5485ba4a15aa8f4517b94edb5728c1d-1142x643.png';

const tags = (
  <div sx={{ display: 'flex', gap: '8px' }}>
    <Tag label="Tag" variant="success" intensity="subtle" />
    <Tag label="Tag" variant="error" intensity="subtle" />
    <Tag label="Tag" variant="info" intensity="subtle" />
  </div>
);

const avatars = (
  <div sx={{ display: 'flex', gap: '8px' }}>
    <Avatar src={avatarFirst} />
    <Avatar src={avatarSecond} />
    <Avatar src={avatarThird} />
  </div>
);

const moreAction = (
  <IconButton variant="neutral" aria-label="settings">
    <Icon family="material" name="more_vert" size={24} />
  </IconButton>
);

export default function CardKitchen() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Typography variant="headlineLarge" sx={{ color: 'var(--palette-onSurface)' }}>
        Stacked Card
      </Typography>

      <Card.Root sx={{ width: '360px' }}>
        <Card.Content title={'HxAuth Prod'} supportingText={'Yesterday at 11:41 AM'} />
        <Card.Actions>
          <Button variant="outlined" size="small">
            Cancel request
          </Button>
        </Card.Actions>
      </Card.Root>

      <Card.Root sx={{ width: '360px' }}>
        <Card.Header
          avatar={<Avatar src={avatarFirst} />}
          action={
            <IconButton variant="neutral">
              <Icon family="material" name="star" size={24} />
            </IconButton>
          }
          heading={'Daniela Maas'}
          subheading={'Yesterday'}
        />
        <Card.Content
          title={'Bug reported'}
          supportingText={'I found a bug where the image stretches when inspect mode is turned on'}
        />
      </Card.Root>

      <Card.Root sx={{ maxWidth: '360px' }}>
        <Card.Media component="img" image={CardCover} />
        <Card.Content title={'Designing Great Software'} supportingText={'120MB'} action={moreAction} />
      </Card.Root>

      <Card.Root sx={{ maxWidth: '360px' }}>
        <Card.Media component="img" image={CardCover} />
        <Card.Content
          startDecorator={tags}
          title={'Designing Great Software'}
          subtitle={'10 useful tips to consider when designing a new application'}
          supportingText={'1 day ago'}
        />
        <Card.Actions>
          <Button endIcon={<Icon family="material" name="arrow_forward" size={24} />}>Read more</Button>
        </Card.Actions>
      </Card.Root>

      <Card.Root sx={{ width: '360px' }}>
        <Card.Header
          avatar={<Avatar color={'error'}>AA</Avatar>}
          action={
            <div sx={{ display: 'flex', gap: '4px' }}>
              {moreAction}
              <Checkbox />
            </div>
          }
          heading={'Heading'}
          subheading={'Subheading'}
        />
        <Card.Media component="img" image={CardCover} sx={{ height: '156px' }} />
        <Card.Content
          startDecorator={tags}
          title={'Title'}
          subtitle={'Subtitle'}
          supportingText={'Supporting text'}
          action={moreAction}
          endDecorator={avatars}
        />
        <Card.Actions>
          <Button variant="text">Button</Button>
          <Button>Button</Button>
        </Card.Actions>
      </Card.Root>

      <Typography variant="headlineLarge" sx={{ color: 'var(--palette-onSurface)' }}>
        Horizontal Card
      </Typography>

      <Card.Root orientation="horizontal" sx={{ width: '400px' }}>
        <Card.Content title={'Project name'} supportingText={'10MB'} />
        <Card.Actions>{moreAction}</Card.Actions>
      </Card.Root>

      <Card.Root orientation="horizontal" sx={{ width: '400px', height: '80px' }}>
        <Card.Media sx={{ width: '100px', height: '100%' }} component="img" image={CardCover} />
        <Card.Content title={'Project name'} supportingText={'10MB'} />
        <Card.Actions>
          <Checkbox />
        </Card.Actions>
      </Card.Root>

      <Card.Root orientation="horizontal" sx={{ width: '400px' }}>
        <Card.Content startDecorator={<Avatar>AA</Avatar>} title={'Title'} supportingText={'Supporting text'} />
        <Card.Actions>
          <Button variant="text">Cancel request</Button>
        </Card.Actions>
      </Card.Root>

      <Card.Root orientation="horizontal" sx={{ height: '96px' }}>
        <Card.Media sx={{ width: '100px', height: '100%' }} component="img" image={CardCover} />
        <Card.Content
          title={'Block engine'}
          subtitle={'Additive manufacturing'}
          supportingText={'Created 3rd November'}
          endDecorator={tags}
        />
        <Card.Actions>
          <IconButton variant="standard" aria-label="settings">
            <Icon family="material" name="group" size={24} />
          </IconButton>
          <IconButton variant="standard" aria-label="settings">
            <Icon family="material" name="ios_share" size={24} />
          </IconButton>
        </Card.Actions>
      </Card.Root>

      <Card.Root sx={{ height: '96px' }} orientation="horizontal">
        <Card.Media component="img" sx={{ height: '100%', maxWidth: '120px' }} image={CardCover} />
        <Card.Content
          startDecorator={<Avatar color={'error'}>AA</Avatar>}
          title={'Title'}
          subtitle={'Subtitle'}
          supportingText={'Supporting text'}
          endDecorator={
            <div sx={{ display: 'flex', gap: '28px', alignItems: 'center' }}>
              {tags}
              {avatars}
            </div>
          }
        />
        <Card.Actions>
          <Button variant="text">Button</Button>
          <Button>Button</Button>
          {moreAction}
          <Checkbox />
        </Card.Actions>
      </Card.Root>

      <Typography variant="headlineLarge" sx={{ color: 'var(--palette-onSurface)' }}>
        Clickable Card
      </Typography>
      <Card.Root
        sx={{ width: '360px' }}
        onClick={() => {
          console.log('Card click');
        }}
      >
        <Card.Content
          title={'Title'}
          subtitle={'Subtitle'}
          supportingText={'Supporting text'}
          action={<Checkbox />}
          endDecorator={avatars}
        />
      </Card.Root>

      <Typography variant="headlineLarge" sx={{ color: 'var(--palette-onSurface)' }}>
        Disabled Card
      </Typography>
      <Card.Root sx={{ width: '360px' }} disabled>
        <Card.Content
          title={'Title'}
          subtitle={'Subtitle'}
          supportingText={'Supporting text'}
          action={<Checkbox disabled />}
          endDecorator={
            <div sx={{ display: 'flex', gap: '8px' }}>
              <Avatar disabled src={avatarFirst} />
              <Avatar disabled src={avatarSecond} />
              <Avatar disabled src={avatarThird} />
            </div>
          }
        />
      </Card.Root>
    </div>
  );
}
