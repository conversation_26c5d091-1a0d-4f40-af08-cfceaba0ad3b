import React, { useState } from 'react';
import { SideSheet } from '@hxnova/react-components/SideSheet';
import { Button } from '@hxnova/react-components/Button';
import { Typography } from '@hxnova/react-components/Typography';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Divider } from '@hxnova/react-components/Divider';
import Icon from '@hxnova/icons/Icon';
import { Card } from '@hxnova/react-components/Card';

export default function SideSheetKitchen() {
  // State for basic side sheet
  const [openBasic, setOpenBasic] = useState(false);

  // State for side sheets with different anchors
  const [openLeft, setOpenLeft] = useState(false);
  const [openRight, setOpenRight] = useState(false);

  // State for different width side sheets
  const [openNarrow, setOpenNarrow] = useState(false);
  const [openWide, setOpenWide] = useState(false);

  // Close handlers
  const handleCloseBasic = () => setOpenBasic(false);
  const handleCloseLeft = () => setOpenLeft(false);
  const handleCloseRight = () => setOpenRight(false);
  const handleCloseNarrow = () => setOpenNarrow(false);
  const handleCloseWide = () => setOpenWide(false);

  return (
    <div>
      <Typography variant="headlineLarge" sx={{ marginBottom: '24px' }}>
        Side Sheet Kitchen
      </Typography>

      <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        {/* Basic Side Sheet Example */}
        <Card.Root>
          <Card.Content>
            <Typography variant="titleLarge" sx={{ marginBottom: '8px' }}>
              Basic Side Sheet
            </Typography>
            <Typography sx={{ marginBottom: '16px' }}>Standard side sheet with default settings.</Typography>
            <Button onClick={() => setOpenBasic(true)}>Open Basic Side Sheet</Button>
          </Card.Content>
        </Card.Root>

        {/* Anchor Example */}
        <Card.Root>
          <Card.Content>
            <Typography variant="titleLarge" sx={{ marginBottom: '8px' }}>
              Anchor Variations
            </Typography>
            <Typography sx={{ marginBottom: '16px' }}>
              Side sheets can appear from either the left or right side of the screen.
            </Typography>
            <div sx={{ display: 'flex', gap: '16px' }}>
              <Button onClick={() => setOpenLeft(true)}>Open Left Side Sheet</Button>
              <Button onClick={() => setOpenRight(true)}>Open Right Side Sheet</Button>
            </div>
          </Card.Content>
        </Card.Root>

        {/* Width Example */}
        <Card.Root>
          <Card.Content>
            <Typography variant="titleLarge" sx={{ marginBottom: '8px' }}>
              Width Variations
            </Typography>
            <Typography sx={{ marginBottom: '16px' }}>
              Side sheets can have different widths to accommodate various content needs.
            </Typography>
            <div sx={{ display: 'flex', gap: '16px' }}>
              <Button onClick={() => setOpenNarrow(true)}>Open Narrow Side Sheet (240px)</Button>
              <Button onClick={() => setOpenWide(true)}>Open Wide Side Sheet (480px)</Button>
            </div>
          </Card.Content>
        </Card.Root>
      </div>

      {/* Basic Side Sheet */}
      <SideSheet.Root open={openBasic} onClose={handleCloseBasic} anchor="right" width={360}>
        <SideSheet.Header>
          <IconButton variant="neutral" sx={{ width: 'fit-content' }} onClick={handleCloseBasic}>
            <Icon family="material" name="arrow_back" size={24} />
          </IconButton>
          <Typography variant="titleLarge" sx={{ fontWeight: 'normal' }}>
            Basic Side Sheet
          </Typography>
          <IconButton variant="neutral" onClick={handleCloseBasic}>
            <Icon family="material" name="close" size={24} />
          </IconButton>
        </SideSheet.Header>
        <SideSheet.Content>
          <Typography>
            This is a basic side sheet that appears from the right side of the screen. It uses the default width of
            360px.
          </Typography>
          <br />
          <Typography>
            Side sheets are modal panels that appear from the edge of the screen to provide supplementary content,
            actions, or functionality.
          </Typography>
          <br />
          <Typography>Click outside the sheet, press Escape, or use the close button to dismiss it.</Typography>
        </SideSheet.Content>
        <Divider variant="inset" />
        <SideSheet.Footer>
          <Button variant="outlined" onClick={handleCloseBasic}>
            Cancel
          </Button>
          <Button onClick={handleCloseBasic}>Confirm</Button>
        </SideSheet.Footer>
      </SideSheet.Root>

      {/* Left Anchor Side Sheet */}
      <SideSheet.Root open={openLeft} onClose={handleCloseLeft} anchor="left" width={360}>
        <SideSheet.Header>
          <IconButton variant="neutral" sx={{ width: 'fit-content' }} onClick={handleCloseLeft}>
            <Icon family="material" name="arrow_back" size={24} />
          </IconButton>
          <Typography variant="titleLarge" sx={{ fontWeight: 'normal' }}>
            Left Side Sheet
          </Typography>
          <IconButton variant="neutral" onClick={handleCloseLeft}>
            <Icon family="material" name="close" size={24} />
          </IconButton>
        </SideSheet.Header>
        <SideSheet.Content>
          <Typography>
            This side sheet appears from the left side of the screen. Left-anchored side sheets are often used for
            navigation or filters.
          </Typography>
        </SideSheet.Content>
        <Divider variant="inset" />
        <SideSheet.Footer>
          <Button variant="outlined" onClick={handleCloseLeft}>
            Cancel
          </Button>
          <Button onClick={handleCloseLeft}>Confirm</Button>
        </SideSheet.Footer>
      </SideSheet.Root>

      {/* Right Anchor Side Sheet */}
      <SideSheet.Root open={openRight} onClose={handleCloseRight} anchor="right" width={360}>
        <SideSheet.Header>
          <IconButton variant="neutral" sx={{ width: 'fit-content' }} onClick={handleCloseRight}>
            <Icon family="material" name="arrow_back" size={24} />
          </IconButton>
          <Typography variant="titleLarge" sx={{ fontWeight: 'normal' }}>
            Right Side Sheet
          </Typography>
          <IconButton variant="neutral" onClick={handleCloseRight}>
            <Icon family="material" name="close" size={24} />
          </IconButton>
        </SideSheet.Header>
        <SideSheet.Content>
          <Typography>
            This side sheet appears from the right side of the screen. Right-anchored side sheets are commonly used for
            details or forms.
          </Typography>
        </SideSheet.Content>
        <Divider variant="inset" />
        <SideSheet.Footer>
          <Button variant="outlined" onClick={handleCloseRight}>
            Cancel
          </Button>
          <Button onClick={handleCloseRight}>Confirm</Button>
        </SideSheet.Footer>
      </SideSheet.Root>

      {/* Narrow Side Sheet */}
      <SideSheet.Root open={openNarrow} onClose={handleCloseNarrow} anchor="right" width={240}>
        <SideSheet.Header>
          <IconButton variant="neutral" sx={{ width: 'fit-content' }} onClick={handleCloseNarrow}>
            <Icon family="material" name="arrow_back" size={24} />
          </IconButton>
          <Typography variant="titleLarge" sx={{ fontWeight: 'normal' }}>
            Narrow Sheet
          </Typography>
          <IconButton variant="neutral" onClick={handleCloseNarrow}>
            <Icon family="material" name="close" size={24} />
          </IconButton>
        </SideSheet.Header>
        <SideSheet.Content>
          <Typography>
            This is a narrow side sheet with a width of 240px, suitable for simpler content or mobile views.
          </Typography>
        </SideSheet.Content>
        <Divider variant="inset" />
        <SideSheet.Footer>
          <Button variant="outlined" onClick={handleCloseNarrow}>
            Cancel
          </Button>
          <Button onClick={handleCloseNarrow}>Confirm</Button>
        </SideSheet.Footer>
      </SideSheet.Root>

      {/* Wide Side Sheet */}
      <SideSheet.Root open={openWide} onClose={handleCloseWide} anchor="right" width={480}>
        <SideSheet.Header>
          <IconButton variant="neutral" sx={{ width: 'fit-content' }} onClick={handleCloseWide}>
            <Icon family="material" name="arrow_back" size={24} />
          </IconButton>
          <Typography variant="titleLarge" sx={{ fontWeight: 'normal' }}>
            Wide Side Sheet
          </Typography>
          <IconButton variant="neutral" onClick={handleCloseWide}>
            <Icon family="material" name="close" size={24} />
          </IconButton>
        </SideSheet.Header>
        <SideSheet.Content>
          <Typography>
            This is a wide side sheet with a width of 480px, providing more space for complex content or detailed forms.
          </Typography>
          <br />
          <Typography>
            Wider side sheets can accommodate more information and complex UI elements, making them suitable for
            detailed information displays, complex forms, or rich media content.
          </Typography>
          <br />
          <Typography>
            Consider the screen size when using wider side sheets to ensure they don&apos;t take up too much of the
            screen on smaller devices.
          </Typography>
        </SideSheet.Content>
        <Divider variant="inset" />
        <SideSheet.Footer>
          <Button variant="outlined" onClick={handleCloseWide}>
            Cancel
          </Button>
          <Button onClick={handleCloseWide}>Confirm</Button>
        </SideSheet.Footer>
      </SideSheet.Root>
    </div>
  );
}
