import { useState } from 'react';
import { Step, stepClasses } from '@hxnova/react-components/Step';
import { Stepper } from '@hxnova/react-components/Stepper';
import { StepButton } from '@hxnova/react-components/StepButton';
import Icon from '@hxnova/icons/Icon';
import { Typography } from '@hxnova/react-components/Typography';
import { Button } from '@hxnova/react-components/Button';

const steps = ['Order placed', 'In review', 'Approved'];
export default function StepperKitchen() {
  const [activeStep, setActiveStep] = useState(0);

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleFinish = () => {
    setActiveStep(steps.length);
  };

  const isFinished = activeStep === steps.length;

  return (
    <div sx={{ padding: '24px' }}>
      <div sx={{ width: '815px', display: 'flex', flexDirection: 'column', gap: '48px' }}>
        {/* Basic Stepper */}
        <div>
          <h3>Basic Stepper</h3>
          <div sx={{ width: '100%', margin: '20px auto' }}>
            <Typography variant="titleSmall" sx={{ marginBottom: '8px' }}>
              Label Left
            </Typography>
            <Stepper sx={{ width: '100%' }}>
              <Step completed icon={<Icon family="material" name="check" size={24} />}>
                <Typography variant="bodyMedium" sx={{ width: 70, height: 48 }}>
                  Step title optional
                </Typography>
              </Step>
              <Step active icon={'2'}>
                <Typography variant="bodyMedium" sx={{ width: 70, height: 48 }}>
                  Step title optional
                </Typography>
              </Step>
              <Step icon={'3'}>
                <Typography variant="bodyMedium" sx={{ width: 70, height: 48 }}>
                  Step title optional
                </Typography>
              </Step>
            </Stepper>
          </div>
          <div sx={{ width: '100%' }}>
            <Typography variant="titleSmall" sx={{ marginBottom: '8px' }}>
              Label Center
            </Typography>
            <Stepper sx={{ width: '100%' }}>
              <Step completed icon={<Icon family="material" name="check" size={24} />}>
                <Typography variant="bodyMedium" sx={{ width: 70, height: 48, textAlign: 'center' }}>
                  Step title optional
                </Typography>
              </Step>
              <Step active icon={'2'}>
                <Typography variant="bodyMedium" sx={{ width: 70, height: 48, textAlign: 'center' }}>
                  Step title optional
                </Typography>
              </Step>
              <Step icon={'3'}>
                <Typography variant="bodyMedium" sx={{ width: 70, height: 48, textAlign: 'center' }}>
                  Step title optional
                </Typography>
              </Step>
            </Stepper>
          </div>
        </div>

        {/* Button */}
        <div>
          <h3>Button</h3>
          <div sx={{ width: '100%' }}>
            <Stepper sx={{ width: '100%' }}>
              {steps.map((step, index) => (
                <Step
                  key={step}
                  active={activeStep === index}
                  completed={activeStep > index}
                  icon={activeStep > index ? <Icon family="material" name="check" size={24} /> : index + 1}
                  sx={
                    activeStep > index && index !== 2
                      ? ({ '--nova-step-connectorBg': 'var(--palette-primary)' } as any)
                      : undefined
                  }
                >
                  <StepButton onClick={() => setActiveStep(index)}>{step}</StepButton>
                </Step>
              ))}
            </Stepper>
          </div>
        </div>

        {/* Basic Vertical Steppers */}
        <div>
          <h3>Basic Vertical Steppers</h3>
          <div sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
            <Stepper orientation="vertical" sx={{ width: 200 }}>
              <Step completed icon={<Icon family="material" name="check" size={24} />}>
                Order placed
              </Step>
              <Step active icon={'2'}>
                In review
              </Step>
              <Step icon={'3'}>Approved</Step>
            </Stepper>
          </div>
        </div>

        {/* Vertical complex */}
        <div>
          <h3>Vertical complex</h3>
          <div sx={{ width: '100%', padding: '24px' }}>
            <Stepper
              orientation="vertical"
              sx={
                {
                  '& .NovaStep-root': { alignItems: 'flex-start' },
                  '& .NovaStepIndicator-root': { marginTop: '4px' },
                  '& .NovaStep-indicator': { alignSelf: 'flex-start' },
                } as any
              }
            >
              {steps.map((label, index) => (
                <Step
                  key={label + index}
                  active={!isFinished && index === activeStep}
                  completed={isFinished || index < activeStep}
                  icon={
                    isFinished || index < activeStep ? <Icon family="material" name="check" size={24} /> : index + 1
                  }
                  sx={{ '&::after': { height: '20px !important' } } as any}
                >
                  <div
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '16px',
                      paddingTop: '2px',
                    }}
                  >
                    <Typography variant="bodyMedium">{label}</Typography>
                    {!isFinished && activeStep === index && (
                      <>
                        <Typography variant="bodyMedium" color="textSecondary" sx={{ maxWidth: '350px' }}>
                          Step {index + 1} content goes here. This is the detailed information for this step.
                        </Typography>
                        <div sx={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
                          {index === steps.length - 1 ? (
                            <Button size="small" variant="filled" onClick={handleFinish}>
                              Finish
                            </Button>
                          ) : (
                            <Button size="small" variant="filled" onClick={handleNext}>
                              Continue
                            </Button>
                          )}
                          <Button size="small" variant="text" onClick={handleBack} disabled={index === 0}>
                            Back
                          </Button>
                        </div>
                      </>
                    )}
                  </div>
                </Step>
              ))}
            </Stepper>
          </div>

          {/* Custom Icons */}
          <div>
            <h3>Custom Icons</h3>
            <div sx={{ width: '100%' }}>
              <Stepper
                sx={
                  {
                    width: '100%',
                    '--nova-stepIndicator-size': '3rem',
                    '--nova-step-connectorInset': '0px',
                    [`& .${stepClasses.indicator}`]: {
                      borderWidth: 4,
                    },
                    [`& .${stepClasses.root}::after`]: {
                      height: 4,
                    },
                    [`& .${stepClasses.completed}`]: {
                      [`& .${stepClasses.indicator}`]: {
                        borderColor: 'primary.300',
                        color: 'primary.300',
                      },
                      '&::after': {
                        bgcolor: 'primary.300',
                      },
                    },
                    [`& .${stepClasses.active}`]: {
                      [`& .${stepClasses.indicator}`]: {
                        borderColor: 'currentColor',
                      },
                    },
                    [`& .${stepClasses.disabled} *`]: {
                      color: 'neutral.outlinedDisabledColor',
                    },
                  } as any
                }
              >
                <Step completed icon={<Icon family="material" name="shopping_cart" size={24} />} />
                <Step completed icon={<Icon family="material" name="contacts" size={24} />} />
                <Step completed icon={<Icon family="material" name="local_shipping" size={24} />} />
                <Step active icon={<Icon family="material" name="credit_card" size={24} />}></Step>
                <Step disabled icon={<Icon family="material" name="check_circle" size={24} />} />
              </Stepper>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
