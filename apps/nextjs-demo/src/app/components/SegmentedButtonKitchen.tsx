import * as React from 'react';
import { SegmentedButtonGroup, SegmentedButton, Typography } from '@hxnova/react-components';
import { Icon } from '@hxnova/icons';

export default function SegmentedButtonGroupKitchen() {
  const [value, setValue] = React.useState('1');
  const handleChange = (_event: React.ChangeEvent<unknown>, newValue: string) => {
    setValue(newValue);
  };

  const [values, setValues] = React.useState(['1', '2']);
  const handleChanges = (_event: React.ChangeEvent<unknown>, newValues: string[]) => {
    setValues(newValues);
  };

  return (
    <div sx={{ margin: '20px', display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <div>
        <Typography
          variant={'labelSmall'}
          sx={(theme) => ({ display: 'block', marginBottom: '12px', color: theme.vars.palette.onSurface })}
        >
          text only
        </Typography>
        <div>
          <SegmentedButtonGroup exclusive value={value} onChange={handleChange}>
            <SegmentedButton value="1" startIcon={value === '1' && <Icon family="material" name="check" />}>
              Button 1
            </SegmentedButton>
            <SegmentedButton value="2" startIcon={value === '2' && <Icon family="material" name="check" />}>
              Button 2
            </SegmentedButton>
            <SegmentedButton value="3" startIcon={value === '3' && <Icon family="material" name="check" />}>
              Button 3
            </SegmentedButton>
          </SegmentedButtonGroup>
        </div>
      </div>
      <div>
        <Typography
          variant={'labelSmall'}
          sx={(theme) => ({ display: 'block', marginBottom: '12px', color: theme.vars.palette.onSurface })}
        >
          text and icon
        </Typography>
        <div>
          <SegmentedButtonGroup exclusive value={value} onChange={handleChange}>
            <SegmentedButton value="1" startIcon={<Icon family="material" name="check" />}>
              Button 1
            </SegmentedButton>
            <SegmentedButton value="2" startIcon={<Icon family="material" name="check" />}>
              Button 2
            </SegmentedButton>
            <SegmentedButton value="3" startIcon={<Icon family="material" name="check" />}>
              Button 3
            </SegmentedButton>
          </SegmentedButtonGroup>
        </div>
      </div>
      <div>
        <Typography
          variant={'labelSmall'}
          sx={(theme) => ({ display: 'block', marginBottom: '12px', color: theme.vars.palette.onSurface })}
        >
          Icon only
        </Typography>
        <div>
          <SegmentedButtonGroup exclusive value={value} onChange={handleChange}>
            <SegmentedButton value="1" startIcon={<Icon family="material" name="check" />} />
            <SegmentedButton value="2" startIcon={<Icon family="material" name="check" />} />
            <SegmentedButton value="3" startIcon={<Icon family="material" name="check" />} />
          </SegmentedButtonGroup>
        </div>
      </div>
      <div>
        <Typography
          variant={'labelSmall'}
          sx={(theme) => ({ display: 'block', marginBottom: '12px', color: theme.vars.palette.onSurface })}
        >
          Sizes
        </Typography>
        <div sx={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          <SegmentedButtonGroup size="small" exclusive value={value} onChange={handleChange}>
            <SegmentedButton value="1" startIcon={<Icon family="material" name="check" />}>
              Button 1
            </SegmentedButton>
            <SegmentedButton value="2" startIcon={<Icon family="material" name="check" />}>
              Button 2
            </SegmentedButton>
            <SegmentedButton value="3" startIcon={<Icon family="material" name="check" />}>
              Button 3
            </SegmentedButton>
          </SegmentedButtonGroup>
          <SegmentedButtonGroup size="medium" exclusive value={value} onChange={handleChange}>
            <SegmentedButton value="1" startIcon={<Icon family="material" name="check" />}>
              Button 1
            </SegmentedButton>
            <SegmentedButton value="2" startIcon={<Icon family="material" name="check" />}>
              Button 2
            </SegmentedButton>
            <SegmentedButton value="3" startIcon={<Icon family="material" name="check" />}>
              Button 3
            </SegmentedButton>
          </SegmentedButtonGroup>
          <SegmentedButtonGroup size="large" exclusive value={value} onChange={handleChange}>
            <SegmentedButton value="1" startIcon={<Icon family="material" name="check" />}>
              Button 1
            </SegmentedButton>
            <SegmentedButton value="2" startIcon={<Icon family="material" name="check" />}>
              Button 2
            </SegmentedButton>
            <SegmentedButton value="3" startIcon={<Icon family="material" name="check" />}>
              Button 3
            </SegmentedButton>
          </SegmentedButtonGroup>
        </div>
      </div>
      <div>
        <Typography
          variant={'labelSmall'}
          sx={(theme) => ({ display: 'block', marginBottom: '12px', color: theme.vars.palette.onSurface })}
        >
          Multi-select mode
        </Typography>
        <div>
          <SegmentedButtonGroup value={values} onChange={handleChanges}>
            <SegmentedButton value="1" startIcon={<Icon family="material" name="check" />}>
              Button 1
            </SegmentedButton>
            <SegmentedButton value="2" startIcon={<Icon family="material" name="check" />}>
              Button 2
            </SegmentedButton>
            <SegmentedButton value="3" startIcon={<Icon family="material" name="check" />}>
              Button 3
            </SegmentedButton>
          </SegmentedButtonGroup>
        </div>
      </div>
    </div>
  );
}
