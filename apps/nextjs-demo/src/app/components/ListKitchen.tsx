import { Avatar, AvatarProps } from '@hxnova/react-components/Avatar';
import { Divider } from '@hxnova/react-components/Divider';
import { Typography } from '@hxnova/react-components/Typography';
import { List, ListProps } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import Icon from '@hxnova/icons/Icon';

const densities = ['compact', 'standard', 'comfortable'];
const sizeMap: Record<string, string> = {
  compact: 'small',
  standard: 'medium',
  comfortable: 'large',
};
export default function ListKitchen() {
  return (
    <div>
      <div sx={{ margin: '20px', display: 'flex', flexDirection: 'row', gap: '30px' }}>
        {densities.map((density) => (
          <div key={density}>
            <div
              sx={{
                fontSize: '14px',
                whiteSpace: 'pre-wrap',
              }}
            >
              {`${density} density with text and avatar`}
            </div>
            <div
              sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: '20px',
                padding: '10px',
              }}
            >
              <List density={density as ListProps['density']}>
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                        AA
                      </Avatar>
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem disabled>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                        AA
                      </Avatar>
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                        AA
                      </Avatar>
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                        AA
                      </Avatar>
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
              </List>
            </div>
          </div>
        ))}
      </div>
      <div sx={{ margin: '20px', display: 'flex', flexDirection: 'row', gap: '30px' }}>
        {densities.map((density) => (
          <div key={density}>
            <div
              sx={{
                fontSize: '14px',
                whiteSpace: 'pre-wrap',
              }}
            >
              {`${density} density with primary text and avatar`}
            </div>
            <div
              sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: '20px',
                padding: '10px',
              }}
            >
              <List density={density as ListProps['density']}>
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                        AA
                      </Avatar>
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem disabled>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                        AA
                      </Avatar>
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                        AA
                      </Avatar>
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                        AA
                      </Avatar>
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
              </List>
            </div>
          </div>
        ))}
      </div>
      <div sx={{ margin: '20px', display: 'flex', flexDirection: 'row', gap: '30px' }}>
        {densities.map((density) => (
          <div key={density}>
            <div
              sx={{
                fontSize: '14px',
                whiteSpace: 'pre-wrap',
              }}
            >
              {`${density} density with text and icon`}
            </div>
            <div
              sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: '20px',
                padding: '10px',
              }}
            >
              <List density={density as ListProps['density']}>
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Icon family="material" name="person" size={24} />
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem disabled>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Icon family="material" name="person" size={24} />
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Icon family="material" name="person" size={24} />
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Icon family="material" name="person" size={24} />
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" secondary="Supporting text" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
              </List>
            </div>
          </div>
        ))}
      </div>

      <div sx={{ margin: '20px', display: 'flex', flexDirection: 'row', gap: '30px' }}>
        {densities.map((density) => (
          <div key={density}>
            <div
              sx={{
                fontSize: '14px',
                whiteSpace: 'pre-wrap',
              }}
            >
              {`${density} density with primary text and icon `}
            </div>
            <div
              sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: '20px',
                padding: '10px',
              }}
            >
              <List density={density as ListProps['density']}>
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Icon family="material" name="person" size={24} />
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem disabled>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Icon family="material" name="person" size={24} />
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Icon family="material" name="person" size={24} />
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemButton>
                    <ListItemDecorator>
                      <Icon family="material" name="person" size={24} />
                    </ListItemDecorator>
                    <ListItemContent primary="Headline" />
                    <ListItemDecorator>
                      <Typography variant="bodySmall">100+</Typography>
                      <Icon family="material" name="keyboard_arrow_right" size={24} />
                    </ListItemDecorator>
                  </ListItemButton>
                </ListItem>
                <Divider />
              </List>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
