import { Radio } from '@hxnova/react-components/Radio';
import { RadioGroup } from '@hxnova/react-components/RadioGroup';

export default function RadioKitchen() {
  return (
    <div
      sx={(theme) => ({
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        padding: '8px',
        color: theme.vars.palette.onSurfaceVariant,
      })}
    >
      {/* 1. Basic status display */}
      <div>
        <h3>Basic States</h3>
        <div sx={{ display: 'flex', gap: '16px' }}>
          <Radio label="Default" />
          <Radio label="Default Checked" defaultChecked />
          <Radio label="Disabled" disabled />
          <Radio label="Disabled Checked" disabled defaultChecked />
          <Radio label="ReadOnly" readOnly />
        </div>
      </div>

      {/* 2. Size Variants */}
      <div>
        <h3>Size Variants</h3>
        <Radio label="Small" size="small" />
        <Radio label="Medium" size="medium" />
        <Radio label="Large" size="large" />
      </div>

      {/* 3. Radio Group */}
      <div>
        <h3>Radio Group</h3>
        <div sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <RadioGroup defaultValue="female" name="radio-buttons-group">
            <Radio value="female" label="Female" />
            <Radio value="male" label="Male" />
            <Radio value="other" label="Other" />
          </RadioGroup>
        </div>
      </div>

      {/* 4. Combined Features */}
      <div>
        <h3>Combined Features</h3>
        <div sx={{ display: 'flex', gap: '16px' }}>
          <Radio size="small" defaultChecked label="Small + Checked" />
          <Radio size="medium" name="age" label="Medium + Name" />
          <Radio size="large" disabled label="Large + Disabled" />
        </div>
      </div>
    </div>
  );
}
