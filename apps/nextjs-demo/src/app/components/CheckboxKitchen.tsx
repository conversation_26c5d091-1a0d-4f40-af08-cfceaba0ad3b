import { CheckboxProps, Checkbox } from '@hxnova/react-components/Checkbox';

export default function ButtonKitchen() {
  return (
    <div>
      <div sx={{ display: 'flex', flexDirection: 'column', gap: 20 }}>
        {(['small', 'medium', 'large'] as Array<CheckboxProps['size']>).map((size) => (
          <div key={size} sx={{ display: 'flex', alignItems: 'center' }}>
            <div sx={{ fontWeight: 700, width: '120px' }}>{size}</div>
            <div key={size} sx={{ display: 'flex', flexDirection: 'column' }}>
              {[false, true].map((disabled) => (
                <div key={`${disabled}`} sx={{ display: 'flex' }}>
                  {['unselected', 'selected', 'indeterminate'].map((type) =>
                    (['primary', 'error'] as Array<CheckboxProps['color']>).map((color) => (
                      <div key={`${color}-${type}`}>
                        <Checkbox
                          sx={{ margin: 2 }}
                          defaultChecked={type === 'selected'}
                          indeterminate={type === 'indeterminate'}
                          color={color}
                          disabled={disabled}
                          size={size}
                          label={'Label'}
                        />
                      </div>
                    )),
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
