<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="33" failures="0" errors="0" time="28.248">
    <testsuite name="Slide/Slide.test.tsx" timestamp="2025-06-09T06:39:02.186Z" hostname="X-CN-7VYQNV.local" tests="33" failures="0" errors="0" skipped="0" time="0.049496417">
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; renders children when in is true" time="0.016760708">
            <system-err>
Warning: Received `true` for a non-boolean attribute `appear`.

If you want to write it to the DOM, pass a string instead: appear=&quot;true&quot; or appear={value.toString()}.
    at div
    at Transition (/Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.test.tsx:4:5)
    at /Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.tsx:79:5

            </system-err>
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; renders children when in is false" time="0.001734917">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; does not apply visibility hidden when entered" time="0.002069209">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; direction prop &gt; handles direction=&quot;down&quot; (default)" time="0.001108541">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; direction prop &gt; handles direction=&quot;up&quot;" time="0.00131">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; direction prop &gt; handles direction=&quot;left&quot;" time="0.001168458">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; direction prop &gt; handles direction=&quot;right&quot;" time="0.001081333">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; container prop &gt; handles container as element" time="0.000967125">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; container prop &gt; handles container as function returning element" time="0.001766542">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; transition callbacks &gt; calls onEnter callback when entering" time="0.001531833">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; transition callbacks &gt; calls onEntering callback when entering" time="0.000977542">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; transition callbacks &gt; calls onEntered callback when entered" time="0.00080475">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; transition callbacks &gt; calls onExit callback when exiting" time="0.000802584">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; transition callbacks &gt; calls onExiting callback when exiting" time="0.000682542">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; transition callbacks &gt; calls onExited callback when exited" time="0.00072575">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; timeout prop &gt; handles timeout prop as number" time="0.000884">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; timeout prop &gt; handles timeout prop as object" time="0.000659417">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; easing prop &gt; handles easing prop as string" time="0.000665125">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; easing prop &gt; handles easing prop as object" time="0.00056625">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; renders with appear prop set to false" time="0.000517041">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; forwards ref to the child element" time="0.000568166">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; merges styles with child element styles" time="0.00102125">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; passes through additional props to TransitionComponent" time="0.00232225">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; works with custom TransitionComponent" time="0.000996958">
            <system-err>
Warning: React does not recognize the `nodeRef` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `noderef` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
    at div
    at CustomTransition (/Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.test.tsx:437:33)
    at /Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.tsx:79:5
Warning: Unknown event handler property `onEnter`. It will be ignored.
    at div
    at CustomTransition (/Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.test.tsx:437:33)
    at /Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.tsx:79:5
Warning: Unknown event handler property `onEntered`. It will be ignored.
    at div
    at CustomTransition (/Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.test.tsx:437:33)
    at /Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.tsx:79:5
Warning: Unknown event handler property `onEntering`. It will be ignored.
    at div
    at CustomTransition (/Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.test.tsx:437:33)
    at /Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.tsx:79:5
Warning: Unknown event handler property `onExit`. It will be ignored.
    at div
    at CustomTransition (/Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.test.tsx:437:33)
    at /Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.tsx:79:5
Warning: Unknown event handler property `onExited`. It will be ignored.
    at div
    at CustomTransition (/Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.test.tsx:437:33)
    at /Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.tsx:79:5
Warning: Unknown event handler property `onExiting`. It will be ignored.
    at div
    at CustomTransition (/Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.test.tsx:437:33)
    at /Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.tsx:79:5
Warning: React does not recognize the `addEndListener` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `addendlistener` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
    at div
    at CustomTransition (/Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.test.tsx:437:33)
    at /Users/<USER>/projects/Nova/packages/nova-react-components/Slide/Slide.tsx:79:5

            </system-err>
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; handles addEndListener callback" time="0.000766792">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; preserves child element props" time="0.001000959">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; clones child element correctly" time="0.000764667">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; transform effects &gt; sets transform property during transitions" time="0.000542834">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; transform effects &gt; clears transform on exit" time="0.00048875">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; resize handling &gt; handles window resize for left direction" time="0.000749625">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; resize handling &gt; handles window resize for up direction" time="0.000523125">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; position updates &gt; updates position when direction changes" time="0.001060833">
        </testcase>
        <testcase classname="Slide/Slide.test.tsx" name="Slide &gt; position updates &gt; updates position when container changes" time="0.000736458">
        </testcase>
    </testsuite>
</testsuites>
